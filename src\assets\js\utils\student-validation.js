/**
 * Frontend Student Validation Utilities
 * Provides client-side validation for student forms
 */
class StudentValidationUtil {
  
  /**
   * Validate basic student information
   */
  static validateBasicInfo(formData, studentType) {
    const errors = [];
    
    // Required field validation
    if (!formData.admission_number || formData.admission_number.trim() === '') {
      errors.push('Admission number is required');
    } else if (!/^[A-Z0-9\/\-]+$/i.test(formData.admission_number)) {
      errors.push('Admission number can only contain letters, numbers, hyphens, and forward slashes');
    }
    
    if (!formData.first_name || formData.first_name.trim() === '') {
      errors.push('First name is required');
    } else if (formData.first_name.length < 2) {
      errors.push('First name must be at least 2 characters long');
    }
    
    if (!formData.last_name || formData.last_name.trim() === '') {
      errors.push('Last name is required');
    } else if (formData.last_name.length < 2) {
      errors.push('Last name must be at least 2 characters long');
    }
    
    if (!formData.gender || !['male', 'female'].includes(formData.gender.toLowerCase())) {
      errors.push('Please select a valid gender');
    }
    
    if (!formData.current_class_id) {
      errors.push('Please select a class');
    }
    
    if (!formData.current_academic_year_id) {
      errors.push('Academic year is required');
    }
    
    if (!formData.current_term_id) {
      errors.push('Term is required');
    }
    
    // A-Level specific validation
    if (studentType === 'a_level' && !formData.stream_id) {
      errors.push('Stream selection is required for A-Level students');
    }
    
    // Date of birth validation
    if (formData.date_of_birth) {
      const birthDate = new Date(formData.date_of_birth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (isNaN(birthDate.getTime())) {
        errors.push('Please enter a valid date of birth');
      } else if (birthDate > today) {
        errors.push('Date of birth cannot be in the future');
      } else if (age < 10 || age > 25) {
        errors.push('Student age must be between 10 and 25 years');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
  
  /**
   * Validate O-Level subject selection
   */
  static validateOLevelSubjects(selectedSubjects, classLevel) {
    const errors = [];
    
    if (!Array.isArray(selectedSubjects) || selectedSubjects.length === 0) {
      errors.push('Please select at least one subject');
      return { isValid: false, errors };
    }
    
    // Validate minimum and maximum subjects
    if (selectedSubjects.length < 8) {
      errors.push('Please select at least 8 subjects');
    }
    
    if (selectedSubjects.length > 12) {
      errors.push('Maximum 12 subjects allowed');
    }
    
    // Class level specific validation
    if (classLevel === 's1_s2') {
      // S1-S2 specific rules
      const electiveSubjects = selectedSubjects.filter(id => {
        const checkbox = document.querySelector(`input[value="${id}"]:not([data-compulsory="true"])`);
        return checkbox !== null;
      });
      
      if (electiveSubjects.length > 2) {
        errors.push('Maximum 2 elective subjects allowed for S.1-S.2');
      }
    } else if (classLevel === 's3_s4') {
      // S3-S4 specific rules
      const electiveSubjects = selectedSubjects.filter(id => {
        const checkbox = document.querySelector(`input[value="${id}"]:not([data-compulsory="true"])`);
        return checkbox !== null;
      });
      
      if (electiveSubjects.length > 2) {
        errors.push('Maximum 2 elective subjects allowed for S.3-S.4');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
  
  /**
   * Validate A-Level subject selection
   */
  static validateALevelSubjects(principalSubjects, subsidiarySubjects) {
    const errors = [];
    
    // Principal subjects validation (exactly 3)
    if (!Array.isArray(principalSubjects) || principalSubjects.length !== 3) {
      errors.push('Please select exactly 3 principal subjects');
    }
    
    // Subsidiary subjects validation (exactly 1 + GP)
    if (!Array.isArray(subsidiarySubjects) || subsidiarySubjects.length !== 2) {
      errors.push('Please select 1 subsidiary subject (General Paper is automatically included)');
    }
    
    // Check for duplicate subjects
    const allSubjects = [...(principalSubjects || []), ...(subsidiarySubjects || [])];
    const uniqueSubjects = [...new Set(allSubjects)];
    
    if (allSubjects.length !== uniqueSubjects.length) {
      errors.push('Cannot select the same subject multiple times');
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
  
  /**
   * Display validation errors in the UI
   */
  static displayErrors(errors, containerId = 'validation-errors') {
    const container = document.getElementById(containerId);
    
    if (!container) {
      console.warn(`Validation error container '${containerId}' not found`);
      return;
    }
    
    if (errors.length === 0) {
      container.innerHTML = '';
      container.classList.add('hidden');
      return;
    }
    
    container.innerHTML = `
      <div class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-red-400"></i>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">
              Please correct the following errors:
            </h3>
            <div class="mt-2 text-sm text-red-700">
              <ul class="list-disc list-inside space-y-1">
                ${errors.map(error => `<li>${error}</li>`).join('')}
              </ul>
            </div>
          </div>
        </div>
      </div>
    `;
    
    container.classList.remove('hidden');
    
    // Scroll to errors
    container.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
  }
  
  /**
   * Clear validation errors
   */
  static clearErrors(containerId = 'validation-errors') {
    const container = document.getElementById(containerId);
    if (container) {
      container.innerHTML = '';
      container.classList.add('hidden');
    }
  }
  
  /**
   * Validate form fields in real-time
   */
  static addRealTimeValidation(formElement, studentType) {
    if (!formElement) return;
    
    const inputs = formElement.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
      input.addEventListener('blur', () => {
        this.validateField(input, studentType);
      });
      
      input.addEventListener('input', () => {
        // Clear field-specific errors on input
        this.clearFieldError(input);
      });
    });
  }
  
  /**
   * Validate individual field
   */
  static validateField(field, studentType) {
    const errors = [];
    const value = field.value.trim();
    const fieldName = field.name || field.id;
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
      errors.push(`${this.getFieldLabel(field)} is required`);
    }
    
    // Field-specific validation
    switch (fieldName) {
      case 'admission_number':
        if (value && !/^[A-Z0-9\/\-]+$/i.test(value)) {
          errors.push('Admission number can only contain letters, numbers, hyphens, and forward slashes');
        }
        break;
        
      case 'first_name':
      case 'last_name':
        if (value && value.length < 2) {
          errors.push(`${this.getFieldLabel(field)} must be at least 2 characters long`);
        }
        break;
        
      case 'date_of_birth':
        if (value) {
          const birthDate = new Date(value);
          const today = new Date();
          const age = today.getFullYear() - birthDate.getFullYear();
          
          if (isNaN(birthDate.getTime())) {
            errors.push('Please enter a valid date');
          } else if (birthDate > today) {
            errors.push('Date of birth cannot be in the future');
          } else if (age < 10 || age > 25) {
            errors.push('Student age must be between 10 and 25 years');
          }
        }
        break;
    }
    
    // Display field-specific errors
    this.displayFieldErrors(field, errors);
    
    return errors.length === 0;
  }
  
  /**
   * Display errors for a specific field
   */
  static displayFieldErrors(field, errors) {
    // Remove existing error display
    this.clearFieldError(field);
    
    if (errors.length === 0) return;
    
    // Add error styling to field
    field.classList.add('border-red-300', 'focus:border-red-500', 'focus:ring-red-500');
    field.classList.remove('border-gray-300', 'focus:border-primary-500', 'focus:ring-primary-500');
    
    // Create error message element
    const errorElement = document.createElement('div');
    errorElement.className = 'mt-1 text-sm text-red-600 field-error';
    errorElement.innerHTML = errors.join('<br>');
    
    // Insert error message after the field
    field.parentNode.insertBefore(errorElement, field.nextSibling);
  }
  
  /**
   * Clear field-specific errors
   */
  static clearFieldError(field) {
    // Remove error styling
    field.classList.remove('border-red-300', 'focus:border-red-500', 'focus:ring-red-500');
    field.classList.add('border-gray-300', 'focus:border-primary-500', 'focus:ring-primary-500');
    
    // Remove error message
    const errorElement = field.parentNode.querySelector('.field-error');
    if (errorElement) {
      errorElement.remove();
    }
  }
  
  /**
   * Get user-friendly field label
   */
  static getFieldLabel(field) {
    const label = field.parentNode.querySelector('label');
    if (label) {
      return label.textContent.replace('*', '').trim();
    }
    
    // Fallback to field name
    return field.name || field.id || 'Field';
  }
  
  /**
   * Comprehensive form validation
   */
  static validateForm(formData, studentType) {
    const allErrors = [];
    
    // Basic info validation
    const basicValidation = this.validateBasicInfo(formData, studentType);
    if (!basicValidation.isValid) {
      allErrors.push(...basicValidation.errors);
    }
    
    // Subject validation
    if (studentType === 'o_level' && formData.selected_subjects) {
      const subjectValidation = this.validateOLevelSubjects(
        formData.selected_subjects, 
        formData.class_level
      );
      if (!subjectValidation.isValid) {
        allErrors.push(...subjectValidation.errors);
      }
    } else if (studentType === 'a_level') {
      const principalSubjects = [
        formData.principal_subject_1_id,
        formData.principal_subject_2_id,
        formData.principal_subject_3_id
      ].filter(id => id);
      
      const subsidiarySubjects = [
        formData.subsidiary_subject_1_id,
        formData.subsidiary_subject_2_id
      ].filter(id => id);
      
      const subjectValidation = this.validateALevelSubjects(principalSubjects, subsidiarySubjects);
      if (!subjectValidation.isValid) {
        allErrors.push(...subjectValidation.errors);
      }
    }
    
    return {
      isValid: allErrors.length === 0,
      errors: allErrors
    };
  }
}

// Make utility globally available
window.StudentValidationUtil = StudentValidationUtil;
