/**
 * Academic Context Manager
 * Manages the active academic year and term throughout the system
 */

class AcademicContextManager {
  constructor() {
    this.context = {
      activeAcademicYear: null,
      activeTerm: null,
      allAcademicYears: [],
      allTerms: [],
      isLoaded: false
    };
    
    this.listeners = [];
    this.loadPromise = null;
  }

  // Initialize the academic context
  async initialize() {
    if (this.loadPromise) {
      return this.loadPromise;
    }

    // Wait for API to be available
    if (!window.API) {
      console.log('⏳ Waiting for API to be available...');
      await new Promise(resolve => {
        const checkAPI = () => {
          if (window.API) {
            resolve();
          } else {
            setTimeout(checkAPI, 100);
          }
        };
        checkAPI();
      });
    }

    this.loadPromise = this.loadAcademicContext();
    return this.loadPromise;
  }

  // Load academic context from API
  async loadAcademicContext() {
    try {
      console.log('🔄 Loading academic context...');

      // Check if user is authenticated before making API calls
      if (!window.SR?.currentUser && !localStorage.getItem('smartreport_token')) {
        console.log('⏳ User not authenticated yet, skipping academic context load');
        return;
      }

      // Load current active academic year and term
      const contextResponse = await window.API.get('/academic/current-context');
      console.log('📡 Context API response:', contextResponse);

      if (contextResponse.success) {
        // Map server response to our context structure
        this.context.activeAcademicYear = contextResponse.data.academicYear;
        this.context.activeTerm = contextResponse.data.currentTerm;

        console.log('📅 Mapped academic context:', {
          activeAcademicYear: this.context.activeAcademicYear,
          activeTerm: this.context.activeTerm
        });
      } else {
        console.warn('⚠️ Context API call failed:', contextResponse);
      }

      // Load all academic years for dropdowns
      const yearsResponse = await window.AcademicYearsAPI.getAll();
      if (yearsResponse.success) {
        this.context.allAcademicYears = yearsResponse.data;
      }

      // Load all terms for the active academic year
      if (this.context.activeAcademicYear) {
        const termsResponse = await window.TermsAPI.getByAcademicYear(this.context.activeAcademicYear.id);
        if (termsResponse.success) {
          this.context.allTerms = termsResponse.data;
        }
      }

      this.context.isLoaded = true;
      console.log('✅ Academic context loaded:', this.context);

      // Notify all listeners
      this.notifyListeners();

      return this.context;
    } catch (error) {
      console.error('❌ Failed to load academic context:', error);
      throw error;
    }
  }

  // Get current active academic year
  getActiveAcademicYear() {
    return this.context.activeAcademicYear;
  }

  // Get current active term
  getActiveTerm() {
    return this.context.activeTerm;
  }

  // Get all academic years (for dropdowns)
  getAllAcademicYears() {
    return this.context.allAcademicYears;
  }

  // Get all terms for active academic year
  getAllTerms() {
    return this.context.allTerms;
  }

  // Get terms for a specific academic year
  async getTermsForYear(academicYearId) {
    try {
      const response = await window.TermsAPI.getByAcademicYear(academicYearId);
      return response.success ? response.data : [];
    } catch (error) {
      console.error('Failed to load terms for year:', academicYearId, error);
      return [];
    }
  }

  // Check if context is loaded
  isContextLoaded() {
    return this.context.isLoaded;
  }

  // Get full context
  getContext() {
    return { ...this.context };
  }

  // Refresh context (call after academic year/term changes)
  async refresh() {
    this.loadPromise = null;
    this.context.isLoaded = false;
    return this.initialize();
  }

  // Add listener for context changes
  addListener(callback) {
    this.listeners.push(callback);
  }

  // Remove listener
  removeListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  // Notify all listeners of context changes
  notifyListeners() {
    this.listeners.forEach(callback => {
      try {
        callback(this.context);
      } catch (error) {
        console.error('Error in academic context listener:', error);
      }
    });
  }

  // Set active academic year (admin function)
  async setActiveAcademicYear(academicYearId) {
    try {
      const response = await window.AcademicYearsAPI.setActive(academicYearId);
      if (response.success) {
        await this.refresh();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to set active academic year:', error);
      return false;
    }
  }

  // Set active term (admin function)
  async setActiveTerm(termId) {
    try {
      const response = await window.TermsAPI.setActive(termId);
      if (response.success) {
        await this.refresh();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to set active term:', error);
      return false;
    }
  }

  // Helper method to format academic year display
  formatAcademicYearDisplay(academicYear = null) {
    const year = academicYear || this.context.activeAcademicYear;
    if (!year) return 'No Academic Year';
    
    const startDate = new Date(year.start_date).toLocaleDateString();
    const endDate = new Date(year.end_date).toLocaleDateString();
    return `${year.name} (${startDate} - ${endDate})`;
  }

  // Helper method to format term display
  formatTermDisplay(term = null) {
    const currentTerm = term || this.context.activeTerm;
    if (!currentTerm) return 'No Active Term';
    
    const startDate = new Date(currentTerm.start_date).toLocaleDateString();
    const endDate = new Date(currentTerm.end_date).toLocaleDateString();
    return `${currentTerm.name} (${startDate} - ${endDate})`;
  }

  // Get academic context for API requests (commonly used parameters)
  getApiContext() {
    return {
      academic_year_id: this.context.activeAcademicYear?.id,
      term_id: this.context.activeTerm?.id
    };
  }
}

// Create global instance
window.AcademicContext = new AcademicContextManager();

// Note: Academic context will be initialized after authentication
// Components should call window.AcademicContext.initialize() when needed

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AcademicContextManager;
}
