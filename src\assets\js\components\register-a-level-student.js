// SmartReport - A-Level Student Registration Component
// Handles registration of A-Level students (S.5 to S.6)

// Uses global API services: window.StudentsAPI, window.ClassesAPI, etc.
// Uses global config: window.SRConfig
// Uses environment configuration: ../config/environment.js

// Register A-Level Student Component
const RegisterALevelStudentComponent = {
  // Form data storage for retention on errors
  formData: {},

  // Render register A-Level student form
  render() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'A-Level Student Registration',
          'Complete all required fields to register a new student'
        )}
        <!-- Registration Form -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <form id="register-a-level-student-form" class="${SRDesignSystem.responsive.spacing.padding} space-y-8">
            <!-- Hidden fields for auto-populated values -->
            <input type="hidden" name="student_level" value="a_level">
            <input type="hidden" id="status" name="status" value="active">
            <input type="hidden" id="current_academic_year_id" name="current_academic_year_id" value="" required>
            <input type="hidden" id="current_term_id" name="current_term_id" value="" required>
            <input type="hidden" id="registration_date" name="registration_date" value="">

            <!-- Personal Information Section -->
            <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900 mb-6">Personal Information</h3>

              <!-- Passport Photo and Admission Number - First row (3 columns: 2+1) -->
              <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap} mb-4">
                <div class="col-span-2 form-field">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Passport Photo
                  </label>
                  <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center space-x-4">
                      <div class="flex-shrink-0">
                        <img id="passport-photo-preview"
                             class="h-20 w-20 rounded-full object-cover border-2 border-gray-200"
                             src="${window.SR.serverUrl}/assets/images/default-avatar.png"
                             alt="Student Photo"
                             onerror="this.src='${window.SR.serverUrl}/assets/images/default-avatar.png'; this.onerror=null;">
                      </div>
                      <div class="flex-1">
                        <input type="file" id="passport_photo" name="passport_photo" accept="image/*"
                               onchange="RegisterALevelStudentComponent.previewPhoto(this)"
                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                        <p class="mt-1 text-xs text-gray-500">Upload student passport photo (JPG, PNG - Max 2MB)</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-span-1">
                  ${SRDesignSystem.forms.input('admission_number', 'Admission Number', '', {
                    required: true,
                    placeholder: 'e.g., A2024/001',
                    helpText: 'Unique A-Level student identifier'
                  })}
                </div>
              </div>

              <!-- Names - Second row (3 columns) -->
              <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap} mb-4">
                ${SRDesignSystem.forms.input('first_name', 'First Name', '', {
                  required: true,
                  placeholder: 'Enter first name'
                })}
                ${SRDesignSystem.forms.input('middle_name', 'Middle Name', '', {
                  placeholder: 'Enter middle name (optional)'
                })}
                ${SRDesignSystem.forms.input('last_name', 'Last Name', '', {
                  required: true,
                  placeholder: 'Enter last name'
                })}
              </div>

              <!-- Date of Birth and Gender - Third row (2 columns) -->
              <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                ${SRDesignSystem.forms.input('date_of_birth', 'Date of Birth', '', {
                  type: 'date',
                })}
                ${SRDesignSystem.forms.select('gender', 'Gender', [
                  { value: '', label: 'Select Gender' },
                  { value: 'Male', label: 'Male' },
                  { value: 'Female', label: 'Female' }
                ], '', { required: true })}
              </div>
            </div>



            <!-- Class & Stream Selection Section -->
            <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900 mb-6">Class Selection</h3>

              <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">

                ${SRDesignSystem.forms.select('current_class_id', 'Current Class', [
                  { value: '', label: 'Select Class' }
                ], '', {
                  required: true,
                  helpText: 'Assign student to an A-Level class'
                })}

                ${SRDesignSystem.forms.select('stream_id', 'Class Stream', [
                  { value: '', label: 'Select Class First', disabled: true }
                ], '', {
                  required: true,
                  helpText: 'Mandatory for A-Level students',
                  disabled: true
                })}

              </div>
            </div>

            <!-- A-Level Subject Combination Section -->
            <div id="a-level-subjects-section" class="hidden bg-white rounded-xl shadow-soft border border-gray-200 p-6">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900 mb-6">Subject Combination</h3>

              <div id="a-level-subjects-container" class="min-h-[100px]">
                <!-- A-Level subjects will be populated dynamically -->
              </div>
            </div>

            <!-- Hidden General Paper Field -->
            <input type="hidden" id="general_paper_id" name="general_paper_id" required>

            <!-- Form Actions -->
            <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
              <div class="flex items-center justify-end">
                <div class="flex justify-end">
                  ${SRDesignSystem.forms.button('register', 'Register Student', 'primary', {
                    type: 'submit',
                    loading: false,
                    icon: 'fas fa-user-plus'
                  })}
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    `;
  },

  // Initialize event listeners for A-Level registration
  initializeEventListeners() {


    // Form submission
    const form = document.getElementById('register-a-level-student-form');
    if (form) {
      form.addEventListener('submit', this.handleSubmit.bind(this));
    }

    // Handle class selection change
    const classSelect = document.getElementById('current_class_id');
    if (classSelect) {
      classSelect.addEventListener('change', this.handleClassChange.bind(this));
    }

    // Setup name field formatting
    this.setupNameFieldFormatting();

    // Setup admission number formatting
    this.setupAdmissionNumberFormatting();

    // Initialize other components
    this.populateDropdowns();
  },

  // Setup name field formatting (uppercase, letters only)
  setupNameFieldFormatting() {
    const nameFields = ['first_name', 'middle_name', 'last_name'];

    nameFields.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (field) {
        // Prevent non-letter characters from being typed
        field.addEventListener('keydown', (e) => {
          // Allow navigation and control keys
          const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];

          // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z
          if ((e.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(e.key.toLowerCase())) ||
              allowedKeys.includes(e.key)) {
            return;
          }

          // Prevent spaces and only allow letters (a-z, A-Z)
          if (e.key === ' ' || !/^[a-zA-Z]$/.test(e.key)) {
            e.preventDefault();
          }
        });

        // Convert to uppercase and remove any non-letter characters including spaces
        field.addEventListener('input', (e) => {
          let value = e.target.value.toUpperCase().replace(/[^A-Z]/g, '');
          e.target.value = value;
        });

        // Handle paste events - only allow letters, no spaces
        field.addEventListener('paste', (e) => {
          e.preventDefault();
          let paste = '';
          if (e.clipboardData) {
            paste = e.clipboardData.getData('text');
          }
          paste = paste.toUpperCase().replace(/[^A-Z]/g, '');
          e.target.value = paste;
        });
      }
    });
  },

  // Setup admission number formatting (no spaces allowed)
  setupAdmissionNumberFormatting() {
    const admissionField = document.getElementById('admission_number');
    if (admissionField) {
      // Prevent spaces from being typed
      admissionField.addEventListener('keydown', (e) => {
        // Block space key
        if (e.key === ' ' || e.key === 'Spacebar') {
          e.preventDefault();
          return;
        }

        // Allow navigation and control keys
        const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];

        // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z
        if ((e.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(e.key.toLowerCase())) ||
            allowedKeys.includes(e.key)) {
          return;
        }
      });

      // Remove spaces on input (in case of paste)
      admissionField.addEventListener('input', (e) => {
        e.target.value = e.target.value.replace(/\s/g, '');
      });

      // Handle paste events to remove spaces
      admissionField.addEventListener('paste', (e) => {
        e.preventDefault();
        let paste = '';
        if (e.clipboardData) {
          paste = e.clipboardData.getData('text');
        }
        paste = paste.replace(/\s/g, ''); // Remove all spaces
        e.target.value = paste;
      });
    }
  },

  // Preview passport photo
  previewPhoto(input) {
    if (input.files && input.files[0]) {
      const file = input.files[0];

      // Validate file size (2MB max)
      if (file.size > 2 * 1024 * 1024) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('File size must be less than 2MB', 'error');
        } else {
          alert('File size must be less than 2MB');
        }
        input.value = '';
        return;
      }

      // Validate file type (only JPG and PNG)
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select a JPG or PNG image file', 'error');
        } else {
          alert('Please select a JPG or PNG image file');
        }
        input.value = '';
        return;
      }

      const reader = new FileReader();
      reader.onload = function(e) {
        const preview = document.getElementById('passport-photo-preview');
        if (preview) {
          preview.src = e.target.result;
        }
      };
      reader.readAsDataURL(file);
    }
  },

  // Handle form submission
  async handleSubmit(event) {
    event.preventDefault();

    // Save form data for retention in case of errors
    this.saveFormData();

    const formData = new FormData(event.target);
    let data = Object.fromEntries(formData.entries());

    // Handle passport photo upload if provided
    const passportPhotoFile = formData.get('passport_photo');
    if (passportPhotoFile && passportPhotoFile.size > 0) {
      try {
        SRDesignSystem.forms.setButtonLoading('register', true);

        // Upload the photo first
        const uploadResult = await window.ImageUploadUtil.uploadImage(
          passportPhotoFile,
          'a-level-student-photo'
        );

        if (uploadResult.success) {
          data.passport_photo = uploadResult.filePath;
        } else {
          throw new Error('Failed to upload passport photo');
        }
      } catch (uploadError) {
        console.error('Photo upload error:', uploadError);
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(
            'Failed to upload passport photo: ' + uploadError.message,
            'error'
          );
        }
        SRDesignSystem.forms.setButtonLoading('register', false);
        return;
      }
    } else {
      // No photo provided, set to null
      data.passport_photo = null;
    }

    // Format name fields (uppercase, letters only)
    if (data.first_name) data.first_name = data.first_name.toUpperCase().replace(/[^A-Z]/g, '');
    if (data.middle_name) data.middle_name = data.middle_name.toUpperCase().replace(/[^A-Z]/g, '');
    if (data.last_name) data.last_name = data.last_name.toUpperCase().replace(/[^A-Z]/g, '');

    // Validate required fields (based on database schema)
    if (!data.admission_number || !data.first_name || !data.last_name || !data.gender) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Please fill in all required personal information fields (admission number, first name, last name, and gender)', 'error');
      }
      return;
    }

    // Validate required academic assignment fields
    if (!data.current_academic_year_id || !data.current_term_id || !data.current_class_id) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Please select academic year, term, and class - these are required for A-Level student registration', 'error');
      }
      return;
    }

    // Validate stream selection (mandatory for A-Level)
    if (!data.stream_id) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Please select a stream (Arts or Sciences) as it is mandatory for A-Level students', 'error');
      }
      return;
    }

    // Validate A-Level subject selection
    const validation = this.validateALevelSubjectSelection();
    if (!validation.isValid) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show(validation.message, 'error');
      }
      return;
    }

    // Collect selected principal subjects
    const principalSubjects = Array.from(document.querySelectorAll('input[name="selected_subjects[]"]:checked:not([disabled])'))
      .map(input => parseInt(input.value));

    // Collect selected subsidiary subject
    const subsidiarySubject = document.querySelector('input[name="subsidiary_subject"]:checked');
    const subsidiarySubjectId = subsidiarySubject ? parseInt(subsidiarySubject.value) : null;

    // Get General Paper ID from the hidden field
    const generalPaperHidden = document.getElementById('general_paper_id');
    const generalPaperId = generalPaperHidden ? parseInt(generalPaperHidden.value) : null;

    // Add subject assignments to data
    data.principal_subject_1_id = principalSubjects[0] || null;
    data.principal_subject_2_id = principalSubjects[1] || null;
    data.principal_subject_3_id = principalSubjects[2] || null;

    // A-Level has 2 subsidiary subjects: GP (mandatory) + 1 chosen (SMATH or SICT)
    data.subsidiary_subject_1_id = generalPaperId; // General Paper is always subsidiary subject 1
    data.subsidiary_subject_2_id = subsidiarySubjectId; // Chosen subsidiary subject (SMATH or SICT)


    try {
      SRDesignSystem.forms.setButtonLoading('register', true);

      // Use A-Level specific API endpoint
      const result = await window.StudentsAPI.registerALevel(data);

      if (result.success) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(
            `Student registered successfully!`,
            'success'
          );
        }

        // Clear saved form data on successful submission
        this.clearFormData();

        event.target.reset();
        setTimeout(() => {
          if (window.PageRouter) {
            window.PageRouter.loadPage('manage-students');
          }
        }, 1500);
      } else {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to register student', 'error');
        }
      }
    } catch (error) {
      console.error('A-Level registration error:', error);

      // Extract the specific error message from the server response
      let errorMessage = 'Failed to register student';
      if (error.message) {
        if (error.message.includes('Academic context not set')) {
          errorMessage = 'Academic context not set. Please set up an active academic year and term before registering students.';
        } else if (error.message.includes('setupRequired')) {
          errorMessage = 'Academic setup required. Please configure academic year and term settings first.';
        } else {
          errorMessage = error.message;
        }
      }

      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show(errorMessage, 'error');
      }
    } finally {
      SRDesignSystem.forms.setButtonLoading('register', false);
    }
  },

  // Handle class selection change
  handleClassChange(event) {
    const classId = event.target.value;
    const selectedOption = event.target.selectedOptions[0];
    const classes = StudentManagementComponents.state.classes.data || StudentManagementComponents.state.classes;
    const selectedClass = Array.isArray(classes) ? classes.find(cls => cls.id == classId) : null;

    // Hide subject section first
    this.hideSubjectSection();

    // Clear stream dropdown first
    this.clearStreamDropdown();

    if (selectedClass && classId) {
      // Determine education level to ensure this is an A-Level class
      const educationLevel = this.determineEducationLevel(selectedClass, selectedOption);

      if (educationLevel === 'a_level') {
        // Load streams for the selected class
        this.loadStreamsForClass(classId);
        this.showALevelSubjects();
      } else {
        // Show error message or redirect to appropriate form
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(
            'Please select an A-Level class',
            'warning'
          );
        }
      }
    } else {
      // No class selected, clear streams
      this.clearStreamDropdown();
    }
  },

  // Determine education level from class data
  determineEducationLevel(selectedClass, selectedOption) {
    if (!selectedClass) return null;

    // Check option dataset first (most reliable)
    const optionEducationLevel = selectedOption?.dataset?.educationLevel;
    if (optionEducationLevel) {
      return optionEducationLevel;
    }

    // Fallback to class data analysis
    const educationLevelCode = selectedClass.education_level_code;
    const classLevelCode = selectedClass.class_level_code;
    const className = selectedClass.name || '';

    // Check for A-Level (S.5 to S.6)
    if (educationLevelCode === 'a_level' ||
        ['s5', 's6'].includes(classLevelCode) ||
        className.match(/S\.[5-6]/) ||
        className.match(/S[5-6]/) ||
        className.toLowerCase().includes('senior 5') ||
        className.toLowerCase().includes('senior 6')) {
      return 'a_level';
    }

    // Check for O-Level (S.1 to S.4)
    if (educationLevelCode === 'o_level' ||
        ['s1', 's2', 's3', 's4'].includes(classLevelCode) ||
        className.match(/S\.[1-4]/) ||
        className.match(/S[1-4]/) ||
        className.toLowerCase().includes('senior 1') ||
        className.toLowerCase().includes('senior 2') ||
        className.toLowerCase().includes('senior 3') ||
        className.toLowerCase().includes('senior 4')) {
      return 'o_level';
    }

    return null;
  },

  // Hide subject section
  hideSubjectSection() {
    // Hide the A-Level subjects section
    this.hideALevelSubjectsSection();
  },

  // Show A-Level subject selection
  showALevelSubjects() {
    console.log('📚 Showing A-Level subject selection...');

    // Show the A-Level subjects section
    const aLevelSection = document.getElementById('a-level-subjects-section');
    if (aLevelSection) {
      aLevelSection.classList.remove('hidden');
      console.log('✅ A-Level subjects section shown');
    } else {
      console.error('❌ A-Level subjects section not found');
      return;
    }

    // Load and populate A-Level subjects
    this.loadALevelSubjects();
  },

  // Clear subject containers
  clearSubjectContainer() {
    const container = document.getElementById('a-level-subjects-container');
    if (container) {
      container.innerHTML = '';
    }
  },

  // Hide A-Level subjects section
  hideALevelSubjectsSection() {
    const aLevelSection = document.getElementById('a-level-subjects-section');
    if (aLevelSection) {
      aLevelSection.classList.add('hidden');
    }
    this.clearSubjectContainer();
  },

  // Load A-Level subjects for selection
  async loadALevelSubjects() {
    try {

      // For A-Level, load ALL subjects (not filtered by class level or stream)
      // Students can choose any 3 principal + 2 subsidiary subjects
      const result = await window.SubjectsAPI.aLevel.getAll();

      if (result.success) {
        const subjects = result.data || result;
        this.renderALevelSubjects(Array.isArray(subjects) ? subjects : []);
      } else {
        console.error('Failed to load A-Level subjects:', result.message);
        this.showSubjectLoadError('principal-subjects-container', 'A-Level subjects');
      }
    } catch (error) {
      console.error('Failed to load A-Level subjects:', error);
      this.showSubjectLoadError('principal-subjects-container', 'A-Level subjects');
    }
  },

  // Show error message when subjects fail to load
  showSubjectLoadError(containerId, subjectType) {
    const container = document.getElementById(containerId);
    if (container) {
      container.innerHTML = `
        <div class="text-center py-12 text-red-500">
          <div class="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-500')}
          </div>
          <p class="${SRDesignSystem.responsive.text.lg} font-medium text-red-600">Failed to load ${subjectType}</p>
          <p class="${SRDesignSystem.responsive.text.sm} text-red-500 mt-1">Please try refreshing the page or contact support.</p>
        </div>
      `;
    }
  },

  // Render A-Level subjects for selection
  renderALevelSubjects(subjects) {
    const container = document.getElementById('a-level-subjects-container');

    if (!container) {
      console.error('❌ A-Level subjects container not found');
      return;
    }

    if (!Array.isArray(subjects) || subjects.length === 0) {
      container.innerHTML = `
        <div class="text-center py-8 text-gray-500">
          <div class="bg-gray-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
            ${SRDesignSystem.components.icon('fas fa-graduation-cap', 'xl', 'gray-400')}
          </div>
          <p class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-600">No A-Level subjects available</p>
          <p class="${SRDesignSystem.responsive.text.xs} text-gray-500 mt-1">Please contact the administrator.</p>
        </div>
      `;
      return;
    }

    // Group A-Level subjects by type
    const principalSubjects = subjects.filter(s => s.subject_type === 'Principal');
    const subsidiarySubjects = subjects.filter(s => s.subject_type === 'Subsidiary' && s.short_name !== 'GP');
    const generalPaper = subjects.find(s => s.short_name === 'GP');


    // Generate the complete subject selection UI
    container.innerHTML = `
      <div class="space-y-6">
        <!-- Selection Rules -->
        <div class="bg-white rounded-lg shadow-soft border border-gray-200 p-4">
          <h4 class="font-semibold text-gray-900 mb-2">Subject Selection Rules</h4>
          <p class="${SRDesignSystem.responsive.text.sm} text-gray-600">A-Level: Select exactly 3 Principal subjects + 1 Subsidiary subject (General Paper is automatically included) = 5 total subjects</p>
        </div>

        <!-- Principal Subjects -->
        ${principalSubjects.length > 0 ? `
          <div class="bg-white rounded-lg shadow-soft border border-gray-200 p-4">
            <h4 class="font-semibold text-gray-900 mb-3">Principal Subjects</h4>
            <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap}">
              ${principalSubjects.map(subject => `
                <div class="flex items-center space-x-3 p-3 bg-white border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                  <input type="checkbox"
                         id="principal_${subject.id}"
                         name="selected_subjects[]"
                         value="${subject.id}"
                         class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                  <label for="principal_${subject.id}" class="flex-1 cursor-pointer">
                    <div class="font-medium text-gray-900 ${SRDesignSystem.responsive.text.sm}">${subject.name}</div>
                    <div class="${SRDesignSystem.responsive.text.xs} text-gray-500">${subject.short_name || subject.code || ''}</div>
                  </label>
                </div>
              `).join('')}
            </div>
          </div>
        ` : `
          <div class="bg-white rounded-lg shadow-soft border border-gray-200 p-4">
            <p class="${SRDesignSystem.responsive.text.sm} text-gray-600">No Principal subjects available</p>
          </div>
        `}

        <!-- Subsidiary Subjects -->
        ${subsidiarySubjects.length > 0 ? `
          <div class="bg-white rounded-lg shadow-soft border border-gray-200 p-4">
            <h4 class="font-semibold text-gray-900 mb-3">Subsidiary Subjects</h4>
            <p class="${SRDesignSystem.responsive.text.sm} text-gray-600 mb-4">
              <strong>Note:</strong> General Paper is automatically included as a subsidiary subject.
            </p>
            <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
              ${subsidiarySubjects.map(subject => `
                <div class="flex items-center space-x-3 p-3 bg-white border border-gray-200 rounded-lg hover:border-purple-300 transition-colors">
                  <input type="radio"
                         id="subsidiary_${subject.id}"
                         name="subsidiary_subject"
                         value="${subject.id}"
                         class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300">
                  <label for="subsidiary_${subject.id}" class="flex-1 cursor-pointer">
                    <div class="font-medium text-gray-900 ${SRDesignSystem.responsive.text.sm}">${subject.name}</div>
                    <div class="${SRDesignSystem.responsive.text.xs} text-gray-500">${subject.short_name || subject.code || ''}</div>
                  </label>
                </div>
              `).join('')}
            </div>
          </div>
        ` : `
          <div class="bg-white rounded-lg shadow-soft border border-gray-200 p-4">
            <p class="${SRDesignSystem.responsive.text.sm} text-gray-600">No Subsidiary subjects available</p>
          </div>
        `}
      </div>
    `;

    // Populate hidden General Paper field
    if (generalPaper) {
      const generalPaperHidden = document.getElementById('general_paper_id');
      if (generalPaperHidden) {
        generalPaperHidden.value = generalPaper.id;
      }
    }

    // Add event listeners for A-Level subject selection validation
    this.addALevelSubjectSelectionListeners();
  },

  // Add event listeners for A-Level subject selection validation
  addALevelSubjectSelectionListeners() {
    // Add listeners for principal subject checkboxes
    const principalCheckboxes = document.querySelectorAll('input[name="selected_subjects[]"]');
    principalCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        this.validateALevelPrincipalSelection();
      });
    });

    // Add listeners for subsidiary subject radio buttons
    const subsidiaryRadios = document.querySelectorAll('input[name="subsidiary_subject"]');
    subsidiaryRadios.forEach(radio => {
      radio.addEventListener('change', () => {
        this.validateALevelSubsidiarySelection();
      });
    });
  },

  // Validate A-Level principal subject selection (exactly 3)
  validateALevelPrincipalSelection() {
    const selectedPrincipal = document.querySelectorAll('input[name="selected_subjects[]"]:checked');

    if (selectedPrincipal.length >= 3) {
      // Disable remaining principal checkboxes
      const unselectedPrincipal = document.querySelectorAll('input[name="selected_subjects[]"]:not(:checked)');
      unselectedPrincipal.forEach(checkbox => {
        checkbox.disabled = true;
        checkbox.parentElement.classList.add('opacity-50');
      });
    } else {
      // Re-enable all principal checkboxes
      const principalCheckboxes = document.querySelectorAll('input[name="selected_subjects[]"]');
      principalCheckboxes.forEach(checkbox => {
        checkbox.disabled = false;
        checkbox.parentElement.classList.remove('opacity-50');
      });
    }
  },

  // Validate A-Level subsidiary subject selection (exactly 1)
  validateALevelSubsidiarySelection() {
    // No additional validation needed for radio buttons (only one can be selected)
  },

  // Render individual subject card
  renderSubjectCard(subject, isCompulsory = false, inputType = 'checkbox', groupName = 'selected_subjects[]') {
    const isRequired = isCompulsory || subject.is_compulsory || subject.subject_type === 'compulsory';
    const inputName = inputType === 'radio' ? groupName : 'selected_subjects[]';

    return `
      <div class="flex items-center space-x-3 p-3 bg-white border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
        <input type="${inputType}"
               id="subject_${subject.id}"
               name="${inputName}"
               value="${subject.id}"
               ${isRequired ? 'checked disabled data-compulsory="true"' : ''}
               class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 ${inputType === 'radio' ? 'rounded-full' : 'rounded'}">
        <label for="subject_${subject.id}" class="flex-1 cursor-pointer">
          <div class="font-medium text-gray-900 text-sm">${subject.name}</div>
          <div class="text-xs text-gray-500">${subject.short_name || subject.code || ''}</div>
          ${isRequired ?
            '<span class="inline-block text-xs text-blue-600 font-medium bg-blue-100 px-2 py-1 rounded mt-1">Required</span>' :
            subject.subject_type ? `<span class="inline-block text-xs text-green-600 font-medium bg-green-100 px-2 py-1 rounded mt-1">${subject.subject_type}</span>` : ''
          }
        </label>
      </div>
    `;
  },

  // Add event listeners for A-Level subject selection validation
  addALevelSubjectSelectionListeners() {
    // Principal subjects validation (exactly 3) - listen to all checkboxes, not just enabled ones
    const principalCheckboxes = document.querySelectorAll('input[name="selected_subjects[]"]:not([data-compulsory="true"])');
    principalCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        this.validateALevelSelection();
      });
    });

    // Subsidiary subject validation (exactly 1)
    const subsidiaryRadios = document.querySelectorAll('input[name="subsidiary_subject"]');
    subsidiaryRadios.forEach(radio => {
      radio.addEventListener('change', () => {
        this.validateALevelSelection();
      });
    });
  },

  // Validate A-Level subject selection
  validateALevelSelection() {
    // Count all checked principal subjects (including those that might be disabled)
    const selectedPrincipal = document.querySelectorAll('input[name="selected_subjects[]"]:checked:not([data-compulsory="true"])');

    // Get all principal checkboxes (excluding compulsory subjects like General Paper)
    const allPrincipalCheckboxes = document.querySelectorAll('input[name="selected_subjects[]"]:not([data-compulsory="true"])');

    if (selectedPrincipal.length >= 3) {
      // Disable remaining principal checkboxes if 3 are selected
      allPrincipalCheckboxes.forEach(checkbox => {
        if (!checkbox.checked) {
          checkbox.disabled = true;
          checkbox.parentElement.classList.add('opacity-50', 'cursor-not-allowed');
        }
      });
    } else {
      // Re-enable all principal checkboxes when under the limit
      allPrincipalCheckboxes.forEach(checkbox => {
        checkbox.disabled = false;
        checkbox.parentElement.classList.remove('opacity-50', 'cursor-not-allowed');
      });
    }

    // Validate subsidiary subject selection
    const selectedSubsidiary = document.querySelector('input[name="subsidiary_subject"]:checked');

    // Show validation messages
    const principalError = document.getElementById('principal-subjects-error');
    const subsidiaryError = document.getElementById('subsidiary-subjects-error');

    if (principalError) {
      if (selectedPrincipal.length !== 3) {
        principalError.classList.remove('hidden');
        principalError.querySelector('#principal-subjects-error-message').textContent =
          'You must select exactly 3 principal subjects for A-Level (no more, no less)';
      } else {
        principalError.classList.add('hidden');
      }
    }

    if (subsidiaryError) {
      if (!selectedSubsidiary) {
        subsidiaryError.classList.remove('hidden');
        subsidiaryError.querySelector('#subsidiary-subjects-error-message').textContent =
          'Please select 1 subsidiary subject (SMATH or SICT)';
      } else {
        subsidiaryError.classList.add('hidden');
      }
    }
  },

  // Validate A-Level subject selection for form submission
  validateALevelSubjectSelection() {
    // Check principal subjects (exactly 3)
    const selectedPrincipal = document.querySelectorAll('input[name="selected_subjects[]"]:checked:not([disabled])');
    if (selectedPrincipal.length !== 3) {
      return {
        isValid: false,
        message: 'Please select exactly 3 Principal subjects for A-Level'
      };
    }

    // Check subsidiary subject (exactly 1 - GP is automatically included)
    const selectedSubsidiary = document.querySelector('input[name="subsidiary_subject"]:checked');
    if (!selectedSubsidiary) {
      return {
        isValid: false,
        message: 'Please select 1 Subsidiary subject (SMATH or SICT). General Paper is automatically included.'
      };
    }

    return { isValid: true };
  },

  // Populate dropdowns with data
  populateDropdowns() {
    // Populate hidden fields first
    this.populateHiddenFields();

    // Then populate visible dropdowns
    this.populateClasses();

    // Initialize stream dropdown in disabled state
    this.clearStreamDropdown();
  },

  // Populate hidden fields with current academic data
  populateHiddenFields() {

    // Use AcademicContext for consistent data
    const activeYear = window.AcademicContext?.getActiveAcademicYear();
    const activeTerm = window.AcademicContext?.getActiveTerm();

    // Set current academic year (hidden field only)
    if (activeYear) {
      const academicYearField = document.getElementById('current_academic_year_id');
      if (academicYearField) {
        academicYearField.value = activeYear.id;
      }
    }

    // Set current term (hidden field only)
    if (activeTerm) {
      const termField = document.getElementById('current_term_id');
      if (termField) {
        termField.value = activeTerm.id;
      }
    }

    // Set registration date to current date
    const registrationDateField = document.getElementById('registration_date');
    if (registrationDateField) {
      const currentDate = new Date().toISOString().split('T')[0];
      registrationDateField.value = currentDate;
    }
  },



  // Populate classes dropdown (A-Level only)
  populateClasses() {
    const classSelect = document.getElementById('current_class_id');
    if (!classSelect) return;

    const classes = StudentManagementComponents.state.classes.data || StudentManagementComponents.state.classes;
    if (Array.isArray(classes)) {
      // Filter for A-Level classes only (S.5 and S.6)
      const aLevelClasses = classes.filter(cls => {
        const isALevel = cls.education_level_code === 'a_level' ||
                        cls.class_level_code?.startsWith('s') && ['s5', 's6'].includes(cls.class_level_code) ||
                        cls.name?.match(/^S\.[5-6]$/);
        return isALevel;
      });

      classSelect.innerHTML = '<option value="">Select Class</option>';

      // Add A-Level classes (simple class names without streams)
      aLevelClasses.forEach(cls => {
        const option = document.createElement('option');
        option.value = cls.id;
        option.textContent = cls.name; // Just the class name (S.5, S.6)
        option.dataset.educationLevel = 'a_level';
        option.dataset.classLevelId = cls.class_level_id;
        option.dataset.classLevelCode = cls.class_level_code;
        classSelect.appendChild(option);
      });


    }
  },

  // Load streams for a specific class
  async loadStreamsForClass(classId) {
    try {
      const result = await window.StreamsAPI.getForClass(classId);

      if (result.success) {
        const { streams, streams_required } = result.data;
        this.populateStreamDropdown(streams, streams_required);
      } else {
        console.error('Failed to load streams:', result.message);
        this.clearStreamDropdown();
      }
    } catch (error) {
      console.error('Error loading streams:', error);
      this.clearStreamDropdown();
    }
  },

  // Populate stream dropdown
  populateStreamDropdown(streams = [], streamsRequired = false) {
    const streamSelect = document.getElementById('stream_id');
    if (!streamSelect) {
      return;
    }

    // Clear and activate the dropdown
    streamSelect.innerHTML = '<option value="">Select Stream</option>';
    streamSelect.disabled = false;

    if (streams.length === 0) {
      // No streams available
      streamSelect.innerHTML = '<option value="">No streams available</option>';
      streamSelect.disabled = true;
      streamSelect.required = false;
      return;
    }

    // Add stream options
    streams.forEach(stream => {
      const option = document.createElement('option');
      option.value = stream.id || '';
      option.textContent = stream.name;
      if (stream.is_default) {
        option.dataset.isDefault = 'true';
      }
      streamSelect.appendChild(option);
    });

    // Set required state based on streams requirement
    streamSelect.required = streamsRequired;

    // Update the label to show required state
    const label = document.querySelector('label[for="stream_id"]');
    if (label) {
      const labelText = streamsRequired ? 'Class Stream *' : 'Class Stream';
      label.innerHTML = labelText;
    }
  },

  // Clear stream dropdown
  clearStreamDropdown() {
    const streamSelect = document.getElementById('stream_id');
    if (streamSelect) {
      // Clear all options first
      streamSelect.innerHTML = '';

      // Create and add the disabled option
      const option = document.createElement('option');
      option.value = '';
      option.textContent = 'Select Class First';
      option.disabled = true;
      option.selected = true;
      streamSelect.appendChild(option);

      // Disable the dropdown
      streamSelect.disabled = true;
      streamSelect.required = false;

      // Update label to remove required indicator
      const label = document.querySelector('label[for="stream_id"]');
      if (label) {
        label.innerHTML = 'Class Stream';
      }
    }
  },

  // Auto-populate active academic year (hidden field only)
  populateAcademicYears() {
    const academicYearHidden = document.getElementById('current_academic_year_id');

    if (academicYearHidden) {
      const academicYears = StudentManagementComponents.state.academicYears.data || StudentManagementComponents.state.academicYears;

      if (Array.isArray(academicYears)) {
        const activeYear = academicYears.find(year => year.is_active);

        if (activeYear) {
          academicYearHidden.value = activeYear.id;
        }
      }
    }
  },

  // Auto-populate active term (hidden field only)
  populateTerms() {
    const termHidden = document.getElementById('current_term_id');

    if (termHidden) {
      const terms = StudentManagementComponents.state.terms.data || StudentManagementComponents.state.terms;

      if (Array.isArray(terms)) {
        const activeTerm = terms.find(term => term.is_active);

        if (activeTerm) {
          termHidden.value = activeTerm.id;
        }
      }
    }
  },

  // Populate A-Level subjects
  populateSubjects() {
    const subjectsData = StudentManagementComponents.state.subjects.data || StudentManagementComponents.state.subjects;
    const aLevelSubjects = subjectsData.aLevel || [];

    // Populate Principal subjects
    const principalContainer = document.getElementById('principal-subjects-container');
    if (principalContainer) {
      const principalSubjects = aLevelSubjects.filter(s => s.subject_type === 'principal');
      principalContainer.innerHTML = principalSubjects.map(subject => `
        <div class="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
          <input type="checkbox"
                 id="principal_${subject.id}"
                 name="principal_subjects[]"
                 value="${subject.id}"
                 class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
          <label for="principal_${subject.id}" class="flex-1 cursor-pointer">
            <div class="font-medium text-gray-900">${subject.name}</div>
            <div class="text-sm text-gray-500">${subject.short_name}</div>
          </label>
        </div>
      `).join('');
    }

    // Populate Subsidiary subjects
    const subsidiaryContainer = document.getElementById('subsidiary-subjects-container');
    if (subsidiaryContainer) {
      const subsidiarySubjects = aLevelSubjects.filter(s => s.subject_type === 'subsidiary');
      subsidiaryContainer.innerHTML = subsidiarySubjects.map(subject => `
        <div class="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
          <input type="checkbox"
                 id="subsidiary_${subject.id}"
                 name="subsidiary_subjects[]"
                 value="${subject.id}"
                 class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
          <label for="subsidiary_${subject.id}" class="flex-1 cursor-pointer">
            <div class="font-medium text-gray-900">${subject.name}</div>
            <div class="text-sm text-gray-500">${subject.short_name}</div>
          </label>
        </div>
      `).join('');
    }
  },



  // Initialize register A-Level student component
  async init() {
    // Wait for academic context to be loaded
    await window.AcademicContext.initialize();

    await StudentManagementComponents.loadInitialData();

    // Initialize directly - DOM should be ready due to lifecycle manager
    this.populateAcademicContextFields();
    this.initializeEventListeners();
    this.restoreFormData(); // Restore form data if it exists
  },

  // Reset component state
  resetComponentState() {
    this.formData = {};
  },

  // Cleanup component
  cleanup() {
    this.resetComponentState();

    // Reset form if it exists
    const form = document.getElementById('register-a-level-student-form');
    if (form) {
      form.reset();
    }
  },

  // Save current form data for retention on errors
  saveFormData() {
    const form = document.getElementById('register-a-level-student-form');
    if (form) {
      const formData = new FormData(form);
      this.formData = Object.fromEntries(formData.entries());

      // Also save checkbox states for subjects
      const checkboxes = form.querySelectorAll('input[type="checkbox"]');
      checkboxes.forEach(checkbox => {
        this.formData[checkbox.name + '_checked'] = checkbox.checked;
      });

      // Save radio button states
      const radios = form.querySelectorAll('input[type="radio"]:checked');
      radios.forEach(radio => {
        this.formData[radio.name] = radio.value;
      });

    }
  },

  // Restore form data after error
  restoreFormData() {
    if (Object.keys(this.formData).length === 0) return;

    const form = document.getElementById('register-a-level-student-form');
    if (!form) return;

    // Restore text inputs, selects, and textareas
    Object.keys(this.formData).forEach(key => {
      if (key.endsWith('_checked')) return; // Skip checkbox state keys

      const element = form.querySelector(`[name="${key}"]`);
      if (element && !element.disabled) {
        if (element.type === 'checkbox') {
          element.checked = this.formData[key + '_checked'] || false;
        } else if (element.type === 'radio') {
          if (element.value === this.formData[key]) {
            element.checked = true;
          }
        } else {
          element.value = this.formData[key] || '';
        }
      }
    });

    // Trigger change events to update dependent fields
    const classSelect = form.querySelector('#current_class_id');
    if (classSelect && classSelect.value) {
      classSelect.dispatchEvent(new Event('change'));
    }
  },

  // Clear saved form data (call after successful submission)
  clearFormData() {
    this.formData = {};
  },

  // Populate academic context fields with active academic year and term
  populateAcademicContextFields() {
    const activeYear = window.AcademicContext.getActiveAcademicYear();
    const activeTerm = window.AcademicContext.getActiveTerm();

    // Populate hidden academic year field
    const academicYearHidden = document.getElementById('current_academic_year_id');
    if (academicYearHidden && activeYear) {
      academicYearHidden.value = activeYear.id;
    }

    // Populate hidden term field
    const termHidden = document.getElementById('current_term_id');
    if (termHidden && activeTerm) {
      termHidden.value = activeTerm.id;
    }
  }
};

// Make component globally available
window.RegisterALevelStudentComponent = RegisterALevelStudentComponent;