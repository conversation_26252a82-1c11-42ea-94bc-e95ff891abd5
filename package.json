{"name": "smartreport", "version": "1.0.0", "main": "src/main.js", "scripts": {"setup": "node setup.js", "start": "electron .", "dev": "npm run css:build && nodemon --exec electron .", "server": "node src/server/server.js", "server:dev": "nodemon src/server/server.js", "build": "npm run css:build && electron-builder", "css:build": "postcss src/assets/css/input.css -o src/assets/css/output.css", "css:watch": "postcss src/assets/css/input.css -o src/assets/css/output.css --watch", "test": "echo \"Error: no test specified\" && exit 1", "init-db": "node -e \"require('dotenv').config(); require('./src/database/init').resetAndInitialize().then(result => { console.log(result.success ? '✅ Final Result: ' + result.message : '❌ Final Error: ' + result.error); process.exit(result.success ? 0 : 1); }).catch(err => { console.error('❌ Fatal error:', err.message); process.exit(1); })\""}, "keywords": ["education", "school-management", "uganda", "o-level", "a-level", "electron", "mysql"], "author": "SmartReport Development Team", "license": "MIT", "description": "Academic Information Management System for Uganda Secondary Schools", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "csv-parser": "^3.2.0", "csv-writer": "^1.6.0", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "mysql2": "^3.14.1", "pdfkit": "^0.17.1", "puppeteer": "^24.14.0"}, "devDependencies": {"autoprefixer": "^10.4.16", "electron": "^36.4.0", "nodemon": "^3.1.10", "postcss": "^8.4.32", "postcss-cli": "^11.0.0", "tailwindcss": "^3.4.0"}}