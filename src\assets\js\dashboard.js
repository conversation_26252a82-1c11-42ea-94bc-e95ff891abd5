// SmartReport Dashboard Component
// Clean, informative dashboard with real-time data and system admin context

const Dashboard = {
  // Dashboard state
  state: {
    stats: {},
    currentAdmin: null,
    enrollmentByLevel: [],
    genderDistribution: {},
    assessmentStats: {
      o_level: {},
      a_level: {}
    }
  },

  // Chart instances for proper cleanup
  enrollmentChart: null,
  genderChart: null,
  oLevelAssessmentChart: null,
  aLevelAssessmentChart: null,

  // Initialize dashboard
  async init() {
    try {
      await this.loadCurrentAdmin();
      await this.loadDashboardData();

      // Render the dashboard content first
      this.render();

      // Initialize charts after DOM is rendered and data is loaded
      setTimeout(() => {
        this.initializeCharts();
      }, 300);
    } catch (error) {
      console.error('❌ Dashboard initialization failed:', error);
      this.showErrorState(error);
    }
  },

  // Show loading state
  showLoadingState() {
    const container = document.getElementById('content-area');
    if (!container) return;

    container.innerHTML = `
      <div class="flex items-center justify-center min-h-96">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <h3 class="${SRDesignSystem.responsive.text.lg} font-medium text-gray-900 mb-2">Loading Dashboard</h3>
          <p class="text-gray-600">Gathering your school's data...</p>
        </div>
      </div>
    `;
  },

  // Show error state
  showErrorState(error) {
    const container = document.getElementById('content-area');
    if (!container) return;

    container.innerHTML = `
      <div class="flex items-center justify-center min-h-96">
        <div class="text-center max-w-md">
          <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-600')}
          </div>
          <h3 class="${SRDesignSystem.responsive.text.lg} font-medium text-gray-900 mb-2">Dashboard Error</h3>
          <p class="text-gray-600 mb-4">Unable to load dashboard data. Please try again.</p>
          ${SRDesignSystem.forms.button('retry-dashboard', 'Retry', 'primary', {
            onclick: 'window.Dashboard.init()'
          })}
        </div>
      </div>
    `;
  },

  // Load current admin information
  async loadCurrentAdmin() {
    try {
      const currentAdminId = this.getCurrentAdminId();
      if (currentAdminId) {
        // You could fetch admin details from API if needed
        this.state.currentAdmin = {
          id: currentAdminId,
          name: this.getAdminName()
        };
      }
    } catch (error) {
      console.error('❌ Failed to load current admin:', error);
      this.state.currentAdmin = null;
    }
  },

  // Get current admin ID
  getCurrentAdminId() {
    try {
      const adminData = localStorage.getItem('sr_admin_data');
      if (adminData) {
        const parsed = JSON.parse(adminData);
        return parsed.id || null;
      }
      return null;
    } catch (error) {
      return null;
    }
  },

  // Get admin name for display
  getAdminName() {
    try {
      const adminData = localStorage.getItem('sr_admin_data');
      if (adminData) {
        const parsed = JSON.parse(adminData);
        return `${parsed.first_name || ''} ${parsed.last_name || ''}`.trim() || 'System Admin';
      }
      return 'System Admin';
    } catch (error) {
      return 'System Admin';
    }
  },

  // Get admin first name only for welcome message
  getAdminFirstName() {
    try {
      // Try to get from current user first
      if (window.SR && window.SR.currentUser && window.SR.currentUser.first_name) {
        return window.SR.currentUser.first_name;
      }

      // Fallback to localStorage
      const adminData = localStorage.getItem('sr_admin_data');
      if (adminData) {
        const parsed = JSON.parse(adminData);
        return parsed.first_name || 'System Admin';
      }

      return 'System Admin';
    } catch (error) {
      return 'System Admin';
    }
  },





  // Load dashboard data
  async loadDashboardData() {
    try {
      // Load complete dashboard data from API
      const dashboardResponse = await this.fetchDashboardStats();

      if (dashboardResponse.setupRequired) {
        this.state.stats = this.getDefaultStats();
        return;
      }

      // Use real data from API and map to expected format
      const overview = dashboardResponse.overview || {};
      this.state.stats = {
        totalStudents: overview.total_students || 0,
        totalTeachers: overview.total_teachers || 0
      };
      this.state.enrollmentByLevel = dashboardResponse.class_breakdown || [];
      this.state.genderDistribution = dashboardResponse.gender_distribution || {};
      this.state.assessmentStats = dashboardResponse.assessments || {
        o_level: {},
        a_level: {}
      };

    } catch (error) {
      console.error('❌ Error loading dashboard data:', error);
      this.state.stats = this.getDefaultStats();
    }
  },

  // Fetch dashboard statistics
  async fetchDashboardStats() {
    try {
      // Build parameters including admin context
      const params = {};
      if (this.state.currentAdmin?.id) {
        params.admin_id = this.state.currentAdmin.id;
      }

      // Use the new API service
      const result = await window.DashboardAPI.getStats(params);

      if (result.success) {
        return result.data;
      } else {
        return { setupRequired: true, message: 'API not available' };
      }
    } catch (error) {
      console.warn('⚠️ Failed to fetch dashboard stats:', error);
      return { setupRequired: true, message: 'Connection error' };
    }
  },


  // Get default stats for fallback
  getDefaultStats() {
    return {
      totalStudents: 0,
      totalTeachers: 0
    };
  },





  // Render dashboard
  render() {
    const container = document.getElementById('content-area');
    if (!container) return;

    container.innerHTML = `
      <div class="space-y-6">
        <!-- Dashboard Header -->
        ${this.renderDashboardHeader()}

        <!-- Key Metrics Grid -->
        ${this.renderKeyMetrics()}

        <!-- Quick Actions -->
        ${this.renderQuickActions()}

        <!-- Analytics Section -->
        ${this.renderAnalyticsSection()}
      </div>
    `;

    // Initialize quick action event listeners
    this.initializeQuickActions();

    // Auto-hide welcome header after 7 seconds
    this.initializeWelcomeHeaderAutoHide();
  },

  // Render dashboard header
  renderDashboardHeader() {
    const adminName = this.getAdminFirstName();

    return `
      <div id="dashboard-welcome-header" class="bg-gradient-to-r from-primary-600 to-secondary-600 rounded-xl shadow-soft text-white ${SRDesignSystem.responsive.spacing.padding} transition-all duration-500">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="${SRDesignSystem.responsive.text['3xl']} font-bold mb-2">Welcome back, ${adminName}</h1>
            <p class="text-primary-100 ${SRDesignSystem.responsive.text.lg}">Continue managing assessments</p>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize welcome header auto-hide functionality
  initializeWelcomeHeaderAutoHide() {
    setTimeout(() => {
      const welcomeHeader = document.getElementById('dashboard-welcome-header');
      if (welcomeHeader) {
        // Add fade out animation
        welcomeHeader.style.opacity = '0';
        welcomeHeader.style.transform = 'translateY(-20px)';

        // Remove from DOM after animation completes
        setTimeout(() => {
          if (welcomeHeader.parentNode) {
            welcomeHeader.remove();
          }
        }, 500); // Wait for transition to complete
      }
    }, 7000); // Hide after 7 seconds
  },

  // Render key metrics with enhanced design
  renderKeyMetrics() {
    const dashboardData = this.state;
    const stats = dashboardData.stats || {};

    return `
      <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
        ${this.renderEnhancedStatCard({
          title: 'Students',
          value: stats.totalStudents || 0,
          subtitle: null,
          icon: 'fas fa-user-graduate',
          color: 'blue',
          trend: null
        })}
        ${this.renderEnhancedStatCard({
          title: 'Teachers',
          value: stats.totalTeachers || 0,
          subtitle: null,
          icon: 'fas fa-chalkboard-teacher',
          color: 'green',
          trend: null
        })}
      </div>
    `;
  },

  // Render enhanced stat card with better design
  renderEnhancedStatCard(config) {
    const { title, value, subtitle, icon, color } = config;

    const colorClasses = {
      blue: { bg: 'bg-blue-500', text: 'text-blue-600', light: 'bg-blue-50', border: 'border-blue-200' },
      green: { bg: 'bg-green-500', text: 'text-green-600', light: 'bg-green-50', border: 'border-green-200' },
      purple: { bg: 'bg-purple-500', text: 'text-purple-600', light: 'bg-purple-50', border: 'border-purple-200' },
      orange: { bg: 'bg-orange-500', text: 'text-orange-600', light: 'bg-orange-50', border: 'border-orange-200' }
    };

    const colors = colorClasses[color];

    return `
      <div class="bg-white rounded-xl ${SRDesignSystem.responsive.spacing.padding} shadow-soft border border-gray-200 hover:shadow-medium transition-all duration-300 hover:border-${color}-200">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <p class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-600 mb-2">${title}</p>
            <p class="${SRDesignSystem.responsive.text['3xl']} font-bold text-gray-900 mb-1">${value.toLocaleString()}</p>
            ${subtitle ? `<p class="${SRDesignSystem.responsive.text.sm} text-gray-500">${subtitle}</p>` : ''}
          </div>
          <div class="w-14 h-14 ${colors.bg} rounded-xl flex items-center justify-center shadow-sm ml-4">
            ${SRDesignSystem.components.icon(icon, 'xl', 'white')}
          </div>
        </div>
      </div>
    `;
  },

  // Legacy stat card method (kept for compatibility)
  renderStatCard(title, value, icon, color, subtitle) {
    return this.renderEnhancedStatCard({ title, value, subtitle, icon, color, trend: null });
  },

  // Render quick actions section
  renderQuickActions() {
    const actions = [
      {
        title: 'Register Student',
        description: 'Add new O-Level or A-Level students',
        icon: 'fas fa-user-plus',
        color: 'blue',
        page: 'register-student'
      },
      {
        title: 'Enter Scores',
        description: 'Record Continuous Assessment scores',
        icon: 'fas fa-edit',
        color: 'green',
        page: 'enter-ca-scores'
      },
      {
        title: 'Manage Streams',
        description: 'Configure class streams',
        icon: 'fas fa-layer-group',
        color: 'purple',
        page: 'manage-streams'
      },
      {
        title: 'Configure Exams',
        description: 'Set up exam types and weights',
        icon: 'fas fa-cog',
        color: 'orange',
        page: 'exam-types-management'
      },
      {
        title: 'Teacher Management',
        description: 'Manage school teachers',
        icon: 'fas fa-chalkboard-teacher',
        color: 'indigo',
        page: 'manage-teachers'
      },
      {
        title: 'Grade Scales',
        description: 'Configure O-Level grading system',
        icon: 'fas fa-sliders-h',
        color: 'pink',
        page: 'o-level-grade-boundaries'
      }
    ];

    return `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
        <div class="flex items-center justify-between mb-6">
          <h2 class="${SRDesignSystem.responsive.text.xl} font-semibold text-gray-900">Quick Actions</h2>
          ${SRDesignSystem.components.icon('fas fa-bolt', 'base', 'yellow-500')}
        </div>
        <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
          ${actions.map(action => this.renderQuickActionCard(action)).join('')}
        </div>
      </div>
    `;
  },

  // Render individual quick action card
  renderQuickActionCard(action) {
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100',
      green: 'bg-green-50 text-green-600 border-green-200 hover:bg-green-100',
      purple: 'bg-purple-50 text-purple-600 border-purple-200 hover:bg-purple-100',
      orange: 'bg-orange-50 text-orange-600 border-orange-200 hover:bg-orange-100',
      indigo: 'bg-indigo-50 text-indigo-600 border-indigo-200 hover:bg-indigo-100',
      pink: 'bg-pink-50 text-pink-600 border-pink-200 hover:bg-pink-100'
    };

    const colors = colorClasses[action.color] || colorClasses.blue;

    return `
      <div class="quick-action-card border border-gray-200 rounded-lg ${SRDesignSystem.responsive.spacing.padding} hover:shadow-md transition-all duration-200 cursor-pointer ${colors}"
           data-page="${action.page}">
        <div class="flex items-start space-x-3">
          <div class="flex-shrink-0">
            ${SRDesignSystem.components.icon(action.icon, 'lg', 'current')}
          </div>
          <div class="flex-1 min-w-0">
            <h3 class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-900 mb-1">${action.title}</h3>
            <p class="${SRDesignSystem.responsive.text.xs} text-gray-600">${action.description}</p>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize quick action event listeners
  initializeQuickActions() {
    const quickActionCards = document.querySelectorAll('.quick-action-card');
    quickActionCards.forEach(card => {
      card.addEventListener('click', () => {
        const page = card.dataset.page;
        if (page && window.PageRouter) {
          console.log(`🎯 Navigating to page: ${page}`);
          window.PageRouter.loadPage(page);

          // Update navigation to show active page
          if (window.ModernNavigation) {
            window.ModernNavigation.setActivePage(page);
          }
        } else {
          console.error('❌ PageRouter not available or page not specified');
        }
      });
    });
  },

  // Render O-Level assessment statistics section
  renderOLevelAssessmentStats() {
    const oLevelStats = this.state.assessmentStats.o_level || {};
    const totalAssessments = oLevelStats.total_assessments || 0;
    const studentsAssessed = oLevelStats.students_assessed || 0;
    const subjectsAssessed = oLevelStats.subjects_assessed || 0;
    const averageScore = parseFloat(oLevelStats.average_competency_score) || 0;
    const excellentCount = oLevelStats.excellent_assessments || 0;
    const goodCount = oLevelStats.good_assessments || 0;
    const satisfactoryCount = oLevelStats.satisfactory_assessments || 0;
    const needsImprovementCount = oLevelStats.needs_improvement_assessments || 0;

    return `
      <div class="bg-white rounded-xl ${SRDesignSystem.responsive.spacing.padding} shadow-soft border border-gray-200">
        <div class="flex items-center justify-between mb-6">
          <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">O-Level Assessment Statistics</h3>
          ${SRDesignSystem.components.icon('fas fa-clipboard-check', 'base', 'green-500')}
        </div>

        <!-- Key Metrics -->
        <div class="${SRDesignSystem.responsive.grid.cols4} ${SRDesignSystem.responsive.grid.gap} mb-6">
          <div class="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div class="${SRDesignSystem.responsive.text['2xl']} font-bold text-blue-600">${totalAssessments.toLocaleString()}</div>
            <div class="${SRDesignSystem.responsive.text.sm} text-blue-700">Total Assessments</div>
          </div>
          <div class="text-center p-3 bg-green-50 rounded-lg border border-green-200">
            <div class="${SRDesignSystem.responsive.text['2xl']} font-bold text-green-600">${studentsAssessed.toLocaleString()}</div>
            <div class="${SRDesignSystem.responsive.text.sm} text-green-700">Students Assessed</div>
          </div>
          <div class="text-center p-3 bg-purple-50 rounded-lg border border-purple-200">
            <div class="${SRDesignSystem.responsive.text['2xl']} font-bold text-purple-600">${subjectsAssessed.toLocaleString()}</div>
            <div class="${SRDesignSystem.responsive.text.sm} text-purple-700">Subjects Assessed</div>
          </div>
          <div class="text-center p-3 bg-orange-50 rounded-lg border border-orange-200">
            <div class="${SRDesignSystem.responsive.text['2xl']} font-bold text-orange-600">${averageScore.toFixed(2)}</div>
            <div class="${SRDesignSystem.responsive.text.sm} text-orange-700">Avg. Competency Score</div>
          </div>
        </div>

        <!-- Performance Distribution Chart -->
        <div class="h-64 relative">
          <canvas id="o-level-assessment-chart" class="w-full h-full"></canvas>
        </div>

        <!-- Performance Summary -->
        <div class="${SRDesignSystem.responsive.grid.cols4} ${SRDesignSystem.responsive.grid.gapSm} mt-4">
          <div class="text-center p-2 bg-emerald-50 rounded border border-emerald-200">
            <div class="${SRDesignSystem.responsive.text.lg} font-semibold text-emerald-600">${excellentCount}</div>
            <div class="${SRDesignSystem.responsive.text.xs} text-emerald-700">Excellent (≥2.5)</div>
          </div>
          <div class="text-center p-2 bg-blue-50 rounded border border-blue-200">
            <div class="${SRDesignSystem.responsive.text.lg} font-semibold text-blue-600">${goodCount}</div>
            <div class="${SRDesignSystem.responsive.text.xs} text-blue-700">Good (2.0-2.4)</div>
          </div>
          <div class="text-center p-2 bg-yellow-50 rounded border border-yellow-200">
            <div class="${SRDesignSystem.responsive.text.lg} font-semibold text-yellow-600">${satisfactoryCount}</div>
            <div class="${SRDesignSystem.responsive.text.xs} text-yellow-700">Satisfactory (1.5-1.9)</div>
          </div>
          <div class="text-center p-2 bg-red-50 rounded border border-red-200">
            <div class="${SRDesignSystem.responsive.text.lg} font-semibold text-red-600">${needsImprovementCount}</div>
            <div class="${SRDesignSystem.responsive.text.xs} text-red-700">Needs Improvement (<1.5)</div>
          </div>
        </div>
      </div>
    `;
  },

  // Render A-Level assessment statistics section
  renderALevelAssessmentStats() {
    const aLevelStats = this.state.assessmentStats.a_level || {};
    const totalAssessments = aLevelStats.total_assessments || 0;
    const studentsAssessed = aLevelStats.students_assessed || 0;
    const subjectsAssessed = aLevelStats.subjects_assessed || 0;
    const averageScore = parseFloat(aLevelStats.average_competency_score) || 0;
    const excellentCount = aLevelStats.excellent_assessments || 0;
    const goodCount = aLevelStats.good_assessments || 0;
    const satisfactoryCount = aLevelStats.satisfactory_assessments || 0;
    const needsImprovementCount = aLevelStats.needs_improvement_assessments || 0;

    return `
      <div class="bg-white rounded-xl ${SRDesignSystem.responsive.spacing.padding} shadow-soft border border-gray-200">
        <div class="flex items-center justify-between mb-6">
          <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">A-Level Assessment Statistics</h3>
          ${SRDesignSystem.components.icon('fas fa-graduation-cap', 'base', 'indigo-500')}
        </div>

        <!-- Key Metrics -->
        <div class="${SRDesignSystem.responsive.grid.cols4} ${SRDesignSystem.responsive.grid.gap} mb-6">
          <div class="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div class="${SRDesignSystem.responsive.text['2xl']} font-bold text-blue-600">${totalAssessments.toLocaleString()}</div>
            <div class="${SRDesignSystem.responsive.text.sm} text-blue-700">Total Assessments</div>
          </div>
          <div class="text-center p-3 bg-green-50 rounded-lg border border-green-200">
            <div class="${SRDesignSystem.responsive.text['2xl']} font-bold text-green-600">${studentsAssessed.toLocaleString()}</div>
            <div class="${SRDesignSystem.responsive.text.sm} text-green-700">Students Assessed</div>
          </div>
          <div class="text-center p-3 bg-purple-50 rounded-lg border border-purple-200">
            <div class="${SRDesignSystem.responsive.text['2xl']} font-bold text-purple-600">${subjectsAssessed.toLocaleString()}</div>
            <div class="${SRDesignSystem.responsive.text.sm} text-purple-700">Subjects Assessed</div>
          </div>
          <div class="text-center p-3 bg-orange-50 rounded-lg border border-orange-200">
            <div class="${SRDesignSystem.responsive.text['2xl']} font-bold text-orange-600">${averageScore.toFixed(2)}</div>
            <div class="${SRDesignSystem.responsive.text.sm} text-orange-700">Avg. Competency Score</div>
          </div>
        </div>

        <!-- Performance Distribution Chart -->
        <div class="h-64 relative">
          <canvas id="a-level-assessment-chart" class="w-full h-full"></canvas>
        </div>

        <!-- Performance Summary -->
        <div class="${SRDesignSystem.responsive.grid.cols4} ${SRDesignSystem.responsive.grid.gapSm} mt-4">
          <div class="text-center p-2 bg-emerald-50 rounded border border-emerald-200">
            <div class="${SRDesignSystem.responsive.text.lg} font-semibold text-emerald-600">${excellentCount}</div>
            <div class="${SRDesignSystem.responsive.text.xs} text-emerald-700">Excellent (≥2.5)</div>
          </div>
          <div class="text-center p-2 bg-blue-50 rounded border border-blue-200">
            <div class="${SRDesignSystem.responsive.text.lg} font-semibold text-blue-600">${goodCount}</div>
            <div class="${SRDesignSystem.responsive.text.xs} text-blue-700">Good (2.0-2.4)</div>
          </div>
          <div class="text-center p-2 bg-yellow-50 rounded border border-yellow-200">
            <div class="${SRDesignSystem.responsive.text.lg} font-semibold text-yellow-600">${satisfactoryCount}</div>
            <div class="${SRDesignSystem.responsive.text.xs} text-yellow-700">Satisfactory (1.5-1.9)</div>
          </div>
          <div class="text-center p-2 bg-red-50 rounded border border-red-200">
            <div class="${SRDesignSystem.responsive.text.lg} font-semibold text-red-600">${needsImprovementCount}</div>
            <div class="${SRDesignSystem.responsive.text.xs} text-red-700">Needs Improvement (<1.5)</div>
          </div>
        </div>
      </div>
    `;
  },

  // Render analytics section with enhanced charts
  renderAnalyticsSection() {
    return `
      <div class="space-y-6">
        <!-- O-Level Assessment Statistics -->
        ${this.renderOLevelAssessmentStats()}

        <!-- A-Level Assessment Statistics -->
        ${this.renderALevelAssessmentStats()}

        <!-- Student Distribution -->
        <div class="bg-white rounded-xl p-6 shadow-soft border border-gray-200">
          <div class="flex items-center justify-between mb-4">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">Student Distribution</h3>
            ${SRDesignSystem.components.icon('fas fa-chart-bar', 'base', 'blue-500')}
          </div>
          <div class="h-64 relative">
            <canvas id="enrollment-chart" class="w-full h-full"></canvas>
          </div>
        </div>

        <!-- Gender Distribution -->
        <div class="bg-white rounded-xl p-6 shadow-soft border border-gray-200">
          <div class="flex items-center justify-between mb-4">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">Gender Distribution</h3>
            ${SRDesignSystem.components.icon('fas fa-users', 'base', 'purple-500')}
          </div>
          <div class="h-64 relative">
            <canvas id="gender-chart" class="w-full h-full"></canvas>
          </div>
        </div>
      </div>
    `;
  },





  // Initialize charts with real data
  initializeCharts() {
    console.log('📊 Initializing dashboard charts...');

    // Destroy existing charts if they exist
    this.destroyExistingCharts();

    // Wait for DOM to be fully ready before initializing charts
    setTimeout(() => {
      this.initializeOLevelAssessmentChart();
      this.initializeALevelAssessmentChart();
      this.initializeEnrollmentChart();
      this.initializeGenderChart();
    }, 100);
  },

  // Destroy existing chart instances to prevent conflicts
  destroyExistingCharts() {
    // Destroy enrollment chart if it exists
    if (this.enrollmentChart) {
      this.enrollmentChart.destroy();
      this.enrollmentChart = null;
    }

    // Destroy gender chart if it exists
    if (this.genderChart) {
      this.genderChart.destroy();
      this.genderChart = null;
    }

    // Destroy O-Level assessment chart if it exists
    if (this.oLevelAssessmentChart) {
      this.oLevelAssessmentChart.destroy();
      this.oLevelAssessmentChart = null;
    }

    // Destroy A-Level assessment chart if it exists
    if (this.aLevelAssessmentChart) {
      this.aLevelAssessmentChart.destroy();
      this.aLevelAssessmentChart = null;
    }
  },

  // Initialize O-Level assessment chart
  initializeOLevelAssessmentChart() {
    const canvas = document.getElementById('o-level-assessment-chart');
    if (!canvas) {
      console.warn('⚠️ O-Level assessment chart canvas not found');
      return;
    }

    // Destroy existing chart if it exists
    if (this.oLevelAssessmentChart) {
      this.oLevelAssessmentChart.destroy();
      this.oLevelAssessmentChart = null;
    }

    const ctx = canvas.getContext('2d');
    const oLevelStats = this.state.assessmentStats.o_level || {};

    const excellentCount = oLevelStats.excellent_assessments || 0;
    const goodCount = oLevelStats.good_assessments || 0;
    const satisfactoryCount = oLevelStats.satisfactory_assessments || 0;
    const needsImprovementCount = oLevelStats.needs_improvement_assessments || 0;
    const total = excellentCount + goodCount + satisfactoryCount + needsImprovementCount;

    if (total === 0) {
      // Show no data message
      ctx.fillStyle = '#f9fafb';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = '#6b7280';
      ctx.font = '16px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('No O-Level assessment data available', canvas.width/2, canvas.height/2);
      return;
    }

    const data = [excellentCount, goodCount, satisfactoryCount, needsImprovementCount];
    const labels = ['Excellent (≥2.5)', 'Good (2.0-2.4)', 'Satisfactory (1.5-1.9)', 'Needs Improvement (<1.5)'];
    const colors = ['#10B981', '#3B82F6', '#F59E0B', '#EF4444']; // Green, Blue, Yellow, Red

    // Create chart using Chart.js if available
    if (window.Chart) {
      this.oLevelAssessmentChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: labels,
          datasets: [{
            data: data,
            backgroundColor: colors,
            borderWidth: 2,
            borderColor: '#ffffff'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                padding: 15,
                usePointStyle: true,
                font: {
                  size: 11
                }
              }
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const label = context.label || '';
                  const value = context.parsed || 0;
                  const total = context.dataset.data.reduce((a, b) => a + b, 0);
                  const percentage = ((value / total) * 100).toFixed(1);
                  return `${label}: ${value} (${percentage}%)`;
                }
              }
            }
          }
        }
      });
    }
  },

  // Initialize A-Level assessment chart
  initializeALevelAssessmentChart() {
    const canvas = document.getElementById('a-level-assessment-chart');
    if (!canvas) {
      console.warn('⚠️ A-Level assessment chart canvas not found');
      return;
    }

    // Destroy existing chart if it exists
    if (this.aLevelAssessmentChart) {
      this.aLevelAssessmentChart.destroy();
      this.aLevelAssessmentChart = null;
    }

    const ctx = canvas.getContext('2d');
    const aLevelStats = this.state.assessmentStats.a_level || {};

    const excellentCount = aLevelStats.excellent_assessments || 0;
    const goodCount = aLevelStats.good_assessments || 0;
    const satisfactoryCount = aLevelStats.satisfactory_assessments || 0;
    const needsImprovementCount = aLevelStats.needs_improvement_assessments || 0;
    const total = excellentCount + goodCount + satisfactoryCount + needsImprovementCount;

    if (total === 0) {
      // Show no data message
      ctx.fillStyle = '#f9fafb';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = '#6b7280';
      ctx.font = '16px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('No A-Level assessment data available', canvas.width/2, canvas.height/2);
      return;
    }

    const data = [excellentCount, goodCount, satisfactoryCount, needsImprovementCount];
    const labels = ['Excellent (≥2.5)', 'Good (2.0-2.4)', 'Satisfactory (1.5-1.9)', 'Needs Improvement (<1.5)'];
    const colors = ['#10B981', '#3B82F6', '#F59E0B', '#EF4444']; // Green, Blue, Yellow, Red

    // Create chart using Chart.js if available
    if (window.Chart) {
      this.aLevelAssessmentChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: labels,
          datasets: [{
            data: data,
            backgroundColor: colors,
            borderWidth: 2,
            borderColor: '#ffffff'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                padding: 15,
                usePointStyle: true,
                font: {
                  size: 11
                }
              }
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const label = context.label || '';
                  const value = context.parsed || 0;
                  const total = context.dataset.data.reduce((a, b) => a + b, 0);
                  const percentage = ((value / total) * 100).toFixed(1);
                  return `${label}: ${value} (${percentage}%)`;
                }
              }
            }
          }
        }
      });
    }
  },

  // Initialize enrollment chart
  initializeEnrollmentChart() {
    const canvas = document.getElementById('enrollment-chart');
    if (!canvas) {
      console.warn('⚠️ Enrollment chart canvas not found');
      return;
    }

    // Destroy existing chart if it exists
    if (this.enrollmentChart) {
      this.enrollmentChart.destroy();
      this.enrollmentChart = null;
    }

    const ctx = canvas.getContext('2d');
    const enrollmentData = this.state.enrollmentByLevel || [];

    if (enrollmentData.length === 0) {
      // Show no data message
      ctx.fillStyle = '#f9fafb';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = '#6b7280';
      ctx.font = '16px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('No enrollment data available', canvas.width/2, canvas.height/2);
      return;
    }

    // Prepare data for chart
    const labels = enrollmentData.map(level => level.level_name || level.class_name || level.level);
    const data = enrollmentData.map(level => level.student_count || 0);
    const colors = [
      '#3B82F6', '#10B981', '#8B5CF6', '#F59E0B',
      '#EF4444', '#06B6D4', '#84CC16', '#F97316'
    ];

    // Create chart using Chart.js if available
    if (window.Chart) {
      this.enrollmentChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: 'Students',
            data: data,
            backgroundColor: colors.slice(0, data.length),
            borderWidth: 1,
            borderColor: colors.slice(0, data.length).map(color => color.replace('0.8', '1')),
            borderRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                color: '#f3f4f6'
              },
              ticks: {
                font: {
                  size: 11
                }
              }
            },
            x: {
              grid: {
                display: false
              },
              ticks: {
                font: {
                  size: 11
                }
              }
            }
          },
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const label = context.label || '';
                  const value = context.parsed.y || 0;
                  return `${label}: ${value} students`;
                }
              }
            }
          }
        }
      });
    }
  },



  // Initialize gender distribution chart
  initializeGenderChart() {
    const canvas = document.getElementById('gender-chart');
    if (!canvas) {
      console.warn('⚠️ Gender chart canvas not found');
      return;
    }

    // Destroy existing chart if it exists
    if (this.genderChart) {
      this.genderChart.destroy();
      this.genderChart = null;
    }

    const ctx = canvas.getContext('2d');
    const genderData = this.state.genderDistribution || {};

    const maleStudents = genderData.male_students || 0;
    const femaleStudents = genderData.female_students || 0;
    const total = maleStudents + femaleStudents;

    if (total === 0) {
      // Show no data message
      ctx.fillStyle = '#f9fafb';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = '#6b7280';
      ctx.font = '16px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('No gender data available', canvas.width/2, canvas.height/2);
      return;
    }

    const data = [maleStudents, femaleStudents];
    const labels = ['Male Students', 'Female Students'];
    const colors = ['#3B82F6', '#EC4899']; // Blue for male, Pink for female

    // Create chart using Chart.js if available
    if (window.Chart) {
      this.genderChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: labels,
          datasets: [{
            data: data,
            backgroundColor: colors,
            borderWidth: 2,
            borderColor: '#ffffff'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                padding: 15,
                usePointStyle: true,
                font: {
                  size: 12
                }
              }
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const label = context.label || '';
                  const value = context.parsed || 0;
                  const total = context.dataset.data.reduce((a, b) => a + b, 0);
                  const percentage = ((value / total) * 100).toFixed(1);
                  return `${label}: ${value} (${percentage}%)`;
                }
              }
            }
          }
        }
      });
    }
  }


};

// Export to global scope
window.Dashboard = Dashboard;
