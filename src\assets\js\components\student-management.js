// SmartReport - Student Management Components
// Student Records: Personal information and subject assignments
// Student Enrollments: Class assignments and academic progression

// Uses global API services: window.StudentsAPI, window.ClassesAPI, etc.
// Uses global config: window.SRConfig
// Uses environment configuration: ../config/environment.js



const StudentManagementComponents = {
  // Component state
  state: {
    students: [],
    oLevelStudents: [],
    classes: [],
    subjects: [],
    academicYears: [],
    terms: [],
    combinations: [],
    currentPage: 'register-student',
    loading: false,
    filters: {
      class: '',
      status: '',
      search: ''
    }
  },

  // Main render method - shows the student management selection interface
  render() {
    // Always show the selection interface since navigation is handled by PageRouter
    return StudentManagementSelectionComponent.render();
  },

  // Initialize component
  async init() {
    // Reset state to show selection interface by default
    this.state.selectedManagementType = '';

    // Load initial data
    await this.loadInitialData();

    // Initialize event listeners directly - DOM should be ready due to lifecycle manager
    this.initializeEventListeners();
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Initialize event listeners for the selection component
    if (StudentManagementSelectionComponent.initializeEventListeners) {
      StudentManagementSelectionComponent.initializeEventListeners();
    }
  },

  // Load initial data
  async loadInitialData() {
    try {
      this.state.loading = true;

      console.log('🔄 Loading student management data...');

      // Load all required data using API service with proper error handling
      const dataPromises = [
        this.loadStudentsData(),
        this.loadOLevelStudentsData(),
        this.loadClassesData(),
        this.loadSubjectsData(),
        this.loadAcademicYearsData(),
        this.loadTermsData(),
        this.loadCombinationsData(),
        this.loadStreamsData()
      ];

      await Promise.allSettled(dataPromises);

      console.log('✅ Student management data loaded successfully');

    } catch (error) {
      console.error('❌ Failed to load initial data:', error);
      if (window.SRConfig && window.SRConfig.get('development.debugMode')) {
        console.error('Debug: Initial data loading error details:', error);
      }
      // Show error notification if available
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to load data', 'error');
      }
    } finally {
      this.state.loading = false;
    }
  },

  // Load students data with error handling
  async loadStudentsData() {
    try {
      console.log('🔄 Loading all students data...');

      // Check if StudentsAPI is available
      if (!window.StudentsAPI) {
        console.error('❌ StudentsAPI not available');
        this.state.students = { success: false, data: [] };
        return;
      }

      const result = await window.StudentsAPI.getAll();
      this.state.students = result;

      // Debug logging
      console.log('✅ Students API result:', result);
      if (result && result.success && result.data) {
        console.log('✅ Students data loaded:', result.data.length, 'students');
        console.log('📊 Sample student data:', result.data.slice(0, 2));
      } else {
        console.warn('⚠️ Unexpected students data structure:', result);
        console.warn('⚠️ Setting empty data array');
        this.state.students = { success: true, data: [] };
      }
    } catch (error) {
      console.error('❌ Failed to load students:', error);
      this.state.students = { success: false, data: [] };
    }
  },

  // Load O-Level students data with error handling
  async loadOLevelStudentsData() {
    try {
      const result = await window.StudentsAPI.oLevel.getAll();
      this.state.oLevelStudents = result;
      console.log('✅ O-Level students data loaded:', result.data?.length || 0, 'students');
    } catch (error) {
      console.error('❌ Failed to load O-Level students:', error);
      this.state.oLevelStudents = { success: false, data: [] };
    }
  },

  // Load classes data with error handling
  async loadClassesData() {
    try {
      const result = await window.ClassesAPI.getAll();
      this.state.classes = result;
      console.log('✅ Classes data loaded:', result.data?.length || 0, 'classes');
    } catch (error) {
      console.error('❌ Failed to load classes:', error);
      this.state.classes = { success: false, data: [] };
    }
  },

  // Load subjects data with error handling
  async loadSubjectsData() {
    try {
      // Load both O-Level and A-Level subjects
      const [oLevelResult, aLevelResult] = await Promise.all([
        window.SubjectsAPI.oLevel.getAll(),
        window.SubjectsAPI.aLevel.getAll()
      ]);

      this.state.subjects = {
        success: true,
        data: {
          oLevel: oLevelResult.data || [],
          aLevel: aLevelResult.data || []
        }
      };
      console.log('✅ Subjects data loaded:',
        'O-Level:', oLevelResult.data?.length || 0,
        'A-Level:', aLevelResult.data?.length || 0);
    } catch (error) {
      console.error('❌ Failed to load subjects:', error);
      this.state.subjects = { success: false, data: { oLevel: [], aLevel: [] } };
    }
  },

  // Load academic years data with error handling
  async loadAcademicYearsData() {
    try {
      const result = await window.AcademicYearsAPI.getAll();
      this.state.academicYears = result;
      console.log('✅ Academic years data loaded:', result.data?.length || 0, 'years');
    } catch (error) {
      console.error('❌ Failed to load academic years:', error);
      this.state.academicYears = { success: false, data: [] };
    }
  },

  // Load terms data with error handling
  async loadTermsData() {
    try {
      const result = await window.TermsAPI.getAll();
      this.state.terms = result;
      console.log('✅ Terms data loaded:', result.data?.length || 0, 'terms');
    } catch (error) {
      console.error('❌ Failed to load terms:', error);
      this.state.terms = { success: false, data: [] };
    }
  },

  // Load combinations data with error handling
  async loadCombinationsData() {
    try {
      // Since combinations are no longer used, return empty data
      this.state.combinations = { success: true, data: [] };
      console.log('✅ Combinations data loaded (deprecated):', 0, 'combinations');
    } catch (error) {
      console.error('❌ Failed to load combinations:', error);
      this.state.combinations = { success: false, data: [] };
    }
  },

  // Load streams data with error handling
  async loadStreamsData() {
    try {
      const result = await window.StreamsAPI.getAll();
      this.state.streams = result;
      console.log('✅ Streams data loaded:', result.data?.length || 0, 'streams');
    } catch (error) {
      console.error('❌ Failed to load streams:', error);
      this.state.streams = { success: false, data: [] };
    }
  }
};

// Student Level Selection Component (Main Entry Point)
const RegisterStudentComponent = {
  // Render level selection interface
  render() {
    return StudentLevelSelectionComponent.render();
  },

  // Initialize event listeners
  initializeEventListeners() {
    StudentLevelSelectionComponent.initializeEventListeners();
  },

  // Initialize component (required by page router)
  async init() {
    console.log('🔧 Initializing RegisterStudentComponent...');

    // Reset component state
    this.resetComponentState();

    // Check academic context first
    await window.AcademicContext.initialize();
    const activeYear = window.AcademicContext.getActiveAcademicYear();
    const activeTerm = window.AcademicContext.getActiveTerm();

    if (!activeYear || !activeTerm) {
      console.log('⚠️ No active academic year or term found - component will show error state');
      return;
    }

    // Load initial data first
    await StudentManagementComponents.loadInitialData();

    // Initialize event listeners directly - DOM should be ready due to lifecycle manager
    // Note: Page router handles rendering, we just initialize functionality
    this.initializeEventListeners();
    console.log('✅ RegisterStudentComponent initialized successfully');
  },

  // Reset component state
  resetComponentState() {
    // Reset any component-specific state if needed
    console.log('🔄 RegisterStudentComponent state reset');
  },

  // Cleanup component
  cleanup() {
    console.log('🧹 Cleaning up RegisterStudentComponent...');
    this.resetComponentState();
    console.log('✅ RegisterStudentComponent cleanup completed');
  }
};

// Student Level Selection Component
const StudentLevelSelectionComponent = {
  // Render level selection modal
  render() {
    // Check if academic context is available
    const hasAcademicContext = window.AcademicContext &&
                              window.AcademicContext.getActiveAcademicYear() &&
                              window.AcademicContext.getActiveTerm();

    if (!hasAcademicContext) {
      return this.renderAcademicContextError();
    }

    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Student Registration',
          'Select the education level for the student you want to register'
        )}

        <!-- Level Selection Cards -->
        <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap} max-w-4xl mx-auto">
          <!-- O-Level Registration Card -->
          <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow duration-300">
            <div class="p-6">
              <div class="space-y-4">
                <div class="flex items-start space-x-3">
                  <i class="fas fa-check-circle text-blue-500 mt-1"></i>
                  <div>
                    <h4 class="font-medium text-gray-900">Ordinary Level Education</h4>
                    <p class="text-sm text-gray-600">For students in classes S.1, S.2, S.3, and S.4</p>
                  </div>
                </div>
                <div class="flex items-start space-x-3">
                  <i class="fas fa-users text-blue-500 mt-1"></i>
                  <div>
                    <h4 class="font-medium text-gray-900">Stream Assignment</h4>
                    <p class="text-sm text-gray-600">Optional stream selection based on class configuration</p>
                  </div>
                </div>
                <div class="flex items-start space-x-3">
                  <i class="fas fa-book text-blue-500 mt-1"></i>
                  <div>
                    <h4 class="font-medium text-gray-900">Subject Selection</h4>
                    <p class="text-sm text-gray-600">Multiple O-Level subjects based on curriculum</p>
                  </div>
                </div>
              </div>
              <div class="mt-6">
                ${SRDesignSystem.forms.button('select-o-level', 'O-Level Student', 'primary', {
                  icon: 'fas fa-arrow-right',
                  onclick: 'StudentLevelSelectionComponent.selectOLevel()',
                  class: 'w-full flex items-center justify-center',
                  type: 'button'
                })}
              </div>
            </div>
          </div>

          <!-- A-Level Registration Card -->
          <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow duration-300">
            <div class="p-6">
              <div class="space-y-4">
                <div class="flex items-start space-x-3">
                  <i class="fas fa-check-circle text-blue-500 mt-1"></i>
                  <div>
                    <h4 class="font-medium text-gray-900">Advanced Level Education</h4>
                    <p class="text-sm text-gray-600">For students in classes S.5 and S.6</p>
                  </div>
                </div>
                <div class="flex items-start space-x-3">
                  <i class="fas fa-route text-blue-500 mt-1"></i>
                  <div>
                    <h4 class="font-medium text-gray-900">Stream Assignment</h4>
                    <p class="text-sm text-gray-600">Mandatory Arts or Sciences stream selection</p>
                  </div>
                </div>
                <div class="flex items-start space-x-3">
                  <i class="fas fa-star text-blue-500 mt-1"></i>
                  <div>
                    <h4 class="font-medium text-gray-900">Combination Selection</h4>
                    <p class="text-sm text-gray-600">3 Principal subjects plus 2 Subsidiary subjects</p>
                  </div>
                </div>
              </div>
              <div class="mt-6">
                ${SRDesignSystem.forms.button('select-a-level', 'A-Level Student', 'primary', {
                  icon: 'fas fa-arrow-right',
                  onclick: 'StudentLevelSelectionComponent.selectALevel()',
                  class: 'w-full flex items-center justify-center',
                  type: 'button'
                })}
              </div>
            </div>
          </div>
        </div>

      </div>
    `;
  },

  // Render academic context error
  renderAcademicContextError() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Student Registration',
          'Register new students'
        )}

        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-semibold text-red-900">Academic Context Required</h3>
              <p class="text-red-700 mt-2">
                You cannot register students without setting up the academic year and terms first.
              </p>
              <p class="text-red-700 mt-1">
                Please go to <strong>Academic Year → Setup</strong> to configure your academic year and terms before registering students.
              </p>
              <div class="mt-4 text-center">
                <button onclick="StudentLevelSelectionComponent.navigateToAcademicSetup()"
                        class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors">
                  <i class="fas fa-calendar-plus mr-2"></i>
                  Setup Academic Year & Terms
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize event listeners
  initializeEventListeners() {
    console.log('🔧 Initializing level selection event listeners...');

    // Add event listeners for level selection buttons
    const oLevelButton = document.getElementById('select-o-level');
    if (oLevelButton) {
      oLevelButton.addEventListener('click', () => {
        this.selectLevel('o-level');
      });
    }

    const aLevelButton = document.getElementById('select-a-level');
    if (aLevelButton) {
      aLevelButton.addEventListener('click', () => {
        this.selectLevel('a-level');
      });
    }

    console.log('✅ Level selection event listeners initialized');
  },

  // Handle level selection
  selectLevel(level) {
    console.log(`📚 Selected education level: ${level}`);

    // Navigate directly to the appropriate registration page using PageRouter
    if (level === 'o-level') {
      console.log('🎯 Navigating to O-Level registration page...');
      if (window.PageRouter) {
        window.PageRouter.loadPage('register-o-level-student');
      } else {
        console.error('❌ PageRouter not available');
      }
    } else if (level === 'a-level') {
      console.log('🎯 Navigating to A-Level registration page...');
      if (window.PageRouter) {
        window.PageRouter.loadPage('register-a-level-student');
      } else {
        console.error('❌ PageRouter not available');
      }
    }
  },

  // Navigate to academic year setup
  navigateToAcademicSetup() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('academic-year-setup');
    }
  }
};


// Manage Students Component
const ManageStudentsComponent = {
  // Render manage students interface
  render() {
    // Check if academic context is available
    const hasAcademicContext = window.AcademicContext &&
                              window.AcademicContext.getActiveAcademicYear() &&
                              window.AcademicContext.getActiveTerm();

    if (!hasAcademicContext) {
      return this.renderAcademicContextError();
    }

    // Show loading state if data is still loading
    if (StudentManagementComponents.state.loading) {
      // Use centralized loading state from page router
      const container = document.getElementById('content-area');
      if (container && window.PageRouter) {
        window.PageRouter.showLoadingState(container, 'Loading students data...');
      }
      return '';
    }

    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Student Records',
          'Personal information and subject assignments'
        )}

        <!-- Student Management Actions -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
          <div class="flex items-center justify-end mb-6">
            <div class="flex items-center space-x-3">
              ${SRDesignSystem.forms.button('add-student', 'Register Student', 'primary', {
                icon: 'fas fa-plus',
                onclick: 'ManageStudentsComponent.addStudent()'
              })}
              ${SRDesignSystem.forms.button('export', 'Export', 'secondary', {
                icon: 'fas fa-download',
                onclick: 'ManageStudentsComponent.exportStudents()'
              })}
            </div>
          </div>

          <!-- Filters and Search -->
          <!-- Search and Filters -->
          <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap}">
            <div>
              ${SRDesignSystem.forms.select('filter_class', 'Class', this.getClassOptions(), '')}
            </div>
            <div>
              ${SRDesignSystem.forms.select('filter_status', 'Status', [
                { value: '', label: 'All Status' },
                { value: 'active', label: 'Active' },
                { value: 'transferred', label: 'Transferred' },
                { value: 'graduated', label: 'Graduated' },
                { value: 'dropped', label: 'Dropped' },
                { value: 'suspended', label: 'Suspended' }
              ], '')}
            </div>
            <div>
              ${SRDesignSystem.forms.input('search', 'Search', '', {
                placeholder: 'Name or admission number...',
                icon: 'fas fa-search'
              })}
            </div>
          </div>
        </div>

        <!-- Students Table -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-4 border-b border-primary-200">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-primary-900 flex items-center">
              ${SRDesignSystem.components.icon('fas fa-users', 'base', 'primary-600')}
              <span class="ml-3">Student Records</span>
            </h3>
          </div>
          <div class="overflow-x-auto max-h-96 overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Photo</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Student Name</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Status</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-right text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Action</th>
                </tr>
              </thead>
              <tbody id="students-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Students will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  },

  // Render academic context error
  renderAcademicContextError() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Student Management',
          'Manage student records'
        )}

        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-600')}
            </div>
            <div class="ml-4">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-red-900">Academic Context Required</h3>
              <p class="text-red-700 mt-2">
                You cannot manage students without setting up the academic year and terms first.
              </p>
              <p class="text-red-700 mt-1">
                Please go to <strong>Academic Year → Setup</strong> to configure your academic year and terms before managing students.
              </p>
              <div class="mt-4 text-center">
                ${SRDesignSystem.forms.button('setup-academic', 'Setup Academic Year & Terms', 'danger', {
                  icon: 'fas fa-calendar-plus',
                  onclick: 'ManageStudentsComponent.navigateToAcademicSetup()'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize manage students component
  async init() {
    console.log('🔧 Initializing ManageStudentsComponent...');

    try {
      // Check academic context first
      await window.AcademicContext.initialize();
      const activeYear = window.AcademicContext.getActiveAcademicYear();
      const activeTerm = window.AcademicContext.getActiveTerm();

      if (!activeYear || !activeTerm) {
        console.log('⚠️ No active academic year or term found - component will show error state');
        return;
      }

      // Load initial data first
      await StudentManagementComponents.loadInitialData();
      console.log('✅ Initial data loaded for ManageStudentsComponent');

      // Re-render the component with loaded data
      this.reRenderComponent();

      // Initialize directly - DOM should be ready due to lifecycle manager
      console.log('🔄 Populating students table...');
      this.populateStudentsTable();
      this.initializeEventListeners();
      console.log('✅ ManageStudentsComponent initialized successfully');

    } catch (error) {
      console.error('❌ Error initializing ManageStudentsComponent:', error);
    }
  },

  // Reset component state
  resetComponentState() {
    // Reset any component-specific state if needed
    console.log('🔄 ManageStudentsComponent state reset');
  },

  // Re-render component with updated data
  reRenderComponent() {
    const contentArea = document.getElementById('content-area');
    if (contentArea) {
      contentArea.innerHTML = this.render();
    }
  },

  // Helper method to get proper photo URL
  getStudentPhotoUrl(passportPhoto) {
    const serverUrl = window.SRConfig.getServerUrl();

    // Handle null, undefined, empty string, or string "null"
    if (passportPhoto === null ||
        passportPhoto === undefined ||
        passportPhoto === '' ||
        passportPhoto === 'null' ||
        passportPhoto === 'undefined' ||
        (typeof passportPhoto === 'string' && passportPhoto.trim() === '')) {
      return `${serverUrl}/assets/images/default-avatar.png`;
    }

    // Convert to string and trim
    const photoPath = String(passportPhoto).trim();

    // If photo path starts with 'C:' or contains backslashes, it's an invalid path
    if (photoPath.startsWith('C:') || photoPath.includes('\\') || photoPath.startsWith('file://')) {
      return `${serverUrl}/assets/images/default-avatar.png`;
    }

    // If photo path doesn't start with '/', add it
    if (!photoPath.startsWith('/')) {
      return `${serverUrl}/${photoPath}`;
    }

    return `${serverUrl}${photoPath}`;
  },



  // Cleanup component
  cleanup() {
    console.log('🧹 Cleaning up ManageStudentsComponent...');
    this.resetComponentState();

    // Hide any open modals
    const modals = ['edit-student-modal', 'delete-student-modal'];
    modals.forEach(modalId => {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.classList.add('hidden');
      }
    });

    console.log('✅ ManageStudentsComponent cleanup completed');
  },

  // Get active students count
  getActiveStudentsCount() {
    const studentsState = StudentManagementComponents.state.students;
    let students = [];

    // Handle different data structures that might be returned
    if (studentsState && studentsState.success && Array.isArray(studentsState.data)) {
      students = studentsState.data;
    } else if (Array.isArray(studentsState)) {
      students = studentsState;
    } else if (studentsState && Array.isArray(studentsState.data)) {
      students = studentsState.data;
    }

    return students.filter(s => s.status === 'active').length;
  },

  // Get class options for filter
  getClassOptions() {
    const options = [{ value: '', label: 'All Classes' }];
    const classes = StudentManagementComponents.state.classes.data || StudentManagementComponents.state.classes;
    if (Array.isArray(classes)) {
      // Only show base classes (S.1 to S.6)
      const baseClasses = classes.filter(cls => {
        const isValidClass = cls.name?.match(/^S\.[1-6]$/);
        return isValidClass;
      });

      baseClasses.forEach(cls => {
        options.push({
          value: cls.id,
          label: cls.name
        });
      });
    }
    return options;
  },

  // Populate students table
  populateStudentsTable() {
    console.log('🔄 ManageStudentsComponent.populateStudentsTable() called');

    const tbody = document.getElementById('students-table-body');
    console.log('📋 Table body found:', !!tbody);

    const students = this.getFilteredStudents();
    console.log('👥 Students data:', students.length, 'students found');

    if (!tbody) {
      console.error('❌ Table body element not found');
      return;
    }

    if (students.length === 0) {
      // Get current filter values to provide contextual messaging
      const classFilter = document.getElementById('filter_class')?.selectedOptions[0]?.text || '';
      const statusFilter = document.getElementById('filter_status')?.selectedOptions[0]?.text || '';
      const searchTerm = document.getElementById('search')?.value || '';

      let message = 'No students found';
      let suggestion = 'Try adjusting your search criteria or add new students.';

      // Provide specific messaging based on applied filters
      if (searchTerm) {
        message = `No students found matching "${searchTerm}"`;
        suggestion = `Try a different search term or check the spelling.`;
      } else if (classFilter && classFilter !== 'All Classes' && statusFilter && statusFilter !== 'All Status') {
        message = `No ${statusFilter.toLowerCase()} students found in ${classFilter}`;
        suggestion = `Try selecting a different class or status, or check if students are enrolled.`;
      } else if (classFilter && classFilter !== 'All Classes') {
        message = `No students found in ${classFilter}`;
        suggestion = `Try selecting a different class or check if students are enrolled in this class.`;
      } else if (statusFilter && statusFilter !== 'All Status') {
        message = `No ${statusFilter.toLowerCase()} students found`;
        suggestion = `Try selecting a different status or check if there are students with this status.`;
      }

      tbody.innerHTML = `
        <tr>
          <td colspan="4" class="px-6 py-12 text-center text-gray-500">
            <div class="text-gray-400 mb-4">
              ${SRDesignSystem.components.icon('fas fa-users', '4xl', 'gray-300')}
            </div>
            <p class="${SRDesignSystem.responsive.text.lg} font-medium">${message}</p>
            <p class="${SRDesignSystem.responsive.text.sm}">${suggestion}</p>
          </td>
        </tr>
      `;
      return;
    }

    // Populate table with student data
    tbody.innerHTML = students.map(student => {
      const actions = [
        {
          label: 'View Details',
          icon: 'fas fa-eye',
          onclick: `ManageStudentsComponent.showStudentDetailsModal(${JSON.stringify(student).replace(/"/g, '&quot;')})`,
          color: 'blue'
        }
      ];

      return `
        <tr class="hover:bg-gray-50">
          <td class="${SRDesignSystem.responsive.table.cell}">
            <div class="flex items-center justify-center">
              <div class="flex-shrink-0 h-10 w-10">
                <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-200"
                     src="${ManageStudentsComponent.getStudentPhotoUrl(student.passport_photo)}"
                     alt="${student.first_name} ${student.last_name}"
                     onerror="this.src='${window.SR.serverUrl}/assets/images/default-avatar.png'; this.onerror=null;">
              </div>
            </div>
          </td>
          <td class="${SRDesignSystem.responsive.table.cell}">
            <div class="text-sm font-medium text-gray-900">
              ${student.first_name} ${student.middle_name || ''} ${student.last_name}
            </div>
          </td>
          <td class="${SRDesignSystem.responsive.table.cell}">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${this.getStatusBadgeClass(student.status)}">
              <span class="w-1.5 h-1.5 mr-1.5 rounded-full ${this.getStatusDotClass(student.status)}"></span>
              ${this.formatStatus(student.status)}
            </span>
          </td>
          <td class="${SRDesignSystem.responsive.table.cell} text-right">
            ${SRDesignSystem.tables.generateKebabMenu(`student-${student.id}`, actions, student)}
          </td>
        </tr>
      `;
    }).join('');
  },

  // Get filtered students based on search and filters
  getFilteredStudents() {
    // Get students data from the state with proper error handling
    const studentsState = StudentManagementComponents.state.students;
    let students = [];

    // Handle different data structures that might be returned
    if (studentsState && studentsState.success && Array.isArray(studentsState.data)) {
      students = studentsState.data;
    } else if (Array.isArray(studentsState)) {
      students = studentsState;
    } else if (studentsState && Array.isArray(studentsState.data)) {
      students = studentsState.data;
    } else {
      console.warn('No valid students data found in state:', studentsState);
      return [];
    }

    console.log('Filtering students:', students.length, 'total students');

    // Apply search filter
    const searchTerm = document.getElementById('search')?.value.toLowerCase() || '';
    if (searchTerm) {
      students = students.filter(student =>
        (student.first_name || '').toLowerCase().includes(searchTerm) ||
        (student.last_name || '').toLowerCase().includes(searchTerm) ||
        (student.admission_number || '').toLowerCase().includes(searchTerm)
      );
    }

    // Apply class filter
    const classFilter = document.getElementById('filter_class')?.value || '';
    if (classFilter) {
      students = students.filter(student => student.current_class_id == classFilter);
    }

    // Apply status filter
    const statusFilter = document.getElementById('filter_status')?.value || '';
    if (statusFilter) {
      students = students.filter(student => student.status === statusFilter);
    }

    console.log('Filtered students:', students.length, 'students after filtering');
    return students;
  },

  // Get status badge CSS class
  getStatusBadgeClass(status) {
    const classes = {
      'active': 'bg-green-100 text-green-800',
      'transferred': 'bg-blue-100 text-blue-800',
      'graduated': 'bg-purple-100 text-purple-800',
      'dropped': 'bg-red-100 text-red-800',
      'suspended': 'bg-yellow-100 text-yellow-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
  },

  // Get status dot CSS class
  getStatusDotClass(status) {
    const classes = {
      'active': 'bg-green-400',
      'transferred': 'bg-blue-400',
      'graduated': 'bg-purple-400',
      'dropped': 'bg-red-400',
      'suspended': 'bg-yellow-400'
    };
    return classes[status] || 'bg-gray-400';
  },

  // Format status for display (sentence case)
  formatStatus(status) {
    if (!status) return 'Unknown';

    const statusMap = {
      'active': 'Active',
      'transferred': 'Transferred',
      'graduated': 'Graduated',
      'dropped': 'Dropped',
      'suspended': 'Suspended',
      'inactive': 'Inactive'
    };

    return statusMap[status.toLowerCase()] || status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Search input with debounce for better performance
    const searchInput = document.getElementById('search');
    if (searchInput) {
      const debouncedSearch = window.ComponentLifecycleManager?.debounce(() => {
        this.populateStudentsTable();
      }, 300) || (() => this.populateStudentsTable());

      searchInput.addEventListener('input', debouncedSearch);
    }

    // Filter dropdowns
    ['filter_class', 'filter_status'].forEach(filterId => {
      const filterElement = document.getElementById(filterId);
      if (filterElement) {
        filterElement.addEventListener('change', () => {
          this.populateStudentsTable();
        });
      }
    });
  },

  // Add new student
  addStudent() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('register-student');
    }
  },





  // Manage student subjects
  async manageStudentSubjects(studentId, studentType) {
    try {
      console.log('Manage subjects for student:', studentId, 'Type:', studentType);

      // Normalize student type to match API expectations
      let normalizedStudentType = studentType;
      if (studentType === 'o-level' || studentType === 'olevel') {
        normalizedStudentType = 'o_level';
      } else if (studentType === 'a-level' || studentType === 'alevel') {
        normalizedStudentType = 'a_level';
      } else if (!studentType || studentType === 'student') {
        // Default to o_level if no type specified
        normalizedStudentType = 'o_level';
      }

      console.log('Normalized student type:', normalizedStudentType);

      // Get student details and current subjects - use correct API methods
      const [studentResult, subjectsResult] = await Promise.all([
        normalizedStudentType === 'o_level'
          ? window.StudentsAPI.oLevel.getById(studentId)
          : window.StudentsAPI.aLevel.getById(studentId),
        window.StudentsAPI.getStudentSubjects(normalizedStudentType, studentId)
      ]);

      if (studentResult.success && subjectsResult.success) {
        this.showManageSubjectsModal(studentResult.data, subjectsResult.data);
      } else {
        console.error('API Results:', { studentResult, subjectsResult });
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Failed to load student subjects', 'error');
        }
      }
    } catch (error) {
      console.error('Error loading student subjects:', error);
      if (window.SRDesignSystem?.notifications) {
        window.SRDesignSystem.notifications.show('Error loading student subjects', 'error');
      }
    }
  },



  // Show student details modal
  showStudentDetailsModal(student) {
    const modalHtml = `
      <div id="student-details-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b">
              <h3 class="text-lg font-semibold text-gray-900">Student Details</h3>
              ${SRDesignSystem.forms.button('close-student-details', '', 'ghost', {
                icon: 'fas fa-times',
                onclick: 'this.closest(\'#student-details-modal\').remove()',
                class: 'text-gray-400 hover:text-gray-600 p-2'
              })}
            </div>

            <!-- Student Information -->
            <div class="py-6">
              <!-- Student Information -->
              <div class="bg-white rounded-lg p-6">
                <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap}">
                  <!-- Photo -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Photo</label>
                    <div class="flex items-center">
                      <img class="h-16 w-16 rounded-full object-cover border-2 border-gray-200"
                           src="${ManageStudentsComponent.getStudentPhotoUrl(student.passport_photo)}"
                           alt="${student.first_name} ${student.last_name}"
                           onerror="this.src='${window.SR.serverUrl}/assets/images/default-avatar.png'; this.onerror=null;">
                    </div>
                  </div>
                  <!-- Full Name -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <p class="text-sm text-gray-900">${student.first_name} ${student.middle_name || ''} ${student.last_name}</p>
                  </div>
                  <!-- Admission No. -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Admission No.</label>
                    <p class="text-sm text-gray-900">${student.admission_number}</p>
                  </div>
                  <!-- Gender -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                    <p class="text-sm text-gray-900">${student.gender}</p>
                  </div>
                  <!-- Date of Birth -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                    <p class="text-sm text-gray-900">${student.date_of_birth ? SRDesignSystem.utils.formatDate(student.date_of_birth) : 'Not provided'}</p>
                  </div>
                  <!-- Enrollment Date -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Enrollment Date</label>
                    <p class="text-sm text-gray-900">${student.enrollment_date ? SRDesignSystem.utils.formatDate(student.enrollment_date) : 'Not provided'}</p>
                  </div>
                  <!-- Class -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Class</label>
                    <p class="text-sm text-gray-900">${student.class_name || 'Not assigned'}</p>
                  </div>
                  <!-- Stream -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Stream</label>
                    <p class="text-sm text-gray-900">${student.stream_name || 'Not assigned'}</p>
                  </div>
                  <!-- Status -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusBadgeClass(student.status)}">
                      ${this.formatStatus(student.status)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3 pt-4 border-t">
              ${SRDesignSystem.forms.button('edit-student-details', 'Edit Details', 'blue', {
                icon: 'fas fa-edit',
                onclick: `ManageStudentsComponent.showEditStudentModal(${JSON.stringify(student).replace(/"/g, '&quot;')}); this.closest('#student-details-modal').remove();`
              })}
              ${SRDesignSystem.forms.button('change-student-subjects', 'Change Subjects', 'green', {
                icon: 'fas fa-book',
                onclick: `ManageStudentsComponent.manageStudentSubjects(${student.id}, '${student.student_type}'); this.closest('#student-details-modal').remove();`
              })}
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
  },

  // Show edit student modal
  showEditStudentModal(student) {
    // Store the original student data for preserving fields not shown in the form
    window.editingStudentData = student;

    const modalHtml = `
      <div id="edit-student-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b">
              <h3 class="text-lg font-semibold text-gray-900">Edit Student Information</h3>
              ${SRDesignSystem.forms.button('close-edit-student', '', 'ghost', {
                icon: 'fas fa-times',
                onclick: 'delete window.editingStudentData; this.closest(\'#edit-student-modal\').remove()',
                class: 'text-gray-400 hover:text-gray-600 p-2'
              })}
            </div>

            <!-- Edit Form -->
            <form id="edit-student-form" class="py-4 space-y-6">
              <input type="hidden" id="edit-student-id" value="${student.id}">
              <input type="hidden" id="edit-student-type" value="${student.student_type}">

              <!-- Passport Photo Section -->
              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Passport Photo</h4>
                <div class="flex items-center space-x-4">
                  <div class="flex-shrink-0">
                    <img id="edit-student-photo-preview"
                         class="h-20 w-20 rounded-full object-cover border-2 border-gray-200"
                         src="${ManageStudentsComponent.getStudentPhotoUrl(student.passport_photo)}"
                         alt="Student Photo"
                         onerror="this.src='${window.SR.serverUrl}/assets/images/default-avatar.png'; this.onerror=null;">
                  </div>
                  <div class="flex-1">
                    <input type="file" id="edit-passport-photo" accept="image/*"
                           onchange="ManageStudentsComponent.previewEditPhoto(this)"
                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                    <p class="mt-1 text-xs text-gray-500">Upload a new passport photo (JPG, PNG - Max 2MB)</p>
                  </div>
                </div>
              </div>

              <!-- Row 1: Admission No., Status -->
              <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Admission Number</label>
                  <input type="text" id="edit-admission-number" value="${student.admission_number}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200" required>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Status</label>
                  <select id="edit-status" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
                    <option value="active" ${student.status === 'active' ? 'selected' : ''}>Active</option>
                    <option value="transferred" ${student.status === 'transferred' ? 'selected' : ''}>Transferred</option>
                    <option value="graduated" ${student.status === 'graduated' ? 'selected' : ''}>Graduated</option>
                    <option value="dropped" ${student.status === 'dropped' ? 'selected' : ''}>Dropped</option>
                    <option value="suspended" ${student.status === 'suspended' ? 'selected' : ''}>Suspended</option>
                  </select>
                </div>
              </div>

              <!-- Row 2: First Name, Middle Name, Last Name -->
              <div class="grid grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">First Name</label>
                  <input type="text" id="edit-first-name" value="${student.first_name}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200" required>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Middle Name</label>
                  <input type="text" id="edit-middle-name" value="${student.middle_name || ''}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Last Name</label>
                  <input type="text" id="edit-last-name" value="${student.last_name}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200" required>
                </div>
              </div>

              <!-- Row 3: Gender, Date of Birth -->
              <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Gender</label>
                  <select id="edit-gender" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200" required>
                    <option value="Male" ${student.gender === 'Male' ? 'selected' : ''}>Male</option>
                    <option value="Female" ${student.gender === 'Female' ? 'selected' : ''}>Female</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Date of Birth</label>
                  <input type="date" id="edit-date-of-birth" value="${student.date_of_birth ? new Date(student.date_of_birth).toISOString().split('T')[0] : ''}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
                </div>
              </div>
            </form>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3 pt-4 border-t">
              ${SRDesignSystem.forms.button('cancel-edit-student', 'Cancel', 'secondary', {
                onclick: 'delete window.editingStudentData; this.closest(\'#edit-student-modal\').remove()'
              })}
              ${SRDesignSystem.forms.button('save-student-changes', 'Save Changes', 'blue', {
                icon: 'fas fa-save',
                onclick: 'ManageStudentsComponent.saveStudentChanges()'
              })}
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Setup validation for edit form
    this.setupEditFormValidation();
  },

  // Setup validation for edit form
  setupEditFormValidation() {
    // Setup name field formatting (uppercase, letters only)
    const nameFields = ['edit-first-name', 'edit-middle-name', 'edit-last-name'];

    nameFields.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (field) {
        // Prevent non-letter characters and spaces from being typed
        field.addEventListener('keydown', (e) => {
          // Allow navigation and control keys
          const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];

          // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z
          if ((e.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(e.key.toLowerCase())) ||
              allowedKeys.includes(e.key)) {
            return;
          }

          // Prevent spaces and only allow letters (a-z, A-Z)
          if (e.key === ' ' || !/^[a-zA-Z]$/.test(e.key)) {
            e.preventDefault();
          }
        });

        // Convert to uppercase and remove any non-letter characters including spaces
        field.addEventListener('input', (e) => {
          let value = e.target.value.toUpperCase().replace(/[^A-Z]/g, '');
          e.target.value = value;
        });

        // Handle paste events - only allow letters, no spaces
        field.addEventListener('paste', (e) => {
          e.preventDefault();
          let paste = '';
          if (e.clipboardData) {
            paste = e.clipboardData.getData('text');
          }
          paste = paste.toUpperCase().replace(/[^A-Z]/g, '');
          e.target.value = paste;
        });
      }
    });

    // Setup admission number validation (letters, numbers, hyphens, forward slashes only)
    const admissionField = document.getElementById('edit-admission-number');
    if (admissionField) {
      admissionField.addEventListener('input', (e) => {
        let value = e.target.value;
        // Remove any characters that aren't letters, numbers, hyphens, or forward slashes
        value = value.replace(/[^A-Z0-9\/\-]/gi, '');
        e.target.value = value.toUpperCase();

        // Validate format
        if (value && !/^[A-Z0-9\/\-]+$/i.test(value)) {
          e.target.setCustomValidity('Admission number can only contain letters, numbers, hyphens, and forward slashes');
        } else {
          e.target.setCustomValidity('');
        }
      });
    }

    // Setup date of birth validation (age between 10-25 years)
    const dobField = document.getElementById('edit-date-of-birth');
    if (dobField) {
      dobField.addEventListener('input', (e) => {
        if (e.target.value) {
          const birthDate = new Date(e.target.value);
          const today = new Date();
          const age = today.getFullYear() - birthDate.getFullYear();

          if (isNaN(birthDate.getTime())) {
            e.target.setCustomValidity('Invalid date of birth format');
          } else if (age < 10 || age > 25) {
            e.target.setCustomValidity('Student age must be between 10 and 25 years');
          } else {
            e.target.setCustomValidity('');
          }
        } else {
          e.target.setCustomValidity('');
        }
      });
    }
  },

  // Preview photo in edit modal
  previewEditPhoto(input) {
    if (input.files && input.files[0]) {
      const file = input.files[0];

      // Validate file size (2MB max)
      if (file.size > 2 * 1024 * 1024) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('File size must be less than 2MB', 'error');
        }
        input.value = '';
        return;
      }

      // Validate file type (only JPG and PNG)
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select a JPG or PNG image file', 'error');
        }
        input.value = '';
        return;
      }

      const reader = new FileReader();
      reader.onload = function(e) {
        const preview = document.getElementById('edit-student-photo-preview');
        if (preview) {
          preview.src = e.target.result;
        }
      };
      reader.readAsDataURL(file);
    }
  },

  // Save student changes
  async saveStudentChanges() {
    try {
      const studentId = document.getElementById('edit-student-id').value;
      const studentType = document.getElementById('edit-student-type').value;

      // Handle passport photo upload if a new file is selected
      const photoInput = document.getElementById('edit-passport-photo');
      let passportPhotoPath = null;

      if (photoInput && photoInput.files && photoInput.files[0]) {
        try {
          // Show loading state
          const saveButton = document.querySelector('#edit-student-modal button[onclick*="saveStudentChanges"]');
          if (saveButton) {
            saveButton.disabled = true;
            saveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Uploading...';
          }

          // Upload the photo first
          const uploadType = studentType === 'o_level' ? 'o-level-student-photo' : 'a-level-student-photo';
          const uploadResult = await window.ImageUploadUtil.uploadImage(photoInput.files[0], uploadType);

          if (uploadResult.success) {
            passportPhotoPath = uploadResult.filePath;
          } else {
            throw new Error('Failed to upload passport photo');
          }
        } catch (uploadError) {
          console.error('Photo upload error:', uploadError);
          if (window.SRDesignSystem?.notifications) {
            window.SRDesignSystem.notifications.show('Failed to upload photo: ' + uploadError.message, 'error');
          }
          return;
        }
      }

      // Get the original student data
      const originalStudent = window.editingStudentData;
      if (!originalStudent) {
        throw new Error('Original student data not found');
      }

      // Get and format the date properly
      const dateValue = document.getElementById('edit-date-of-birth').value;
      let formattedDate = null;
      if (dateValue) {
        // Ensure the date is in YYYY-MM-DD format for the database
        formattedDate = dateValue; // HTML date input already provides YYYY-MM-DD format
      }

      // Prepare updated data with all required fields (including those not shown in the form)
      const updatedData = {
        // Form fields (user can edit these)
        first_name: document.getElementById('edit-first-name').value.trim(),
        middle_name: document.getElementById('edit-middle-name').value.trim() || null,
        last_name: document.getElementById('edit-last-name').value.trim(),
        admission_number: document.getElementById('edit-admission-number').value.trim(),
        gender: document.getElementById('edit-gender').value,
        date_of_birth: formattedDate,
        status: document.getElementById('edit-status').value,

        // Preserve existing fields that are not editable in this form
        passport_photo: originalStudent.passport_photo,
        current_class_id: originalStudent.current_class_id,
        current_academic_year_id: originalStudent.current_academic_year_id,
        current_term_id: originalStudent.current_term_id
      };

      // Override passport photo if a new one was uploaded
      if (passportPhotoPath) {
        updatedData.passport_photo = passportPhotoPath;
      }

      // For A-Level students, also include stream_id
      if (studentType === 'a_level' && originalStudent.stream_id) {
        updatedData.stream_id = originalStudent.stream_id;
      }

      // Validate required fields
      if (!updatedData.first_name || !updatedData.last_name || !updatedData.admission_number) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please fill in all required fields', 'error');
        }
        return;
      }

      // Use the correct API method based on student type
      let result;
      if (studentType === 'o_level') {
        result = await window.StudentsAPI.oLevel.update(studentId, updatedData);
      } else if (studentType === 'a_level') {
        result = await window.StudentsAPI.aLevel.update(studentId, updatedData);
      } else {
        throw new Error('Invalid student type');
      }

      if (result.success) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Student information updated successfully', 'success');
        }

        // Close modal and refresh data
        document.getElementById('edit-student-modal').remove();
        // Clean up stored data
        delete window.editingStudentData;
        await StudentManagementComponents.loadInitialData();
        this.populateStudentsTable();
      } else {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to update student', 'error');
        }
      }
    } catch (error) {
      console.error('Error saving student changes:', error);
      if (window.SRDesignSystem?.notifications) {
        window.SRDesignSystem.notifications.show('Error updating student information', 'error');
      }
    } finally {
      // Reset button state
      const saveButton = document.querySelector('#edit-student-modal button[onclick*="saveStudentChanges"]');
      if (saveButton) {
        saveButton.disabled = false;
        saveButton.innerHTML = '<i class="fas fa-save mr-2"></i>Save';
      }

      // Clean up stored data if modal is still open (in case of error)
      if (document.getElementById('edit-student-modal')) {
        // Don't clean up here as user might try again
      }
    }
  },

  // Get all subjects for a specific student type
  async getAllSubjectsForType(studentType) {
    try {
      if (studentType === 'o_level') {
        return await window.SubjectsAPI.oLevel.getAll();
      } else {
        return await window.SubjectsAPI.aLevel.getAll();
      }
    } catch (error) {
      console.error('Error fetching subjects:', error);
      return { success: false, error: error.message };
    }
  },

  // Get subjects for a specific class level with their status (compulsory/elective)
  async getSubjectsForClassLevel(studentType, classLevel, classId) {
    try {
      if (studentType === 'o_level') {
        // For O-Level, get subjects with their class-level relationships
        // We need to get the class_level_id from the class
        const classesResult = await window.ClassesAPI.getAll();
        if (!classesResult.success) {
          throw new Error('Failed to get class information');
        }

        const studentClass = classesResult.data.find(c => c.id === classId);
        if (!studentClass) {
          throw new Error('Student class not found');
        }

        // Get subjects for this class level with their status
        return await window.SubjectsAPI.oLevel.getAll({ class_level_id: studentClass.class_level_id });
      } else {
        // For A-Level, get all subjects (General Paper will be marked as compulsory)
        return await window.SubjectsAPI.aLevel.getAll();
      }
    } catch (error) {
      console.error('Error fetching subjects for class level:', error);
      return { success: false, error: error.message };
    }
  },

  // Show comprehensive subject change modal
  async showManageSubjectsModal(student, currentSubjects) {
    try {
      // Normalize student type to match API expectations
      let normalizedStudentType = student.student_type;
      if (student.student_type === 'o-level' || student.student_type === 'olevel') {
        normalizedStudentType = 'o_level';
      } else if (student.student_type === 'a-level' || student.student_type === 'alevel') {
        normalizedStudentType = 'a_level';
      } else if (!student.student_type || student.student_type === 'student') {
        // Default to o_level if no type specified
        normalizedStudentType = 'o_level';
      }

      // Get classes data first to determine student's class level
      const classesResult = await window.ClassesAPI.getAll();
      if (!classesResult.success) {
        throw new Error('Failed to load class information');
      }

      const classes = classesResult.data || [];
      const classLevel = this.getStudentClassLevel(student, classes);

      if (!classLevel) {
        throw new Error('Could not determine student class level');
      }

      // Get subjects for the specific class level and current student subjects
      const [subjectsResult, currentSubjectsResult] = await Promise.all([
        this.getSubjectsForClassLevel(normalizedStudentType, classLevel, student.current_class_id),
        window.StudentsAPI.getStudentSubjects(normalizedStudentType, student.id)
      ]);

      if (!subjectsResult.success || !currentSubjectsResult.success) {
        console.error('API Results:', { subjectsResult, currentSubjectsResult });
        throw new Error('Failed to load required data');
      }

      const subjects = subjectsResult.data || [];
      const currentSubjects = (currentSubjectsResult.data || []).map(s => s.subject_id);

      this.renderSubjectChangeModal(student, normalizedStudentType, subjects, classLevel, currentSubjects);
    } catch (error) {
      console.error('Error loading subject change modal:', error);
      if (window.SRDesignSystem?.notifications) {
        window.SRDesignSystem.notifications.show('Failed to load subject change interface', 'error');
      }
    }
  },

  // Get student's class level information
  getStudentClassLevel(student, classes) {
    const studentClass = classes.find(c => c.id === student.current_class_id);
    if (!studentClass) return null;

    // Determine class level rules based on class name
    const className = studentClass.name;

    // O-Level classes
    if (className.match(/^S\.[12]$/)) {
      return {
        code: 'S1-S2',
        canChangeCompulsory: false, // Cannot change compulsory subjects
        maxElectives: 2, // S1-S2: 10 compulsory + 2 electives = 12 total
        compulsoryCount: 10, // S1-S2 compulsory subjects
        totalRequired: 12,
        level: 'o_level'
      };
    } else if (className.match(/^S\.[34]$/)) {
      return {
        code: 'S3-S4',
        canChangeCompulsory: false, // Cannot change compulsory subjects
        maxElectives: 1, // S3-S4: 7 compulsory + 1 elective = 8 total (can be 9)
        compulsoryCount: 7, // S3-S4 compulsory subjects
        totalRequired: 8, // Minimum required (can be 9)
        level: 'o_level'
      };
    }
    // A-Level classes
    else if (className.match(/^S\.[56]$/)) {
      return {
        code: 'S5-S6',
        canChangeCompulsory: false, // Cannot change General Paper
        maxPrincipal: 3, // Must select exactly 3 principal subjects
        maxSubsidiary: 1, // Must select exactly 1 subsidiary subject
        compulsoryCount: 1, // Only General Paper is compulsory
        totalRequired: 5, // 3 Principal + 2 Subsidiary (including GP)
        level: 'a_level'
      };
    }
    return null;
  },

  // Render subject change modal
  renderSubjectChangeModal(student, studentType, subjects, classLevel, currentSubjects = []) {
    const isOLevel = studentType === 'o_level';

    // Categorize subjects based on student type and class level
    let compulsorySubjects = [];
    let electiveSubjects = [];
    let subsidiarySubjects = [];

    if (isOLevel) {
      // For O-Level: Use database subject-class relationships to determine compulsory vs elective
      // Compulsory subjects are those marked as 'compulsory' for the student's class level
      compulsorySubjects = subjects.filter(s => s.subject_status === 'compulsory');

      // Elective subjects are those marked as 'elective' for the student's class level
      electiveSubjects = subjects.filter(s => s.subject_status === 'elective');
    } else {
      // For A-Level: General Paper is compulsory and not changeable (exclude from modal)
      compulsorySubjects = subjects.filter(s => s.name === 'General Paper' || s.short_name === 'GP');

      // Principal and Subsidiary subjects (excluding General Paper which is compulsory)
      electiveSubjects = subjects.filter(s => s.subject_type === 'Principal');
      subsidiarySubjects = subjects.filter(s => s.subject_type === 'Subsidiary' && s.name !== 'General Paper' && s.short_name !== 'GP');
    }

    const modalHtml = `
      <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" id="subject-change-modal">
        <div class="relative top-10 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b">
              <h3 class="text-lg font-semibold text-gray-900">
                Change Subjects for - ${student.first_name} ${student.last_name}
              </h3>
              ${SRDesignSystem.forms.button('close-subject-change', '', 'ghost', {
                icon: 'fas fa-times',
                onclick: 'document.getElementById(\'subject-change-modal\').remove()',
                class: 'text-gray-400 hover:text-gray-600 p-2'
              })}
            </div>

            <form id="subject-change-form" class="space-y-6">
              <input type="hidden" name="student_id" value="${student.id}">
              <input type="hidden" name="student_type" value="${studentType}">

              ${isOLevel ? this.renderOLevelSubjectChangeForm(compulsorySubjects, electiveSubjects, currentSubjects, classLevel) :
                           this.renderALevelSubjectChangeForm(electiveSubjects, subsidiarySubjects, currentSubjects, classLevel)}

              <!-- Action Buttons -->
              <div class="flex justify-end space-x-3 pt-4 border-t">
                ${SRDesignSystem.forms.button('cancel-subject-changes', 'Cancel', 'secondary', {
                  type: 'button',
                  onclick: 'document.getElementById(\'subject-change-modal\').remove()'
                })}
                ${SRDesignSystem.forms.button('save-subject-changes', 'Save Changes', 'blue', {
                  icon: 'fas fa-save',
                  type: 'submit'
                })}
              </div>
            </form>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    this.initializeSubjectChangeModal(isOLevel, classLevel);
  },

  // Render O-Level subject change form
  renderOLevelSubjectChangeForm(compulsorySubjects, electiveSubjects, currentSubjects, classLevel) {
    const canChangeCompulsory = classLevel?.canChangeCompulsory || false;
    const maxElectives = classLevel?.maxElectives || 2;

    // Group elective subjects by type (handle both naming conventions)
    const religiousSubjects = electiveSubjects.filter(s =>
      s.subject_type === 'elective_religious' || s.subject_type === 'Religious Education'
    );
    const languageSubjects = electiveSubjects.filter(s =>
      s.subject_type === 'elective_language' || s.subject_type === 'Language'
    );
    const practicalSubjects = electiveSubjects.filter(s =>
      s.subject_type === 'elective_practical' || s.subject_type === 'Practical (pre-vocational)'
    );

    return `
      <div class="space-y-6">
        <!-- Elective Subjects Only -->
        <div>
          <h4 class="text-lg font-medium text-gray-900 mb-3">Elective Subjects (Select up to ${maxElectives})</h4>

          <!-- Religious Education -->
          <div class="mb-4">
            <h5 class="text-md font-medium text-gray-800 mb-2">Religious Education Subjects</h5>
            <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gapSm}">
              ${religiousSubjects.map(subject => `
                <div class="flex items-center p-3 border border-gray-200 rounded-md">
                  <input type="checkbox"
                         name="selected_subjects"
                         value="${subject.id}"
                         ${currentSubjects.includes(subject.id) ? 'checked' : ''}
                         data-group="religious"
                         class="mr-3 elective-checkbox">
                  <label class="text-sm font-medium text-gray-700">${subject.name}</label>
                </div>
              `).join('')}
            </div>
          </div>

          <!-- Language Subjects -->
          <div class="mb-4">
            <h5 class="text-md font-medium text-gray-800 mb-2">Language Subjects</h5>
            <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gapSm}">
              ${languageSubjects.map(subject => `
                <div class="flex items-center p-3 border border-gray-200 rounded-md">
                  <input type="checkbox"
                         name="selected_subjects"
                         value="${subject.id}"
                         ${currentSubjects.includes(subject.id) ? 'checked' : ''}
                         data-group="language"
                         class="mr-3 elective-checkbox">
                  <label class="text-sm font-medium text-gray-700">${subject.name}</label>
                </div>
              `).join('')}
            </div>
          </div>

          <!-- Practical Subjects -->
          <div class="mb-4">
            <h5 class="text-md font-medium text-gray-800 mb-2">Practical (Pre-vocational) Subjects</h5>
            <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gapSm}">
              ${practicalSubjects.map(subject => `
                <div class="flex items-center p-3 border border-gray-200 rounded-md">
                  <input type="checkbox"
                         name="selected_subjects"
                         value="${subject.id}"
                         ${currentSubjects.includes(subject.id) ? 'checked' : ''}
                         data-group="practical"
                         class="mr-3 elective-checkbox">
                  <label class="text-sm font-medium text-gray-700">${subject.name}</label>
                </div>
              `).join('')}
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Render A-Level subject change form
  renderALevelSubjectChangeForm(principalSubjects, subsidiarySubjects, currentSubjects, classLevel) {
    return `
      <div class="space-y-6">

        <!-- Principal Subjects -->
        <div>
          <h4 class="text-lg font-medium text-gray-900 mb-3">Principal Subjects (Select exactly 3)</h4>
          <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gapSm}">
            ${principalSubjects.map(subject => `
              <div class="flex items-center p-3 border border-gray-200 rounded-md">
                <input type="checkbox"
                       name="selected_subjects"
                       value="${subject.id}"
                       ${currentSubjects.includes(subject.id) ? 'checked' : ''}
                       class="mr-3 principal-checkbox">
                <label class="text-sm font-medium text-gray-700">${subject.name}</label>
              </div>
            `).join('')}
          </div>
          <div id="principal-count-message" class="mt-2 text-sm text-gray-600"></div>
        </div>

        <!-- Subsidiary Subjects -->
        <div>
          <h4 class="text-lg font-medium text-gray-900 mb-3">Subsidiary Subjects (Select exactly 1)</h4>
          <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gapSm}">
            ${subsidiarySubjects.map(subject => `
              <div class="flex items-center p-3 border border-gray-200 rounded-md">
                <input type="radio"
                       name="subsidiary_subject"
                       value="${subject.id}"
                       ${currentSubjects.includes(subject.id) ? 'checked' : ''}
                       class="mr-3 subsidiary-radio">
                <label class="text-sm font-medium text-gray-700">${subject.name}</label>
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    `;
  },

  // Initialize subject change modal
  initializeSubjectChangeModal(isOLevel, classLevel) {
    const form = document.getElementById('subject-change-form');
    if (form) {
      form.addEventListener('submit', this.handleSubjectChangeSubmit.bind(this));
    }

    if (isOLevel) {
      this.initializeOLevelSubjectValidation(classLevel);
    } else {
      this.initializeALevelSubjectValidation();
    }
  },

  // Initialize O-Level subject validation
  initializeOLevelSubjectValidation(classLevel) {
    const electiveCheckboxes = document.querySelectorAll('.elective-checkbox');

    electiveCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', (event) => {
        this.enforceGroupSelectionLimits(event.target);
        this.validateElectiveSelection(classLevel);
      });
    });
  },

  // Enforce one subject per group rule (same logic as O-Level registration)
  enforceGroupSelectionLimits(changedCheckbox) {
    const subjectGroup = changedCheckbox.getAttribute('data-group');

    if (subjectGroup) {
      const groupCheckboxes = document.querySelectorAll(`.elective-checkbox[data-group="${subjectGroup}"]`);

      if (changedCheckbox.checked) {
        // If a checkbox in a group is checked, uncheck and disable all other checkboxes in the same group
        groupCheckboxes.forEach(checkbox => {
          if (checkbox !== changedCheckbox) {
            checkbox.checked = false;
            checkbox.disabled = true;
            checkbox.parentElement.classList.add('opacity-50', 'cursor-not-allowed');
          }
        });
      } else {
        // If a checkbox in a group is unchecked, re-enable all other checkboxes in the same group
        groupCheckboxes.forEach(checkbox => {
          if (checkbox !== changedCheckbox) {
            checkbox.disabled = false;
            checkbox.parentElement.classList.remove('opacity-50', 'cursor-not-allowed');
          }
        });
      }
    }
  },

  // Validate and enforce elective selection limits based on class level
  validateElectiveSelection(classLevel) {
    const selectedElectives = document.querySelectorAll('.elective-checkbox:checked');
    let maxElectives;

    // Determine max electives based on class level
    if (classLevel?.code === 'S1-S2') {
      maxElectives = 2; // S1-S2: 10 compulsory + 2 electives = 12 total
    } else if (classLevel?.code === 'S3-S4') {
      maxElectives = 1; // S3-S4: 7 compulsory + 1 elective = 8 total (can be 9 with optional)
    } else {
      maxElectives = 2; // Default fallback
    }

    if (selectedElectives.length >= maxElectives) {
      // Disable other elective checkboxes that are not already disabled by group rules
      const unselectedElectives = document.querySelectorAll('.elective-checkbox:not(:checked):not([disabled])');
      unselectedElectives.forEach(checkbox => {
        checkbox.disabled = true;
        checkbox.parentElement.classList.add('opacity-50', 'cursor-not-allowed');
        checkbox.setAttribute('data-disabled-by-limit', 'true');
      });
    } else {
      // Re-enable elective checkboxes that were disabled by limit (but not by group rules)
      const limitDisabledCheckboxes = document.querySelectorAll('.elective-checkbox[data-disabled-by-limit="true"]');
      limitDisabledCheckboxes.forEach(checkbox => {
        checkbox.disabled = false;
        checkbox.parentElement.classList.remove('opacity-50', 'cursor-not-allowed');
        checkbox.removeAttribute('data-disabled-by-limit');
      });
    }
  },

  // Initialize A-Level subject validation (same logic as A-Level registration)
  initializeALevelSubjectValidation() {
    // Principal subjects validation (exactly 3)
    const principalCheckboxes = document.querySelectorAll('.principal-checkbox');
    principalCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        this.validateALevelSelection();
      });
    });

    // Subsidiary subject validation (exactly 1) - radio buttons
    const subsidiaryRadios = document.querySelectorAll('.subsidiary-radio');
    subsidiaryRadios.forEach(radio => {
      radio.addEventListener('change', () => {
        this.validateALevelSelection();
      });
    });

    // Initial validation
    this.validateALevelSelection();
  },

  // Validate A-Level subject selection (same logic as A-Level registration)
  validateALevelSelection() {
    // Count all checked principal subjects
    const selectedPrincipal = document.querySelectorAll('.principal-checkbox:checked');

    // Get all principal checkboxes
    const allPrincipalCheckboxes = document.querySelectorAll('.principal-checkbox');

    if (selectedPrincipal.length >= 3) {
      // Disable remaining principal checkboxes if 3 are selected
      allPrincipalCheckboxes.forEach(checkbox => {
        if (!checkbox.checked) {
          checkbox.disabled = true;
          checkbox.parentElement.classList.add('opacity-50', 'cursor-not-allowed');
        }
      });
    } else {
      // Re-enable all principal checkboxes when under the limit
      allPrincipalCheckboxes.forEach(checkbox => {
        checkbox.disabled = false;
        checkbox.parentElement.classList.remove('opacity-50', 'cursor-not-allowed');
      });
    }

    // Update count message
    const messageElement = document.getElementById('principal-count-message');
    if (messageElement) {
      const count = selectedPrincipal.length;
      if (count < 3) {
        messageElement.textContent = `Selected: ${count}/3 - Please select ${3 - count} more principal subject(s)`;
        messageElement.className = 'mt-2 text-sm text-orange-600';
      } else if (count === 3) {
        messageElement.textContent = `Selected: ${count}/3 - Perfect! You have selected exactly 3 principal subjects`;
        messageElement.className = 'mt-2 text-sm text-green-600';
      } else {
        messageElement.textContent = `Selected: ${count}/3 - Too many! Please unselect ${count - 3} principal subject(s)`;
        messageElement.className = 'mt-2 text-sm text-red-600';
      }
    }

    // Validate subsidiary subject selection
    const selectedSubsidiary = document.querySelector('.subsidiary-radio:checked');

    // Show subsidiary validation message if needed
    const subsidiaryError = document.getElementById('subsidiary-subjects-error');
    if (subsidiaryError) {
      if (!selectedSubsidiary) {
        subsidiaryError.classList.remove('hidden');
        const errorMessage = subsidiaryError.querySelector('#subsidiary-subjects-error-message');
        if (errorMessage) {
          errorMessage.textContent = 'Please select 1 subsidiary subject';
        }
      } else {
        subsidiaryError.classList.add('hidden');
      }
    }
  },

  // Handle subject change form submission
  async handleSubjectChangeSubmit(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const studentId = formData.get('student_id');
    const studentType = formData.get('student_type');
    const selectedElectiveSubjects = Array.from(formData.getAll('selected_subjects')).map(id => parseInt(id));

    // For A-Level students, also get the subsidiary subject
    const subsidiarySubject = formData.get('subsidiary_subject');
    if (subsidiarySubject && studentType === 'a_level') {
      selectedElectiveSubjects.push(parseInt(subsidiarySubject));
    }

    // For O-Level students, we need to include compulsory subjects
    let allSelectedSubjects = [...selectedElectiveSubjects];

    if (studentType === 'o_level') {
      // Get current student's compulsory subjects and add them
      const student = await this.getStudentDetails(studentType, studentId);
      if (student) {
        const classesResult = await window.ClassesAPI.getAll();
        if (classesResult.success) {
          const classes = classesResult.data || [];
          const classLevel = this.getStudentClassLevel(student, classes);

          // Get all subjects for this class level to find compulsory ones
          const subjectsResult = await this.getSubjectsForClassLevel(studentType, classLevel, student.current_class_id);
          if (subjectsResult.success) {
            const compulsorySubjects = (subjectsResult.data || [])
              .filter(s => s.subject_status === 'compulsory')
              .map(s => s.id);

            // Add compulsory subjects to the selection
            allSelectedSubjects = [...compulsorySubjects, ...selectedElectiveSubjects];
          }
        }
      }
    } else if (studentType === 'a_level') {
      // For A-Level, always preserve General Paper (it's compulsory and unchangeable)
      const student = await this.getStudentDetails(studentType, studentId);
      if (student) {
        const classesResult = await window.ClassesAPI.getAll();
        if (classesResult.success) {
          const classes = classesResult.data || [];
          const classLevel = this.getStudentClassLevel(student, classes);

          // Get all subjects for this class level to find General Paper
          const subjectsResult = await this.getSubjectsForClassLevel(studentType, classLevel, student.current_class_id);
          if (subjectsResult.success) {
            const generalPaper = (subjectsResult.data || [])
              .find(s => s.name === 'General Paper' || s.short_name === 'GP');

            if (generalPaper) {
              // Always include General Paper in A-Level subject selection
              allSelectedSubjects = [generalPaper.id, ...selectedElectiveSubjects];
            }
          }
        }
      }
    }

    const selectedSubjects = allSelectedSubjects;

    // Debug: Log collected subjects
    console.log('🔧 Form submission debug:', {
      studentType,
      studentId,
      selectedElectiveSubjects,
      allSelectedSubjects,
      finalSelectedSubjects: selectedSubjects
    });

    // Validate subject selection
    const isValid = await this.validateSubjectSelection(studentType, studentId);
    if (!isValid) {
      return;
    }

    try {
      // Get student's current class level ID
      const student = await this.getStudentDetails(studentType, studentId);
      if (!student) {
        throw new Error('Student not found');
      }

      // Get class level ID from the student's current class
      const classesResult = await window.ClassesAPI.getAll();
      if (!classesResult.success) {
        throw new Error('Failed to load class information');
      }

      const classes = classesResult.data || [];
      const studentClass = classes.find(c => c.id === student.current_class_id);
      if (!studentClass) {
        throw new Error('Student class not found');
      }

      const classLevelId = studentClass.class_level_id;

      // Debug: Log the data being sent
      console.log('🔧 Updating student subjects with data:', {
        studentType,
        studentId,
        selectedSubjects,
        classLevelId,
        dataToSend: {
          subject_ids: selectedSubjects,
          class_level_id: classLevelId
        }
      });

      // Update student subjects using the new API
      const result = await window.StudentsAPI.updateStudentSubjects(studentType, studentId, {
        subject_ids: selectedSubjects,
        class_level_id: classLevelId
      });

      console.log('🔧 API Response:', result);

      if (result.success) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Student subjects updated successfully!', 'success');
        }
        document.getElementById('subject-change-modal').remove();
        await StudentManagementComponents.loadInitialData();
        this.populateStudentsTable();
      } else {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to update subjects', 'error');
        }
      }
    } catch (error) {
      console.error('Subject change error:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to update student subjects', 'error');
      }
    }
  },

  // Get student details
  async getStudentDetails(studentType, studentId) {
    try {
      const result = studentType === 'o_level'
        ? await window.StudentsAPI.oLevel.getById(studentId)
        : await window.StudentsAPI.aLevel.getById(studentId);

      if (result.success) {
        return result.data;
      }
      return null;
    } catch (error) {
      console.error('Error getting student details:', error);
      return null;
    }
  },

  // Validate subject selection based on class level requirements
  async validateSubjectSelection(studentType, studentId) {
    if (studentType === 'o_level') {
      // For O-Level, validate elective count and group restrictions
      const electiveCheckboxes = document.querySelectorAll('.elective-checkbox:checked');
      const groups = {};

      electiveCheckboxes.forEach(checkbox => {
        const group = checkbox.dataset.group;
        if (!groups[group]) groups[group] = 0;
        groups[group]++;
      });

      // Check if any group has more than 1 selection
      for (const [group, count] of Object.entries(groups)) {
        if (count > 1) {
          if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
            window.SRDesignSystem.notifications.show(
              `You can only select 1 subject from ${group} group`,
              'error'
            );
          }
          return false;
        }
      }

      // Get student's class level to validate total subject count
      try {
        const student = await this.getStudentDetails(studentType, studentId);
        if (student) {
          const classesResult = await window.ClassesAPI.getAll();
          if (classesResult.success) {
            const classes = classesResult.data || [];
            const classLevel = this.getStudentClassLevel(student, classes);

            // Get all subjects for this class level to count compulsory ones
            const subjectsResult = await this.getSubjectsForClassLevel(studentType, classLevel, student.current_class_id);
            if (subjectsResult.success) {
              const compulsoryCount = (subjectsResult.data || [])
                .filter(s => s.subject_status === 'compulsory').length;
              const electiveCount = electiveCheckboxes.length;
              const totalSubjects = compulsoryCount + electiveCount;

              if (classLevel?.code === 'S1-S2') {
                // S1-S2: Must have exactly 12 subjects
                if (totalSubjects !== 12) {
                  if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
                    window.SRDesignSystem.notifications.show(
                      `S.1-S.2 students must have exactly 12 subjects (${compulsoryCount} compulsory + ${12 - compulsoryCount} electives). Currently: ${totalSubjects}`,
                      'error'
                    );
                  }
                  return false;
                }
              } else if (classLevel?.code === 'S3-S4') {
                // S3-S4: Must have 8 or 9 subjects (can select 1 elective)
                if (totalSubjects < 8 || totalSubjects > 9) {
                  if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
                    window.SRDesignSystem.notifications.show(
                      `S.3-S.4 students must have 8-9 subjects (${compulsoryCount} compulsory + up to 1 elective). Currently: ${totalSubjects}`,
                      'error'
                    );
                  }
                  return false;
                }
              }
            }
          }
        }
      } catch (error) {
        console.error('Error validating O-Level subject count:', error);
      }

      return true;
    } else {
      // For A-Level, validate exactly 3 principal subjects and 1 subsidiary subject (5 total)
      const principalCheckboxes = document.querySelectorAll('.principal-checkbox:checked');
      const subsidiaryRadio = document.querySelector('.subsidiary-radio:checked');

      if (principalCheckboxes.length !== 3) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(
            'You must select exactly 3 principal subjects for A-Level',
            'error'
          );
        }
        return false;
      }

      if (!subsidiaryRadio) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(
            'Please select 1 subsidiary subject. General Paper is automatically included.',
            'error'
          );
        }
        return false;
      }

      // A-Level must have exactly 5 subjects total (3 principal + 2 subsidiary including GP)
      const totalSubjects = principalCheckboxes.length + 2; // +2 for subsidiary + GP
      if (totalSubjects !== 5) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(
            'A-Level students must have exactly 5 subjects (3 Principal + 2 Subsidiary including General Paper)',
            'error'
          );
        }
        return false;
      }

      return true;
    }
  },



  // Export students
  exportStudents() {
    // Implementation for exporting students data
    console.log('Export students');
  },

  // Navigate to academic year setup
  navigateToAcademicSetup() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('academic-year-setup');
    }
  }
};


// Student Enrollment Component
const StudentEnrollmentComponent = {
  // Render student enrollment interface
  render() {
    // Check if academic context is available
    const hasAcademicContext = window.AcademicContext &&
                              window.AcademicContext.getActiveAcademicYear() &&
                              window.AcademicContext.getActiveTerm();

    if (!hasAcademicContext) {
      return this.renderAcademicContextError();
    }

    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Student Enrollments',
          'Class assignments and academic progression'
        )}

        <!-- Enrollment Management Actions -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="flex items-center justify-end mb-6">
            <div class="flex space-x-3">
              ${SRDesignSystem.forms.button('bulk-stream-assignment', 'Bulk Stream Assignment', 'secondary', {
                icon: 'fas fa-exchange-alt',
                onclick: 'StudentEnrollmentComponent.showBulkStreamAssignmentModal()',
                title: 'Assign or change streams for multiple students'
              })}
              ${SRDesignSystem.forms.button('bulk-class-promotion', 'Bulk Class Promotion', 'primary', {
                icon: 'fas fa-graduation-cap',
                onclick: 'StudentEnrollmentComponent.showBulkClassPromotionModal()',
                title: 'Promote students to new class and stream in next academic year'
              })}
            </div>
          </div>

          <!-- Search and Filters -->
          <div class="space-y-4">
            <!-- First Row: Academic Year and Term -->
            <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
              <div>
                ${SRDesignSystem.forms.select('filter_academic_year', 'Academic Year', [], '', {
                  onchange: 'StudentEnrollmentComponent.applyFilters()'
                })}
              </div>
              <div>
                ${SRDesignSystem.forms.select('filter_term', 'Term', [], '', {
                  onchange: 'StudentEnrollmentComponent.applyFilters()'
                })}
              </div>
            </div>

            <!-- Second Row: Search and Class -->
            <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
              <div>
                ${SRDesignSystem.forms.select('filter_class', 'Class', [], '', {
                  onchange: 'StudentEnrollmentComponent.applyFilters()'
                })}
              </div>
              <div>
                ${SRDesignSystem.forms.input('search', 'Search', '', {
                  placeholder: 'Name or admission number...',
                  icon: 'fas fa-search'
                })}
              </div>
            </div>
          </div>
        </div>

        <!-- Current Enrollments Table -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-4 border-b border-primary-200">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-primary-900 flex items-center">
              ${SRDesignSystem.components.icon('fas fa-table', 'base', 'primary-600')}
              <span class="ml-3">Current Enrollments</span>
            </h3>
          </div>
          <div class="overflow-x-auto max-h-96 overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Photo</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Student Name</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Class</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Stream</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-right text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Actions</th>
                </tr>
              </thead>
              <tbody id="current-enrollments-container" class="bg-white divide-y divide-gray-200">
                <!-- Current enrollments will be loaded here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

      ${this.renderBulkModals()}
    `;
  },

  // Render academic context error
  renderAcademicContextError() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Student Enrollments',
          'Manage student enrollments'
        )}

        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-600')}
            </div>
            <div class="ml-4">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-red-900">Academic Context Required</h3>
              <p class="text-red-700 mt-2">
                You cannot manage student enrollments without setting up the academic year and terms first.
              </p>
              <p class="text-red-700 mt-1">
                Please go to <strong>Academic Year → Setup</strong> to configure your academic year and terms before managing enrollments.
              </p>
              <div class="mt-4 text-center">
                ${SRDesignSystem.forms.button('setup-academic-enrollment', 'Setup Academic Year & Terms', 'danger', {
                  icon: 'fas fa-calendar-plus',
                  onclick: 'StudentEnrollmentComponent.navigateToAcademicSetup()'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize component
  async init() {
    console.log('🔧 Initializing Student Enrollment Component...');

    // Check academic context first
    await window.AcademicContext.initialize();
    const activeYear = window.AcademicContext.getActiveAcademicYear();
    const activeTerm = window.AcademicContext.getActiveTerm();

    if (!activeYear || !activeTerm) {
      console.log('⚠️ No active academic year or term found - component will show error state');
      return;
    }

    // Load initial data first
    await StudentManagementComponents.loadInitialData();

    // Initialize directly - DOM should be ready due to lifecycle manager
    console.log('🔄 Populating Student Enrollment UI...');
    this.populateDropdowns();
    this.setDefaultAcademicContext();
    this.loadCurrentEnrollments();
    this.initializeEventListeners();
    console.log('✅ Student Enrollment Component initialized successfully');
  },

  // Set default academic context in filters
  setDefaultAcademicContext() {
    const activeYear = window.AcademicContext.getActiveAcademicYear();
    const activeTerm = window.AcademicContext.getActiveTerm();

    // Set default academic year filter
    const academicYearSelect = document.getElementById('filter_academic_year');
    if (academicYearSelect && activeYear) {
      academicYearSelect.value = activeYear.id;
    }

    // Set default term filter
    const termSelect = document.getElementById('filter_term');
    if (termSelect && activeTerm) {
      termSelect.value = activeTerm.id;
    }
  },

  // Populate filter dropdowns
  populateDropdowns() {
    // Populate academic years filter
    const academicYearSelect = document.getElementById('filter_academic_year');
    if (academicYearSelect) {
      academicYearSelect.innerHTML = '<option value="">All Academic Years</option>';
      const academicYears = StudentManagementComponents.state.academicYears.data || StudentManagementComponents.state.academicYears;
      if (Array.isArray(academicYears)) {
        academicYears.forEach(year => {
          const option = document.createElement('option');
          option.value = year.id;
          option.textContent = year.name;
          academicYearSelect.appendChild(option);
        });
      }
    }

    // Populate terms filter
    const termSelect = document.getElementById('filter_term');
    if (termSelect) {
      termSelect.innerHTML = '<option value="">All Terms</option>';
      const terms = StudentManagementComponents.state.terms.data || StudentManagementComponents.state.terms;
      if (Array.isArray(terms)) {
        terms.forEach(term => {
          const option = document.createElement('option');
          option.value = term.id;
          option.textContent = term.name;
          termSelect.appendChild(option);
        });
      }
    }

    // Populate classes filter
    const classSelect = document.getElementById('filter_class');
    if (classSelect) {
      classSelect.innerHTML = '<option value="">All Classes</option>';
      const classes = StudentManagementComponents.state.classes.data || StudentManagementComponents.state.classes;
      if (Array.isArray(classes)) {
        classes.forEach(cls => {
          const option = document.createElement('option');
          option.value = cls.id;
          option.textContent = cls.name;
          classSelect.appendChild(option);
        });
      }
    }

    // Add event listeners for enrollment filters
    const enrollmentSearchInput = document.getElementById('enrollment_search');
    if (enrollmentSearchInput) {
      const debouncedSearch = window.ComponentLifecycleManager?.debounce(() => {
        this.loadCurrentEnrollments();
      }, 300) || (() => this.loadCurrentEnrollments());

      enrollmentSearchInput.addEventListener('input', debouncedSearch);
    }

    // Filter dropdowns for enrollments
    ['filter_class', 'filter_academic_year', 'filter_term'].forEach(filterId => {
      const filterElement = document.getElementById(filterId);
      if (filterElement) {
        filterElement.addEventListener('change', () => {
          this.loadCurrentEnrollments();
        });
      }
    });
  },

  // Apply filters to enrollment list
  applyFilters() {
    this.loadCurrentEnrollments();
  },

  // Load current enrollments
  async loadCurrentEnrollments() {
    try {
      const container = document.getElementById('current-enrollments-container');
      if (!container) return;

      // Show loading state using centralized design system
      if (window.SRDesignSystem && window.SRDesignSystem.layouts && window.SRDesignSystem.layouts.loadingState) {
        container.innerHTML = window.SRDesignSystem.layouts.loadingState('Loading current enrollments...');
      } else {
        container.innerHTML = `
          <div class="p-6 text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-500">Loading current enrollments...</p>
          </div>
        `;
      }

      // Get filter values - only show active and suspended students
      const filters = {
        academic_year_id: document.getElementById('filter_academic_year')?.value || '',
        term_id: document.getElementById('filter_term')?.value || '',
        class_id: document.getElementById('filter_class')?.value || '',
        status: 'active,suspended' // Only show active and suspended students
      };

      // Remove empty filters
      Object.keys(filters).forEach(key => {
        if (!filters[key]) delete filters[key];
      });

      // Get current enrollments from API using the new EnrollmentsAPI
      const result = await window.EnrollmentsAPI.getAll(filters);

      if (result.success) {
        let enrollments = result.data?.enrollments || result.data || [];
        enrollments = Array.isArray(enrollments) ? enrollments : [];

        // Apply search filter if search term exists
        const searchTerm = document.getElementById('search')?.value.toLowerCase() || '';
        if (searchTerm) {
          enrollments = enrollments.filter(enrollment => {
            const studentName = enrollment.student_name ||
                              `${enrollment.first_name || ''} ${enrollment.middle_name || ''} ${enrollment.last_name || ''}`.trim();
            const admissionNumber = enrollment.admission_number || '';

            return studentName.toLowerCase().includes(searchTerm) ||
                   admissionNumber.toLowerCase().includes(searchTerm);
          });
        }

        this.currentEnrollments = enrollments;
        this.renderCurrentEnrollments(this.currentEnrollments);
      } else {
        container.innerHTML = `
          <div class="p-6 text-center text-red-500">
            <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
            <p>Failed to load enrollments: ${result.message || 'Unknown error'}</p>
          </div>
        `;
      }
    } catch (error) {
      console.error('Failed to load current enrollments:', error);
      const container = document.getElementById('current-enrollments-container');
      if (container) {
        container.innerHTML = `
          <div class="p-6 text-center text-red-500">
            <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
            <p>Failed to load enrollments</p>
          </div>
        `;
      }
    }
  },

  // Render current enrollments
  renderCurrentEnrollments(enrollments) {
    const container = document.getElementById('current-enrollments-container');
    if (!container) return;

    if (enrollments.length === 0) {
      // Get current filter values to provide contextual messaging
      const academicYear = document.getElementById('filter_academic_year')?.selectedOptions[0]?.text || '';
      const term = document.getElementById('filter_term')?.selectedOptions[0]?.text || '';
      const classFilter = document.getElementById('filter_class')?.selectedOptions[0]?.text || '';
      const searchTerm = document.getElementById('search')?.value || '';

      let message = 'No current enrollments found';
      let suggestion = 'Try adjusting your search criteria or check if students are enrolled.';

      // Provide specific messaging based on applied filters
      if (searchTerm) {
        message = `No students found matching "${searchTerm}"`;
        suggestion = `Try a different search term or check the spelling.`;
      } else if (academicYear && term && classFilter && classFilter !== 'All Classes') {
        message = `No students enrolled in ${classFilter}`;
        suggestion = `for ${academicYear} - ${term}. Try selecting a different class or check if students are enrolled.`;
      } else if (academicYear && term) {
        message = `No student enrollments found`;
        suggestion = `for ${academicYear} - ${term}. Try selecting a specific class or check if students are enrolled for this period.`;
      }

      container.innerHTML = `
        <tr>
          <td colspan="5" class="${SRDesignSystem.responsive.table.cell} py-12 text-center text-gray-500">
            <div class="flex flex-col items-center">
              <div class="text-gray-400 mb-4">
                ${SRDesignSystem.components.icon('fas fa-user-check', '4xl', 'gray-300')}
              </div>
              <p class="${SRDesignSystem.responsive.text.lg} font-medium">${message}</p>
              <p class="${SRDesignSystem.responsive.text.sm}">${suggestion}</p>
            </div>
          </td>
        </tr>
      `;
      return;
    }

    container.innerHTML = enrollments.map(enrollment => `
      <tr class="hover:bg-gray-50">
        <td class="${SRDesignSystem.responsive.table.cell}">
          <div class="flex items-center">
            <div class="flex-shrink-0 h-10 w-10">
              <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-200"
                   src="${ManageStudentsComponent.getStudentPhotoUrl(enrollment.passport_photo)}"
                   alt="${enrollment.student_name || `${enrollment.first_name || ''} ${enrollment.last_name || ''}`.trim() || 'Student'}"
                   onerror="this.src='${window.SR.serverUrl}/assets/images/default-avatar.png'; this.onerror=null;">
            </div>
          </div>
        </td>
        <td class="${SRDesignSystem.responsive.table.cell}">
          <div class="text-sm font-medium text-gray-900">
            ${enrollment.student_name || `${enrollment.first_name || ''} ${enrollment.middle_name || ''} ${enrollment.last_name || ''}`.trim() || 'Unknown Student'}
          </div>
        </td>
        <td class="${SRDesignSystem.responsive.table.cell} text-sm text-gray-900">
          ${enrollment.class_name}
        </td>
        <td class="${SRDesignSystem.responsive.table.cell} text-sm text-gray-900">
          ${enrollment.stream_name || 'No Stream'}
        </td>
        <td class="${SRDesignSystem.responsive.table.cell} text-right">
          ${SRDesignSystem.tables.generateKebabMenu(`enrollment-${enrollment.student_id}-${enrollment.student_type}`, [
            {
              label: 'Change Stream',
              icon: 'fas fa-exchange-alt',
              onclick: `StudentEnrollmentComponent.changeStream(${JSON.stringify(enrollment).replace(/"/g, '&quot;')})`,
              color: 'indigo'
            }
          ], enrollment)}
        </td>
      </tr>
    `).join('');
  },

  // Render bulk operation modals
  renderBulkModals() {
    return `
      <!-- Bulk Class Promotion Modal -->
      <div id="bulk-class-promotion-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-xl bg-white">
          <div class="mt-3">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">Bulk Class Promotion</h3>
              ${SRDesignSystem.forms.button('close-bulk-promotion', '', 'ghost', {
                icon: 'fas fa-times text-xl',
                onclick: 'StudentEnrollmentComponent.closeBulkClassPromotionModal()',
                class: 'text-gray-400 hover:text-gray-600 p-2'
              })}
            </div>

            <div class="mt-6 space-y-6">

              <form id="bulk-class-promotion-form" class="space-y-6">
                <!-- Three column layout: Education Level, Current Class, Target Class -->
                <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap}">
                  ${SRDesignSystem.forms.select('bulk_class_promotion_level', 'Education Level', [
                    { value: '', label: 'Select Education Level' },
                    { value: 'o_level', label: 'O-Level' },
                    { value: 'a_level', label: 'A-Level' }
                  ], '', {
                    required: true,
                    onchange: 'StudentEnrollmentComponent.onLevelSelectionChange()'
                  })}
                  ${SRDesignSystem.forms.select('bulk_class_promotion_current_class', 'Current Class', [], '', {
                    required: true,
                    onchange: 'StudentEnrollmentComponent.loadCurrentClassStudents()'
                  })}
                  ${SRDesignSystem.forms.input('bulk_class_promotion_target_class', 'Target Class', '', {
                    required: true,
                    readonly: true,
                    placeholder: 'Select education level and current class first',
                    helpText: 'Automatically determined based on class progression'
                  })}
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Students Eligible for Promotion
                  </label>
                  <div id="class-promotion-students-preview" class="space-y-2 max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-3 bg-gray-50">
                    <p class="text-gray-500 text-center py-4">Select a current class to see eligible students</p>
                  </div>
                  <p class="mt-1 text-sm text-gray-500">Only students from previous academic year (Term 3) will be promoted</p>
                </div>

                <div class="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
                  ${SRDesignSystem.forms.button('cancel-bulk-class-promotion', 'Cancel', 'secondary', {
                    type: 'button',
                    onclick: 'StudentEnrollmentComponent.closeBulkClassPromotionModal()'
                  })}
                  ${SRDesignSystem.forms.button('save-bulk-class-promotion', 'Promote Students', 'primary', {
                    type: 'submit'
                  })}
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>


    `;
  },



  // Show bulk class promotion modal
  showBulkClassPromotionModal() {
    const modal = document.getElementById('bulk-class-promotion-modal');
    if (modal) {
      modal.classList.remove('hidden');
      this.populateBulkClassPromotionDropdowns();
    }
  },

  // Load students for selected current class (Class Promotion)
  async loadCurrentClassStudents() {
    const classId = document.getElementById('bulk_class_promotion_current_class')?.value;
    const levelSelect = document.getElementById('bulk_class_promotion_level');
    const container = document.getElementById('class-promotion-students-preview');

    if (!container) return;

    if (!classId) {
      container.innerHTML = '<p class="text-gray-500 text-center py-4">Select a current class to see eligible students</p>';
      return;
    }

    const selectedLevel = levelSelect?.value;
    if (!selectedLevel) {
      container.innerHTML = '<p class="text-gray-500 text-center py-4">Select education level first</p>';
      return;
    }

    try {
      // Get current academic context
      const currentAcademicYear = window.AcademicContext.getActiveAcademicYear();
      if (!currentAcademicYear) {
        container.innerHTML = '<p class="text-red-500 text-center py-4">No active academic year found</p>';
        return;
      }

      // Calculate previous academic year
      const previousYear = parseInt(currentAcademicYear.name) - 1;

      // Load students using existing StudentsAPI with level filter
      const filters = {
        level: selectedLevel,
        class_id: classId,
        academic_year_name: previousYear.toString(),
        term_name: 'Term 3',
        status: 'active,suspended'
      };

      const studentsResult = await window.StudentsAPI.getAll(filters);

      if (!studentsResult.success || !studentsResult.data) {
        container.innerHTML = '<p class="text-gray-500 text-center py-4">Failed to load students</p>';
        return;
      }

      // Handle different response structures for O-Level and A-Level APIs
      const eligibleStudents = studentsResult.data.students || studentsResult.data.enrollments || studentsResult.data || [];

      if (eligibleStudents.length === 0) {
        container.innerHTML = `
          <div class="text-center py-4">
            <p class="text-gray-500">No students eligible for promotion</p>
            <p class="text-sm text-gray-400 mt-1">Students must be in ${previousYear} Term 3 to be promoted</p>
          </div>
        `;
        return;
      }

      container.innerHTML = `
        <div class="mb-2 text-sm text-green-600 font-medium">
          ${eligibleStudents.length} student${eligibleStudents.length !== 1 ? 's' : ''} eligible for promotion
        </div>
        ${eligibleStudents.map(student => `
          <div class="flex items-center space-x-3 p-2 bg-white rounded border">
            <div class="flex-1">
              <div class="text-sm font-medium text-gray-900">
                ${student.student_name || `${student.first_name || ''} ${student.last_name || ''}`.trim() || 'Unknown Student'}
              </div>
              <div class="text-xs text-gray-500">
                ${student.admission_number} • ${student.academic_year_name} ${student.term_name} •
                <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                  student.status === 'active' ? 'bg-green-100 text-green-800' :
                  student.status === 'suspended' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }">
                  ${student.status || 'Unknown'}
                </span>
              </div>
            </div>
          </div>
        `).join('')}
      `;

      // Store eligible students for promotion
      this.eligibleStudentsForPromotion = eligibleStudents;

    } catch (error) {
      console.error('Error loading students for promotion:', error);
      container.innerHTML = '<p class="text-red-500 text-center py-4">Error loading students</p>';
    }
  },

  // Close bulk class promotion modal
  closeBulkClassPromotionModal() {
    const modal = document.getElementById('bulk-class-promotion-modal');
    if (modal) {
      modal.classList.add('hidden');
      document.getElementById('bulk-class-promotion-form').reset();
    }
  },







  // Handle level selection change
  async onLevelSelectionChange() {
    const levelSelect = document.getElementById('bulk_class_promotion_level');
    const currentClassSelect = document.getElementById('bulk_class_promotion_current_class');
    const targetClassInput = document.getElementById('bulk_class_promotion_target_class');
    const studentsContainer = document.getElementById('class-promotion-students-preview');

    if (!levelSelect || !currentClassSelect) return;

    const selectedLevel = levelSelect.value;

    // Clear current selections
    currentClassSelect.innerHTML = '<option value="">Select Current Class</option>';
    if (targetClassInput) {
      targetClassInput.value = '';
      targetClassInput.placeholder = 'Select education level and current class first';
      targetClassInput.removeAttribute('data-target-class-id');
    }
    if (studentsContainer) {
      studentsContainer.innerHTML = '<p class="text-gray-500 text-center py-4">Select a current class to see eligible students</p>';
    }

    // If no education level selected, disable class selection
    if (!selectedLevel) {
      currentClassSelect.disabled = true;
      currentClassSelect.innerHTML = '<option value="">Select Education Level First</option>';
      currentClassSelect.classList.add('opacity-50', 'cursor-not-allowed');
      return;
    }

    // Enable class selection and show loading state
    currentClassSelect.disabled = false;
    currentClassSelect.classList.remove('opacity-50', 'cursor-not-allowed');
    currentClassSelect.innerHTML = '<option value="">Loading classes...</option>';

    // Load classes directly from API using education level filter
    try {
      // Use level parameter to get filtered classes directly from API
      const result = await window.ClassesAPI.getAll({ level: selectedLevel });

      if (result.success && result.data) {
        // Reset the dropdown
        currentClassSelect.innerHTML = '<option value="">Select Current Class</option>';

        // Filter classes based on promotion rules:
        // O-Level: Only S.1, S.2, S.3 (S.4 students complete O-Level)
        // A-Level: Only S.5 (S.6 students graduate)
        const promotableClasses = result.data.filter(cls => {
          const className = cls.name.toLowerCase();
          if (selectedLevel === 'o_level') {
            // Only S.1, S.2, S.3 can be promoted (S.4 completes O-Level)
            return className.includes('s.1') || className.includes('s.2') || className.includes('s.3') ||
                   className.includes('s1') || className.includes('s2') || className.includes('s3');
          } else if (selectedLevel === 'a_level') {
            // Only S.5 can be promoted (S.6 graduates)
            return className.includes('s.5') || className.includes('s5');
          }
          return false;
        });

        // Populate dropdown with promotable classes
        promotableClasses.forEach(cls => {
          const option = document.createElement('option');
          option.value = cls.id;
          option.textContent = cls.name;
          option.setAttribute('data-class-name', cls.name);
          option.setAttribute('data-level', selectedLevel);
          currentClassSelect.appendChild(option);
        });

        console.log(`✅ Loaded ${promotableClasses.length} promotable ${selectedLevel.replace('_', '-')} classes`);

        if (promotableClasses.length === 0) {
          currentClassSelect.innerHTML = '<option value="">No promotable classes found for this level</option>';
          currentClassSelect.disabled = true;
          currentClassSelect.classList.add('opacity-50');
        }
      } else {
        currentClassSelect.innerHTML = '<option value="">Failed to load classes</option>';
        currentClassSelect.disabled = true;
        currentClassSelect.classList.add('opacity-50');
        console.error('❌ Failed to load classes:', result);
      }
    } catch (error) {
      console.error('❌ Error loading classes for bulk class promotion:', error);
      currentClassSelect.innerHTML = '<option value="">Error loading classes</option>';
      currentClassSelect.disabled = true;
      currentClassSelect.classList.add('opacity-50');
    }
  },

  // Populate dropdowns for bulk class promotion
  populateBulkClassPromotionDropdowns() {
    // Initialize class selection as disabled until education level is selected
    const currentClassSelect = document.getElementById('bulk_class_promotion_current_class');
    if (currentClassSelect) {
      currentClassSelect.innerHTML = '<option value="">Select Education Level First</option>';
      currentClassSelect.disabled = true;
      currentClassSelect.classList.add('opacity-50', 'cursor-not-allowed');

      // Add event listener to auto-populate target class (remove existing first to prevent duplicates)
      currentClassSelect.removeEventListener('change', this.autoPopulateTargetClass);
      currentClassSelect.addEventListener('change', this.autoPopulateTargetClass.bind(this));
    }

    // Ensure education level dropdown has proper event listener
    const levelSelect = document.getElementById('bulk_class_promotion_level');
    if (levelSelect) {
      // Remove existing listener to prevent duplicates
      levelSelect.removeEventListener('change', this.onLevelSelectionChange);
      levelSelect.addEventListener('change', this.onLevelSelectionChange.bind(this));
    }
  },

  // Auto-populate target class based on current class selection
  async autoPopulateTargetClass() {
    const currentClassSelect = document.getElementById('bulk_class_promotion_current_class');
    const targetClassInput = document.getElementById('bulk_class_promotion_target_class');

    if (!currentClassSelect || !targetClassInput) return;

    const selectedOption = currentClassSelect.options[currentClassSelect.selectedIndex];
    if (!selectedOption || !selectedOption.value) {
      targetClassInput.value = '';
      targetClassInput.placeholder = 'Select current class first';
      // Clear the stored target class ID
      targetClassInput.removeAttribute('data-target-class-id');
      return;
    }

    const currentClassName = selectedOption.getAttribute('data-class-name');
    const targetClassName = this.getNextClassName(currentClassName);

    if (targetClassName) {
      // Get the selected level to make a targeted API call
      const levelSelect = document.getElementById('bulk_class_promotion_level');
      const selectedLevel = levelSelect?.value;

      if (selectedLevel) {
        try {
          // Load only classes for the same education level to find target class
          const result = await window.ClassesAPI.getAll({ level: selectedLevel });
          if (result.success && result.data) {
            const targetClass = result.data.find(cls => cls.name === targetClassName);

            if (targetClass) {
              targetClassInput.value = targetClassName;
              targetClassInput.setAttribute('data-target-class-id', targetClass.id);
              targetClassInput.placeholder = '';
            } else {
              targetClassInput.value = `${targetClassName} (Not Found)`;
              targetClassInput.removeAttribute('data-target-class-id');
              targetClassInput.placeholder = '';
            }
          } else {
            targetClassInput.value = `${targetClassName} (Error loading)`;
            targetClassInput.removeAttribute('data-target-class-id');
            targetClassInput.placeholder = '';
          }
        } catch (error) {
          console.error('❌ Error loading classes for target class lookup:', error);
          targetClassInput.value = `${targetClassName} (Error)`;
          targetClassInput.removeAttribute('data-target-class-id');
          targetClassInput.placeholder = '';
        }
      } else {
        targetClassInput.value = `${targetClassName} (Level not selected)`;
        targetClassInput.removeAttribute('data-target-class-id');
        targetClassInput.placeholder = '';
      }
    } else {
      // No progression available (S.4 for O-Level, S.6 for A-Level)
      const levelSelect = document.getElementById('bulk_class_promotion_level');
      const selectedLevel = levelSelect?.value;

      if (selectedLevel === 'o_level') {
        targetClassInput.value = 'O-Level Complete (No promotion to A-Level)';
      } else if (selectedLevel === 'a_level') {
        targetClassInput.value = 'A-Level Complete (Graduation)';
      } else {
        targetClassInput.value = 'No progression available';
      }

      targetClassInput.removeAttribute('data-target-class-id');
      targetClassInput.placeholder = '';
    }
  },

  // Get the next class name based on current class and level
  getNextClassName(currentClassName) {
    if (!currentClassName) return null;

    // Get the selected level to determine progression rules
    const levelSelect = document.getElementById('bulk_class_promotion_level');
    const selectedLevel = levelSelect?.value;

    if (selectedLevel === 'o_level') {
      // O-Level progression: S.1 → S.2 → S.3 → S.4
      // Note: Only S.1, S.2, S.3 are shown in dropdown (S.4 students complete O-Level)
      const oLevelMap = {
        'S.1': 'S.2', 'S1': 'S2',
        'S.2': 'S.3', 'S2': 'S3',
        'S.3': 'S.4', 'S3': 'S4'
      };

      if (oLevelMap[currentClassName]) {
        return oLevelMap[currentClassName];
      }

      // S.4 students complete O-Level, no further promotion via this modal
      if (currentClassName === 'S.4' || currentClassName === 'S4') {
        return null; // No progression available
      }
    } else if (selectedLevel === 'a_level') {
      // A-Level progression: S.5 → S.6
      // Note: Only S.5 is shown in dropdown (S.6 students graduate)
      const aLevelMap = {
        'S.5': 'S.6', 'S5': 'S6'
      };

      if (aLevelMap[currentClassName]) {
        return aLevelMap[currentClassName];
      }

      // S.6 students graduate, no further promotion
      if (currentClassName === 'S.6' || currentClassName === 'S6') {
        return null; // No progression available
      }
    }

    // Fallback: try to extract class level and increment
    const match = currentClassName.match(/S\.?(\d)/i);
    if (match) {
      const currentLevel = parseInt(match[1]);
      if (currentLevel >= 1 && currentLevel <= 5) {
        const nextLevel = currentLevel + 1;
        // Return in the same format as the input
        return currentClassName.includes('.') ? `S.${nextLevel}` : `S${nextLevel}`;
      }
    }

    return null;
  },



  // Load streams for bulk class promotion
  async loadStreamsForBulkClassPromotion() {
    const classId = document.getElementById('bulk_class_promotion_target_class')?.value;
    const streamSelect = document.getElementById('bulk_class_promotion_target_stream');

    if (!streamSelect) return;

    streamSelect.innerHTML = '<option value="">Select Stream</option>';

    if (!classId) return;

    try {
      const result = await window.StreamsAPI.getForClass(classId);
      if (result.success && result.data.streams) {
        result.data.streams.forEach(stream => {
          const option = document.createElement('option');
          option.value = stream.id || '';
          option.textContent = stream.name;
          streamSelect.appendChild(option);
        });
      }
    } catch (error) {
      console.error('Error loading streams for bulk class promotion:', error);
    }
  },

  // Populate students for bulk enroll (students who can be reassigned)
  populateStudentsForBulkEnroll() {
    const container = document.getElementById('students-selection');
    if (!container) return;

    // Get current enrollments data
    const enrollments = this.currentEnrollments || [];

    if (enrollments.length === 0) {
      container.innerHTML = '<p class="text-gray-500 text-center py-4">No students available for reassignment</p>';
      return;
    }

    container.innerHTML = enrollments.map(enrollment => `
      <label class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer">
        <input type="checkbox" name="selected_students" value="${enrollment.id}" class="rounded border-gray-300">
        <div class="flex-1">
          <div class="text-sm font-medium text-gray-900">
            ${enrollment.student_name || `${enrollment.first_name || ''} ${enrollment.last_name || ''}`.trim() || 'Unknown Student'}
          </div>
          <div class="text-xs text-gray-500">
            ${enrollment.admission_number} • ${enrollment.class_name} • ${enrollment.stream_name || 'No Stream'}
          </div>
        </div>
      </label>
    `).join('');
  },

  // Populate enrollments for bulk promote
  populateEnrollmentsForBulkPromote() {
    const container = document.getElementById('enrollments-selection');
    if (!container) return;

    // Get current enrollments data
    const enrollments = this.currentEnrollments || [];

    if (enrollments.length === 0) {
      container.innerHTML = '<p class="text-gray-500 text-center py-4">No students available for promotion</p>';
      return;
    }

    container.innerHTML = enrollments.map(enrollment => `
      <label class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer">
        <input type="checkbox" name="selected_enrollments" value="${enrollment.id}" class="rounded border-gray-300">
        <div class="flex-1">
          <div class="text-sm font-medium text-gray-900">
            ${enrollment.student_name || `${enrollment.first_name || ''} ${enrollment.last_name || ''}`.trim() || 'Unknown Student'}
          </div>
          <div class="text-xs text-gray-500">
            ${enrollment.admission_number} • ${enrollment.class_name} • ${enrollment.stream_name || 'No Stream'}
          </div>
        </div>
      </label>
    `).join('');
  },

  // Populate students for class promotion
  populateStudentsForClassPromotion() {
    const container = document.getElementById('class-promotion-students-selection');
    if (!container) return;

    // Get current enrollments data
    const enrollments = this.currentEnrollments || [];

    if (enrollments.length === 0) {
      container.innerHTML = '<p class="text-gray-500 text-center py-4">No students available for class promotion</p>';
      return;
    }

    container.innerHTML = enrollments.map(enrollment => `
      <label class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer">
        <input type="checkbox" name="class_promotion_students" value="${enrollment.id}" class="rounded border-gray-300">
        <div class="flex-1">
          <div class="text-sm font-medium text-gray-900">
            ${enrollment.student_name || `${enrollment.first_name || ''} ${enrollment.last_name || ''}`.trim() || 'Unknown Student'}
          </div>
          <div class="text-xs text-gray-500">
            ${enrollment.admission_number} • ${enrollment.class_name} • ${enrollment.stream_name || 'No Stream'}
          </div>
        </div>
      </label>
    `).join('');
  },

  // Populate students for term promotion
  populateStudentsForTermPromotion() {
    const container = document.getElementById('term-promotion-students-selection');
    if (!container) return;

    // Get current enrollments data
    const enrollments = this.currentEnrollments || [];

    if (enrollments.length === 0) {
      container.innerHTML = '<p class="text-gray-500 text-center py-4">No students available for term promotion</p>';
      return;
    }

    container.innerHTML = enrollments.map(enrollment => `
      <label class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer">
        <input type="checkbox" name="term_promotion_students" value="${enrollment.id}" class="rounded border-gray-300">
        <div class="flex-1">
          <div class="text-sm font-medium text-gray-900">
            ${enrollment.student_name || `${enrollment.first_name || ''} ${enrollment.last_name || ''}`.trim() || 'Unknown Student'}
          </div>
          <div class="text-xs text-gray-500">
            ${enrollment.admission_number} • ${enrollment.class_name} • ${enrollment.stream_name || 'No Stream'}
          </div>
        </div>
      </label>
    `).join('');
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Add form submission handlers for bulk operations
    const bulkClassPromotionForm = document.getElementById('bulk-class-promotion-form');
    if (bulkClassPromotionForm) {
      bulkClassPromotionForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.saveBulkClassPromotion();
      });
    }


  },

  // Save bulk class promotion
  async saveBulkClassPromotion() {
    try {
      const currentClassId = document.getElementById('bulk_class_promotion_current_class')?.value;
      const targetClassInput = document.getElementById('bulk_class_promotion_target_class');
      const targetClassId = targetClassInput?.getAttribute('data-target-class-id');

      // Validation
      if (!currentClassId) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select a current class', 'error');
        }
        return;
      }

      if (!targetClassId) {
        const levelSelect = document.getElementById('bulk_class_promotion_level');
        const selectedLevel = levelSelect?.value;

        let message = 'Target class not found. Please ensure the progression class exists.';
        if (selectedLevel === 'o_level') {
          message = 'S.4 students have completed O-Level. Use A-Level registration for S.5 enrollment.';
        } else if (selectedLevel === 'a_level') {
          message = 'S.6 students have completed A-Level and should graduate.';
        }

        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show(message, 'error');
        }
        return;
      }

      // Get current academic context
      const currentAcademicYear = window.AcademicContext.getActiveAcademicYear();
      if (!currentAcademicYear) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('No active academic year found', 'error');
        }
        return;
      }

      // Use eligible students that were loaded earlier
      const eligibleStudents = this.eligibleStudentsForPromotion || [];

      if (eligibleStudents.length === 0) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('No eligible students found for promotion', 'error');
        }
        return;
      }

      // Get Term 1 of the current academic year (where students will be promoted to)
      const terms = StudentManagementComponents.state.terms.data || StudentManagementComponents.state.terms;
      const term1 = Array.isArray(terms) ? terms.find(term => term.name === 'Term 1') : null;

      if (!term1) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Term 1 not found for promotion', 'error');
        }
        return;
      }

      // Set loading state
      if (window.SRDesignSystem?.forms) {
        window.SRDesignSystem.forms.setButtonLoading('save-bulk-class-promotion', true);
      }

      // Get selected level to determine student type
      const levelSelect = document.getElementById('bulk_class_promotion_level');
      const selectedLevel = levelSelect?.value;

      if (!selectedLevel) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select education level', 'error');
        }
        return;
      }

      // Prepare data for bulk class promotion
      const promotions = eligibleStudents.map(student => ({
        student_id: student.student_id || student.id,
        student_type: selectedLevel, // Use selected level as student type
        target_class_id: targetClassId,
        target_stream_id: null, // Stream will be assigned separately if needed
        target_academic_year_id: currentAcademicYear.id, // Promote to current academic year
        target_term_id: term1.id // Promote to Term 1
      }));

      // Call API for bulk class promotion
      const result = await window.EnrollmentsAPI.bulkPromote({ promotions });

      if (result.success) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show(
            `${promotions.length} students promoted successfully!`,
            'success'
          );
        }
        this.closeBulkClassPromotionModal();
        await this.loadCurrentEnrollments();
      } else {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show(
            result.message || 'Failed to promote students',
            'error'
          );
        }
      }
    } catch (error) {
      console.error('Bulk class promotion error:', error);
      if (window.SRDesignSystem?.notifications) {
        window.SRDesignSystem.notifications.show('Failed to promote students', 'error');
      }
    } finally {
      // Reset loading state
      if (window.SRDesignSystem?.forms) {
        window.SRDesignSystem.forms.setButtonLoading('save-bulk-class-promotion', false);
      }
    }
  },

  // Save bulk promote
  async saveBulkPromote() {
    try {
      const targetClassId = document.getElementById('bulk_promote_target_class')?.value;
      const targetStreamId = document.getElementById('bulk_promote_target_stream')?.value || null;
      const academicYearId = document.getElementById('bulk_promote_academic_year')?.value;
      const termId = document.getElementById('bulk_promote_term')?.value;
      const selectedEnrollments = document.querySelectorAll('input[name="selected_enrollments"]:checked');

      // Validation
      if (!targetClassId || !academicYearId || !termId) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select target class, academic year, and term', 'error');
        }
        return;
      }

      if (selectedEnrollments.length === 0) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select at least one student to promote', 'error');
        }
        return;
      }

      // Set loading state
      if (window.SRDesignSystem?.forms) {
        window.SRDesignSystem.forms.setButtonLoading('save-bulk-promote', true);
      }

      // Prepare data for bulk promotion
      const promotions = Array.from(selectedEnrollments).map(checkbox => ({
        enrollment_id: checkbox.value,
        target_class_id: targetClassId,
        target_stream_id: targetStreamId,
        target_academic_year_id: academicYearId,
        target_term_id: termId
      }));

      // Call API for bulk promotion
      const result = await window.EnrollmentsAPI.bulkPromote({ promotions });

      if (result.success) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show(
            `${promotions.length} students promoted successfully!`,
            'success'
          );
        }
        this.closeBulkTermPromotionModal();
        await this.loadCurrentEnrollments();
      } else {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show(
            result.message || 'Failed to promote students',
            'error'
          );
        }
      }
    } catch (error) {
      console.error('Bulk promote error:', error);
      if (window.SRDesignSystem?.notifications) {
        window.SRDesignSystem.notifications.show('Failed to promote students', 'error');
      }
    } finally {
      // Reset loading state
      if (window.SRDesignSystem?.forms) {
        window.SRDesignSystem.forms.setButtonLoading('save-bulk-promote', false);
      }
    }
  },



  // Change stream assignment - using enrollment data directly (like Student Records approach)
  changeStream(enrollmentData) {
    try {
      // If enrollment is a string (JSON), parse it
      if (typeof enrollmentData === 'string') {
        enrollmentData = JSON.parse(enrollmentData);
      }

      // Show stream change modal with the enrollment data we already have
      this.showChangeStreamModal(enrollmentData);
    } catch (error) {
      console.error('Change stream error:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to process enrollment data', 'error');
      }
    }
  },



  // Show change stream modal
  showChangeStreamModal(enrollment) {
    const modalHtml = `
      <div id="change-stream-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-6 border w-2/3 shadow-lg rounded-lg bg-white">
          <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b">
              <h3 class="text-xl font-semibold text-gray-900">Change Stream Assignment</h3>
              ${SRDesignSystem.forms.button('close-change-stream', '', 'ghost', {
                icon: 'fas fa-times text-xl',
                onclick: 'this.closest(\'#change-stream-modal\').remove()',
                class: 'text-gray-400 hover:text-gray-600 p-2'
              })}
            </div>

            <!-- Change Form -->
            <div class="py-4">
              <form id="change-stream-form" class="space-y-4">
                <input type="hidden" id="stream-student-id" value="${enrollment.student_id}">
                <input type="hidden" id="stream-student-type" value="${enrollment.student_type}">

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">New Stream *</label>
                  <select id="new-stream-id" class="w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200" required>
                    <option value="">Select New Stream</option>
                  </select>
                  <p class="mt-1 text-sm text-gray-500">Select a stream within the current class (${enrollment.class_name})</p>
                </div>
              </form>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end pt-4 border-t">
              ${SRDesignSystem.forms.button('save-stream-change', 'Save Changes', 'blue', {
                icon: 'fas fa-save',
                onclick: 'StudentEnrollmentComponent.saveStreamChange()'
              })}
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Populate streams for the current class only
    this.populateStreamsForClass(enrollment.class_id, 'new-stream-id');
  },



  // Populate streams for a specific class
  async populateStreamsForClass(classId, selectElementId) {
    try {
      const streamSelect = document.getElementById(selectElementId);
      if (!streamSelect) return;

      streamSelect.innerHTML = '<option value="">Select Stream</option>';

      if (!classId) return;

      const result = await window.StreamsAPI.getForClass(classId);
      if (result.success && result.data.streams) {
        result.data.streams.forEach(stream => {
          const option = document.createElement('option');
          option.value = stream.id || '';
          option.textContent = stream.name;
          streamSelect.appendChild(option);
        });
      }
    } catch (error) {
      console.error('Error loading streams for class:', error);
    }
  },



  // Save stream change
  async saveStreamChange() {
    try {
      const studentId = document.getElementById('stream-student-id').value;
      const studentType = document.getElementById('stream-student-type').value;
      const newStreamId = document.getElementById('new-stream-id').value;

      if (!newStreamId) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select a new stream', 'error');
        }
        return;
      }

      // Use the new change-stream endpoint
      const result = await window.EnrollmentsAPI.changeStream({
        student_id: studentId,
        student_type: studentType,
        new_stream_id: newStreamId
      });

      if (result.success) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Stream assignment changed successfully!', 'success');
        }
        document.getElementById('change-stream-modal').remove();
        await this.loadCurrentEnrollments();
      } else {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to change stream assignment', 'error');
        }
      }
    } catch (error) {
      console.error('Save stream change error:', error);
      if (window.SRDesignSystem?.notifications) {
        window.SRDesignSystem.notifications.show('Failed to change stream', 'error');
      }
    }
  },



  // Load classes for change dropdown
  async loadClassesForChange() {
    try {
      const classSelect = document.getElementById('new-class-id');
      if (!classSelect) return;

      // Get classes from the state
      const classes = StudentManagementComponents.state.classes.data || StudentManagementComponents.state.classes;

      if (Array.isArray(classes)) {
        classSelect.innerHTML = '<option value="">Select New Class</option>';
        classes.forEach(cls => {
          const option = document.createElement('option');
          option.value = cls.id;
          option.textContent = cls.name;
          classSelect.appendChild(option);
        });

        // Add event listener for class change to load streams
        classSelect.addEventListener('change', () => {
          this.loadStreamsForChange(classSelect.value);
        });
      }
    } catch (error) {
      console.error('Error loading classes for change:', error);
    }
  },

  // Load streams for change dropdown
  async loadStreamsForChange(classId) {
    try {
      const streamSelect = document.getElementById('new-stream-id');
      if (!streamSelect) return;

      streamSelect.innerHTML = '<option value="">Select New Stream</option>';

      if (!classId) return;

      // Get streams for the selected class
      const result = await window.StreamsAPI.getForClass(classId);

      if (result.success && result.data.streams) {
        result.data.streams.forEach(stream => {
          const option = document.createElement('option');
          option.value = stream.id || '';
          option.textContent = stream.name;
          streamSelect.appendChild(option);
        });
      }
    } catch (error) {
      console.error('Error loading streams for change:', error);
    }
  },

  // Save class/stream change
  async saveClassStreamChange() {
    try {
      const enrollmentId = document.getElementById('enrollment-id').value;
      const newClassId = document.getElementById('new-class-id').value;
      const newStreamId = document.getElementById('new-stream-id').value || null;
      const reason = document.getElementById('change-reason').value.trim();

      if (!newClassId) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select a new class', 'error');
        }
        return;
      }

      // Prepare update data
      const updateData = {
        class_id: newClassId,
        stream_id: newStreamId,
        change_reason: reason
      };

      // Call API to update enrollment
      const result = await window.EnrollmentsAPI.updateClassStream(enrollmentId, updateData);

      if (result.success) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Class/Stream assignment updated successfully', 'success');
        }

        // Close modal and refresh data
        document.getElementById('change-class-stream-modal').remove();
        await this.loadCurrentEnrollments();
      } else {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to update assignment', 'error');
        }
      }
    } catch (error) {
      console.error('Save class/stream change error:', error);
      if (window.SRDesignSystem?.notifications) {
        window.SRDesignSystem.notifications.show('Failed to update class/stream assignment', 'error');
      }
    }
  },

  // Promote student
  async promoteStudent(enrollmentId) {
    // This would typically open a modal with promotion form
    // For now, we'll use simple prompts
    const newClassId = prompt('Enter new class ID:');
    const newStreamId = prompt('Enter new stream ID (optional):');
    const newAcademicYearId = prompt('Enter new academic year ID:');
    const newTermId = prompt('Enter new term ID:');

    if (!newClassId || !newAcademicYearId || !newTermId) {
      alert('Class ID, Academic Year ID, and Term ID are required');
      return;
    }

    try {
      const promotionData = {
        new_class_id: parseInt(newClassId),
        new_stream_id: newStreamId ? parseInt(newStreamId) : null,
        new_academic_year_id: parseInt(newAcademicYearId),
        new_term_id: parseInt(newTermId),
        promotion_date: new Date().toISOString().split('T')[0]
      };

      const result = await window.EnrollmentsAPI.promote(enrollmentId, promotionData);

      if (result.success) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Student promoted successfully', 'success');
        }
        await this.loadCurrentEnrollments();
      } else {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to promote student', 'error');
        }
      }
    } catch (error) {
      console.error('Promote student error:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to promote student', 'error');
      }
    }
  },



  // View student enrollment history
  async viewHistory(studentId, studentType) {
    try {
      const result = await window.EnrollmentsAPI.getStudentHistory(studentId, studentType);

      if (result.success) {
        const history = result.data || [];
        this.showHistoryModal(history);
      } else {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to load history', 'error');
        }
      }
    } catch (error) {
      console.error('View history error:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to load enrollment history', 'error');
      }
    }
  },

  // Show history modal
  showHistoryModal(history) {
    // Create modal content
    let historyContent = '';
    if (history.length === 0) {
      historyContent = '<p class="text-gray-500 text-center py-4">No enrollment history found</p>';
    } else {
      let tableRows = '';
      history.forEach(record => {
        tableRows += '<tr>' +
          '<td class="px-4 py-2 text-sm text-gray-900">' + record.academic_year_name + '</td>' +
          '<td class="px-4 py-2 text-sm text-gray-900">' + record.term_name + '</td>' +
          '<td class="px-4 py-2 text-sm text-gray-900">' + record.class_name + '</td>' +
          '<td class="px-4 py-2 text-sm text-gray-900">' + (record.stream_name || 'No Stream') + '</td>' +
          '<td class="px-4 py-2 text-sm text-gray-500">' + new Date(record.enrollment_date).toLocaleDateString() + '</td>' +
        '</tr>';
      });

      historyContent = '<table class="min-w-full divide-y divide-gray-200">' +
        '<thead class="bg-gray-50">' +
          '<tr>' +
            '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Academic Year</th>' +
            '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Term</th>' +
            '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Class</th>' +
            '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Stream</th>' +
            '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Enrollment Date</th>' +
          '</tr>' +
        '</thead>' +
        '<tbody class="bg-white divide-y divide-gray-200">' + tableRows + '</tbody>' +
      '</table>';
    }

    const modalHtml = '<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" id="history-modal">' +
      '<div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">' +
        '<div class="mt-3">' +
          '<div class="flex items-center justify-between mb-4">' +
            '<h3 class="text-lg font-medium text-gray-900">Student Enrollment History</h3>' +
            SRDesignSystem.forms.button('close-history-modal', '', 'ghost', {
              icon: 'fas fa-times',
              onclick: 'document.getElementById(\'history-modal\').remove()',
              class: 'text-gray-400 hover:text-gray-600 p-2'
            }) +
          '</div>' +
          '<div class="max-h-96 overflow-y-auto">' + historyContent + '</div>' +
        '</div>' +
      '</div>' +
    '</div>';

    document.body.insertAdjacentHTML('beforeend', modalHtml);
  },

  // Show bulk stream assignment modal
  showBulkStreamAssignmentModal() {
    const modalHtml = `
      <div id="bulk-stream-assignment-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <div class="p-6">
            <!-- Header -->
            <div class="flex items-center justify-between mb-6">
              <div>
                <h2 class="text-xl font-semibold text-gray-900">Bulk Stream Assignment</h2>
                <p class="text-sm text-gray-600 mt-1">Assign or change streams for multiple students</p>
              </div>
              ${SRDesignSystem.forms.button('close-bulk-stream-modal', '', 'ghost', {
                icon: 'fas fa-times text-xl',
                onclick: 'this.closest(\'#bulk-stream-assignment-modal\').remove()',
                class: 'text-gray-400 hover:text-gray-600 p-2'
              })}
            </div>

            <!-- Class Selection -->
            <div class="mb-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Class</label>
                  <select id="bulk-stream-class-id" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500" onchange="StudentEnrollmentComponent.loadStudentsForBulkStreamAssignment()" required>
                    <option value="">Select Class</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Target Stream</label>
                  <select id="bulk-stream-target-stream-id" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500" required>
                    <option value="">Select Stream</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Students  -->
            <div class="mb-6">
              <div class="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                <div id="bulk-stream-students-container">
                  <p class="text-gray-500 text-center py-4">Select a class to view students</p>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3 pt-4 border-t">
              ${SRDesignSystem.forms.button('cancel-bulk-stream-assignment', 'Cancel', 'secondary', {
                onclick: 'this.closest(\'#bulk-stream-assignment-modal\').remove()'
              })}
              ${SRDesignSystem.forms.button('save-bulk-stream-assignment', 'Assign Streams', 'primary', {
                icon: 'fas fa-save',
                onclick: 'StudentEnrollmentComponent.saveBulkStreamAssignment()'
              })}
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Populate classes dropdown
    this.populateClassesForBulkStreamAssignment();
  },

  // Populate classes for bulk stream assignment (O-Level only: S.1 to S.4)
  async populateClassesForBulkStreamAssignment() {
    try {
      const classSelect = document.getElementById('bulk-stream-class-id');
      if (!classSelect) return;

      // Use level parameter to get only O-Level classes directly from API
      const result = await window.ClassesAPI.getAll({ level: 'o_level' });
      if (result.success && result.data) {
        // A-Level students (S.5, S.6) must have streams, so no bulk assignment needed
        result.data.forEach(cls => {
          const option = document.createElement('option');
          option.value = cls.id;
          option.textContent = cls.name;
          classSelect.appendChild(option);
        });

        console.log(`✅ Loaded ${result.data.length} O-Level classes for bulk stream assignment`);
      }
    } catch (error) {
      console.error('Error loading classes for bulk stream assignment:', error);
    }
  },

  // Load students for bulk stream assignment
  async loadStudentsForBulkStreamAssignment() {
    try {
      const classId = document.getElementById('bulk-stream-class-id').value;
      const container = document.getElementById('bulk-stream-students-container');
      const streamSelect = document.getElementById('bulk-stream-target-stream-id');

      if (!classId) {
        container.innerHTML = '<p class="text-gray-500 text-center py-4">Select a class to view students</p>';
        streamSelect.innerHTML = '<option value="">Select Stream</option>';
        return;
      }

      // Load streams for the selected class
      const streamsResult = await window.StreamsAPI.getForClass(classId);
      streamSelect.innerHTML = '<option value="">Select Stream</option>';

      if (streamsResult.success && streamsResult.data.streams) {
        streamsResult.data.streams.forEach(stream => {
          const option = document.createElement('option');
          option.value = stream.id;
          option.textContent = stream.name;
          streamSelect.appendChild(option);
        });
      }

      // Load students in the class
      const activeYear = window.AcademicContext.getActiveAcademicYear();
      const activeTerm = window.AcademicContext.getActiveTerm();

      const studentsResult = await window.EnrollmentsAPI.getByClass(classId, {
        academic_year_id: activeYear?.id,
        term_id: activeTerm?.id
      });

      if (studentsResult.success && studentsResult.data) {
        // Extract enrollments array from the API response
        const enrollments = studentsResult.data.enrollments || studentsResult.data || [];
        this.renderBulkStreamStudents(enrollments);
      } else {
        container.innerHTML = '<p class="text-gray-500 text-center py-4">No students found in this class</p>';
      }
    } catch (error) {
      console.error('Error loading students for bulk stream assignment:', error);
      const container = document.getElementById('bulk-stream-students-container');
      container.innerHTML = '<p class="text-red-500 text-center py-4">Error loading students</p>';
    }
  },

  // Render students for bulk stream assignment
  renderBulkStreamStudents(enrollments) {
    const container = document.getElementById('bulk-stream-students-container');
    if (!container) return;

    // Filter out students who already have streams assigned
    const studentsWithoutStreams = enrollments.filter(enrollment =>
      !enrollment.stream_id || !enrollment.stream_name || enrollment.stream_name === 'No Stream'
    );

    if (studentsWithoutStreams.length === 0) {
      const totalStudents = enrollments.length;

      // Disable the Target Stream dropdown (don't hide it)
      const targetStreamSelect = document.getElementById('bulk-stream-target-stream-id');
      if (targetStreamSelect) {
        targetStreamSelect.disabled = true;
        targetStreamSelect.innerHTML = '<option value="">No stream assignment needed</option>';
        // Add visual styling to show it's disabled
        targetStreamSelect.classList.add('opacity-50', 'cursor-not-allowed');
      }

      // Add visual indication to the Target Stream label
      const targetStreamLabel = document.querySelector('label[for="bulk-stream-target-stream-id"]');
      if (targetStreamLabel) {
        targetStreamLabel.classList.add('opacity-50');
      }

      // Hide the action buttons container
      const buttonsContainer = document.querySelector('#bulk-stream-assignment-modal .flex.justify-end.space-x-3.pt-4.border-t');
      if (buttonsContainer) {
        buttonsContainer.style.display = 'none';
      }

      container.innerHTML = `
        <div class="text-center py-8">
          <div class="text-gray-500 mb-2">
            <i class="fas fa-check-circle text-blue-500 text-2xl mb-2"></i>
          </div>
          <p class="text-gray-600 font-medium">All students in this class already have streams assigned</p>
          <p class="text-sm text-gray-500 mt-1">
            ${totalStudents} student${totalStudents !== 1 ? 's' : ''} found, all with streams assigned
          </p>
        </div>
      `;
      return;
    }

    const totalStudents = enrollments.length;
    const studentsWithStreams = totalStudents - studentsWithoutStreams.length;

    // Re-enable the Target Stream dropdown
    const targetStreamSelect = document.getElementById('bulk-stream-target-stream-id');
    if (targetStreamSelect) {
      targetStreamSelect.disabled = false;
      // Remove visual styling that shows it's disabled
      targetStreamSelect.classList.remove('opacity-50', 'cursor-not-allowed');
    }

    // Remove visual indication from the Target Stream label
    const targetStreamLabel = document.querySelector('label[for="bulk-stream-target-stream-id"]');
    if (targetStreamLabel) {
      targetStreamLabel.classList.remove('opacity-50');
    }

    // Show and re-enable the action buttons container
    const buttonsContainer = document.querySelector('#bulk-stream-assignment-modal .flex.justify-end.space-x-3.pt-4.border-t');
    if (buttonsContainer) {
      buttonsContainer.style.display = 'flex';
    }

    const assignButton = document.getElementById('save-bulk-stream-assignment');
    if (assignButton) {
      assignButton.disabled = false;
      assignButton.textContent = 'Assign Streams';
    }

    container.innerHTML = `
      <div class="space-y-2">
        ${studentsWithStreams > 0 ? `
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <div class="flex items-center text-blue-800 text-sm">
              <i class="fas fa-info-circle mr-2"></i>
              <span>
                Showing ${studentsWithoutStreams.length} students without streams.
                ${studentsWithStreams} student${studentsWithStreams !== 1 ? 's' : ''} already have streams assigned.
              </span>
            </div>
          </div>
        ` : ''}

        <div class="flex items-center mb-4">
          <input type="checkbox" id="select-all-stream-students" class="rounded border-gray-300 mr-2" onchange="StudentEnrollmentComponent.toggleAllStreamStudents(this.checked)">
          <label for="select-all-stream-students" class="text-sm font-medium text-gray-700">
            Select All Students (${studentsWithoutStreams.length} without streams)
          </label>
        </div>

        ${studentsWithoutStreams.map(enrollment => `
          <label class="flex items-center space-x-3 p-3 hover:bg-gray-100 rounded cursor-pointer border border-gray-200">
            <input type="checkbox" name="selected_stream_students"
                   value="${enrollment.student_id}"
                   data-student-type="${enrollment.student_type}"
                   class="rounded border-gray-300">
            <div class="flex-1">
              <div class="text-sm font-medium text-gray-900">
                ${enrollment.student_name || `${enrollment.first_name || ''} ${enrollment.last_name || ''}`.trim() || 'Unknown Student'}
              </div>
              <div class="text-xs text-gray-500">
                ${enrollment.admission_number} • <span class="text-orange-600 font-medium">No Stream Assigned</span>
              </div>
            </div>
            <div class="text-xs text-orange-600">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
          </label>
        `).join('')}
      </div>
    `;
  },

  // Toggle all students for stream assignment
  toggleAllStreamStudents(checked) {
    const checkboxes = document.querySelectorAll('input[name="selected_stream_students"]');
    checkboxes.forEach(checkbox => {
      checkbox.checked = checked;
    });
  },

  // Save bulk stream assignment
  async saveBulkStreamAssignment() {
    try {
      const targetStreamId = document.getElementById('bulk-stream-target-stream-id').value;
      const selectedStudents = Array.from(document.querySelectorAll('input[name="selected_stream_students"]:checked'))
        .map(checkbox => checkbox.value);

      if (!targetStreamId) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select a target stream', 'error');
        }
        return;
      }

      if (selectedStudents.length === 0) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select at least one student', 'error');
        }
        return;
      }

      // Set loading state
      if (window.SRDesignSystem?.forms) {
        window.SRDesignSystem.forms.setButtonLoading('save-bulk-stream-assignment', true);
      }

      // Get student data from checkboxes
      const selectedCheckboxes = document.querySelectorAll('input[name="selected_stream_students"]:checked');
      const studentUpdates = Array.from(selectedCheckboxes).map(checkbox => ({
        student_id: checkbox.value,
        student_type: checkbox.dataset.studentType,
        new_stream_id: targetStreamId
      }));

      // Update each selected student's stream using the new API
      const updatePromises = studentUpdates.map(update =>
        window.EnrollmentsAPI.changeStream(update)
      );

      const results = await Promise.all(updatePromises);
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;

      if (successCount > 0) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show(
            `Successfully assigned ${successCount} students to stream${failureCount > 0 ? ` (${failureCount} failed)` : ''}`,
            failureCount > 0 ? 'warning' : 'success'
          );
        }

        // Close modal and refresh data
        document.getElementById('bulk-stream-assignment-modal')?.remove();
        this.loadCurrentEnrollments();
      } else {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Failed to assign streams to students', 'error');
        }
      }

    } catch (error) {
      console.error('Bulk stream assignment error:', error);
      if (window.SRDesignSystem?.notifications) {
        window.SRDesignSystem.notifications.show('Failed to assign streams', 'error');
      }
    } finally {
      // Reset loading state
      if (window.SRDesignSystem?.forms) {
        window.SRDesignSystem.forms.setButtonLoading('save-bulk-stream-assignment', false);
      }
    }
  },

  // Apply filters and reload enrollments
  applyFilters() {
    console.log('🔍 Applying enrollment filters...');
    this.loadCurrentEnrollments();
  },

  // Initialize event listeners
  initializeEventListeners() {
    console.log('🔧 Initializing StudentEnrollmentComponent event listeners...');

    // Search input with debounce for better performance
    const searchInput = document.getElementById('search');
    if (searchInput) {
      const debouncedSearch = window.ComponentLifecycleManager?.debounce(() => {
        this.loadCurrentEnrollments();
      }, 300) || (() => this.loadCurrentEnrollments());

      searchInput.addEventListener('input', debouncedSearch);
    }

    console.log('✅ StudentEnrollmentComponent event listeners initialized');
  },

  // Navigate to academic year setup
  navigateToAcademicSetup() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('academic-year-setup');
    }
  }
};


// Student Management Selection Component
const StudentManagementSelectionComponent = {
  // Render management selection options
  render() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Student Management',
          'Choose the type of student management you want to perform'
        )}

        <!-- Management Selection Cards -->
        <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap} max-w-4xl mx-auto">
          <!-- Student Records Card -->
          <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow duration-300">
            <div class="p-6">
              <div class="flex items-center space-x-3 mb-4">
                <div class="bg-blue-100 p-2 rounded-lg">
                  <i class="fas fa-user-edit text-blue-600 text-lg"></i>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">Student Records</h3>
                  <p class="text-sm text-gray-500">Personal information and subject assignments</p>
                </div>
              </div>
              <div class="space-y-3">
                <div class="flex items-start space-x-3">
                  <i class="fas fa-user text-blue-500 mt-1 text-sm"></i>
                  <div>
                    <h4 class="font-medium text-gray-900 text-sm">Personal Information</h4>
                    <p class="text-xs text-gray-600">Admission number, name, gender, date of birth, passport photo</p>
                  </div>
                </div>
                <div class="flex items-start space-x-3">
                  <i class="fas fa-book-open text-blue-500 mt-1 text-sm"></i>
                  <div>
                    <h4 class="font-medium text-gray-900 text-sm">Subject Assignments</h4>
                    <p class="text-xs text-gray-600">O-Level & A-Level subject selections and modifications</p>
                  </div>
                </div>
              </div>
              <div class="mt-6">
                ${SRDesignSystem.forms.button('select-student-profiles', 'Manage Records', 'primary', {
                  icon: 'fas fa-user-edit',
                  onclick: 'StudentManagementSelectionComponent.selectManagementType(\'profiles\')',
                  class: 'w-full flex items-center justify-center',
                  type: 'button'
                })}
              </div>
            </div>
          </div>

          <!-- Enrollment Management Card -->
          <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow duration-300">
            <div class="p-6">
              <div class="flex items-center space-x-3 mb-4">
                <div class="bg-blue-100 p-2 rounded-lg">
                  <i class="fas fa-users text-blue-600 text-lg"></i>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">Student Enrollments</h3>
                  <p class="text-sm text-gray-500">Class assignments and academic progression</p>
                </div>
              </div>
              <div class="space-y-3">
                <div class="flex items-start space-x-3">
                  <i class="fas fa-door-open text-blue-500 mt-1 text-sm"></i>
                  <div>
                    <h4 class="font-medium text-gray-900 text-sm">Class & Stream Assignments</h4>
                    <p class="text-xs text-gray-600">Current class, stream, academic year and term tracking</p>
                  </div>
                </div>
                <div class="flex items-start space-x-3">
                  <i class="fas fa-arrow-up text-blue-500 mt-1 text-sm"></i>
                  <div>
                    <h4 class="font-medium text-gray-900 text-sm">Student Promotions</h4>
                    <p class="text-xs text-gray-600">Promote between classes, and academic years</p>
                  </div>
                </div>
              </div>
              <div class="mt-6">
                ${SRDesignSystem.forms.button('select-enrollment-management', 'Manage Enrollments', 'primary', {
                  icon: 'fas fa-users',
                  onclick: 'StudentManagementSelectionComponent.selectManagementType(\'enrollment\')',
                  class: 'w-full flex items-center justify-center',
                  type: 'button'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Add event listeners for management type selection buttons
    const profilesButton = document.getElementById('select-student-profiles');
    if (profilesButton) {
      profilesButton.addEventListener('click', () => {
        this.selectManagementType('profiles');
      });
    }

    const enrollmentButton = document.getElementById('select-enrollment-management');
    if (enrollmentButton) {
      enrollmentButton.addEventListener('click', () => {
        this.selectManagementType('enrollment');
      });
    }
  },

  // Handle management type selection
  selectManagementType(type) {
    // Navigate directly to the appropriate management page using PageRouter
    if (type === 'profiles') {
      if (window.PageRouter) {
        window.PageRouter.loadPage('student-profiles');
      }
    } else if (type === 'enrollment') {
      if (window.PageRouter) {
        window.PageRouter.loadPage('enrollment-management');
      }
    }
  },

  // Initialize component (required by page router)
  async init() {
    // Reset component state
    this.resetComponentState();

    // Load initial data first
    await StudentManagementComponents.loadInitialData();

    // Initialize event listeners directly - DOM should be ready due to lifecycle manager
    this.initializeEventListeners();
  },

  // Reset component state
  resetComponentState() {
    // Reset any component-specific state if needed
  },

  // Cleanup component
  cleanup() {
    console.log('🧹 Cleaning up StudentManagementSelectionComponent...');
    this.resetComponentState();
    console.log('✅ StudentManagementSelectionComponent cleanup completed');
  }
};

// Export components to global scope
window.RegisterStudentComponent = RegisterStudentComponent;
window.StudentLevelSelectionComponent = StudentLevelSelectionComponent;
window.ManageStudentsComponent = ManageStudentsComponent;
window.StudentEnrollmentComponent = StudentEnrollmentComponent;
window.StudentManagementSelectionComponent = StudentManagementSelectionComponent;
window.StudentManagementComponents = StudentManagementComponents;
