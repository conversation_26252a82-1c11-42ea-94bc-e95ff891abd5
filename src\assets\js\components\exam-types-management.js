// SmartReport - Exam Types Management Component
// Manages predefined exam types and their activation for specific classes
// Flexible system - any exam type can be assigned to any class based on school's needs

// Uses global API services: window.API
// Uses global config: window.SRConfig
// Uses global design system: window.SRDesignSystem

const ExamTypesManagementComponent = {
  // Component state
  state: {
    examTypes: [],
    classes: [],
    classExamTypes: [], // Stores which exam types are active for which classes
    academicYears: [],
    terms: [],
    loading: false,
    selectedClass: null,
    selectedTerm: null,
    filters: {
      academicYear: '',
      term: '',
      class: ''
    }
  },

  // Initialize component
  async init() {
    console.log('🔧 Initializing Exam Types Management Component...');

    // Reset state to ensure fresh start when component is initialized
    this.resetComponentState();

    // Check academic context first
    await window.AcademicContext.initialize();
    const activeYear = window.AcademicContext.getActiveAcademicYear();
    const activeTerm = window.AcademicContext.getActiveTerm();

    if (!activeYear || !activeTerm) {
      console.log('⚠️ No active academic year or term found - component will show error state');
      return;
    }

    // Load initial data
    await this.loadInitialData();

    // Re-render the component with loaded data
    this.reRenderComponent();

    // Initialize directly - DOM should be ready due to lifecycle manager
    console.log('🔄 Populating Exam Types Management UI...');
    this.populateClassExamTypes(); // Render the table with loaded data
    this.initializeEventListeners();
    console.log('✅ Exam Types Management Component initialized successfully');
  },

  // Reset component state to initial values
  resetComponentState() {
    this.state.selectedClass = null;
    this.state.selectedTerm = null;
    this.state.loading = false;
    this.state.filters = {
      academicYear: '',
      term: '',
      class: ''
    };
    console.log('🔄 Component state reset to initial values');
  },

  // Re-render component with updated data
  reRenderComponent() {
    const contentArea = document.getElementById('content-area');
    if (contentArea) {
      contentArea.innerHTML = this.render();
    }
  },

  // Cleanup method called when navigating away from component
  cleanup() {
    console.log('🧹 Cleaning up Exam Types Management Component...');
    this.resetComponentState();

    // Hide any open modals
    const modals = ['configure-exam-types-modal'];
    modals.forEach(modalId => {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.classList.add('hidden');
      }
    });

    // Remove any dynamically created modals
    const dynamicModal = document.getElementById('configure-class-modal');
    if (dynamicModal) {
      dynamicModal.remove();
    }

    console.log('✅ Component cleanup completed');
  },

  // Load initial data
  async loadInitialData() {
    try {
      this.state.loading = true;
      
      // Load all required data in parallel
      const [examTypesResponse, classesResponse, academicYearsResponse, termsResponse] = await Promise.all([
        window.API.get('/academic/exam-types'),
        window.API.get('/academic/classes'),
        window.API.get('/academic/years'),
        window.API.get('/academic/terms')
      ]);

      this.state.examTypes = examTypesResponse.success ? (examTypesResponse.data || []) : [];
      this.state.classes = classesResponse.success ? (classesResponse.data || []) : [];
      this.state.academicYears = academicYearsResponse.success ? (academicYearsResponse.data || []) : [];
      this.state.terms = termsResponse.success ? (termsResponse.data || []) : [];

      // Load class-exam type associations
      await this.loadClassExamTypes();
      
    } catch (error) {
      console.error('Failed to load initial data:', error);
      this.showError('Failed to load exam types data');
    } finally {
      this.state.loading = false;
    }
  },

  // Load class-exam type associations
  async loadClassExamTypes() {
    try {
      // Get current academic year and term for filtering
      const currentAcademicYear = this.state.academicYears.find(ay => ay.is_active);
      const currentTerm = this.state.terms.find(t => t.is_active);

      if (!currentAcademicYear || !currentTerm) {
        console.warn('No active academic year or term found');
        this.state.classExamTypes = [];
        return;
      }

      // Load associations from the database
      const response = await window.API.get(`/academic/class-exam-types?academic_year_id=${currentAcademicYear.id}&term_id=${currentTerm.id}`);

      if (response.success) {
        this.state.classExamTypes = response.data || [];
      } else {
        console.warn('Failed to load class exam types:', response.message);
        this.state.classExamTypes = [];
      }

    } catch (error) {
      console.error('Failed to load class exam types:', error);
      this.state.classExamTypes = [];
    }
  },



  // Render academic context error
  renderAcademicContextError() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Exam Types Management',
          'Configure exam types and their weights for each class'
        )}

        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-600')}
            </div>
            <div class="ml-4">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-red-900">Academic Context Required</h3>
              <p class="text-red-700 mt-2">
                You cannot configure exam types without setting up the academic year and terms first.
              </p>
              <p class="text-red-700 mt-1">
                Please go to <strong>Academic Year → Setup</strong> to configure your academic year and terms before managing exam types.
              </p>
              <div class="mt-4 text-center">
                ${SRDesignSystem.forms.button('setup-academic-exam-types', 'Setup Academic Year & Terms', 'danger', {
                  icon: 'fas fa-calendar-plus',
                  onclick: 'ExamTypesManagementComponent.navigateToAcademicSetup()'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Render main component
  render() {
    try {
      console.log('🎨 ExamTypesManagementComponent.render() called');

      // Check if academic context is available
      const hasAcademicContext = window.AcademicContext &&
                                window.AcademicContext.getActiveAcademicYear() &&
                                window.AcademicContext.getActiveTerm();

      if (!hasAcademicContext) {
        return this.renderAcademicContextError();
      }

      // Show loading state if data is still loading
      if (this.state.loading) {
        // Use centralized loading state from page router
        const container = document.getElementById('content-area');
        if (container && window.PageRouter) {
          window.PageRouter.showLoadingState(container, 'Loading exam types data...');
        }
        return '';
      }

      return this.renderConfigureInterface();
    } catch (error) {
      console.error('❌ Error in render method:', error);
      return `
        <div class="space-y-6">
          <div class="bg-red-50 border border-red-200 rounded-xl p-6">
            <h3 class="text-lg font-semibold text-red-900">Rendering Error</h3>
            <p class="text-red-700 mt-2">Failed to render exam types management interface.</p>
            <p class="text-sm text-red-600 mt-1">Error: ${error.message}</p>
          </div>
        </div>
      `;
    }
  },





  // Render configure interface
  renderConfigureInterface() {
    try {
      console.log('🎨 Rendering configure interface...');

      // Show loading state if data is still loading
      if (this.state.loading) {
        return `
          <div class="space-y-6">
            ${SRDesignSystem.layouts.pageHeader(
              'Configure Exams Types',
              'Select a class and term to configure exam types'
            )}
            ${SRDesignSystem.layouts.loadingState('Loading configuration data...')}
          </div>
        `;
      }

      return `
        <div class="space-y-6">
          ${SRDesignSystem.layouts.pageHeader(
            'Configure Exams Types',
            `Configure exam types for classes in the current term`
          )}



        <!-- Class Configuration -->
        <div id="class-configuration" class="hidden bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <!-- Configuration content will be populated here -->
        </div>

        <!-- All Classes Overview -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-4 border-b border-primary-200">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-primary-900 flex items-center">
              ${SRDesignSystem.components.icon('fas fa-clipboard-list', 'base', 'primary-600')}
              <span class="ml-3">Examinations</span>
            </h3>
          </div>

          <!-- Class-Exam Types Table -->
          <div class="overflow-x-auto">
            <table class="${SRDesignSystem.responsive.table.base}">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Class</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Exam Types</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Actions</th>
                </tr>
              </thead>
              <tbody id="class-exam-types-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Table content will be populated by JavaScript -->
              </tbody>
            </table>
          </div>
        </div>

        <!-- System Information -->
        <div class="bg-blue-50 rounded-xl border border-blue-200 p-6">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-info-circle', 'xl', 'blue-400')}
            </div>
            <div class="ml-3">
              <h3 class="${SRDesignSystem.responsive.text.sm} font-medium text-blue-800">Flexible Configuration Features</h3>
              <div class="mt-2 ${SRDesignSystem.responsive.text.sm} text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                  <li><strong>Flexible Assignment:</strong> Any exam type can be assigned to any class</li>
                  <li><strong>Class-Specific:</strong> Configure exam types for individual classes</li>
                  <li><strong>Term-Based:</strong> Different terms can have different exam requirements</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modals will be added here -->
      ${this.renderModals()}
      `;
    } catch (error) {
      console.error('❌ Error rendering configure interface:', error);
      return `
        <div class="space-y-6">
          <div class="bg-red-50 border border-red-200 rounded-xl p-6">
            <h3 class="text-lg font-semibold text-red-900">Rendering Error</h3>
            <p class="text-red-700 mt-2">Failed to render configure interface.</p>
            <p class="text-sm text-red-600 mt-1">Error: ${error.message}</p>
          </div>
        </div>
      `;
    }
  },

  // Render weights interface
  renderWeightsInterface() {
    try {
      console.log('🎨 Rendering weights interface...');
      return `
        <div class="space-y-6">
          ${SRDesignSystem.layouts.pageHeader(
            'Manage Exam Set Weights',
            'Configure weight percentages for exam types. These weights determine how much each exam type contributes to final grades.'
          )}



        <!-- Exam Types Selection -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Select Exam Type to Configure</h3>
          <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
            ${SRDesignSystem.forms.select('exam_type_selector', 'Choose Exam Type', [], '')}
            <div class="flex items-end">
              ${SRDesignSystem.forms.button('load-exam-type', 'Load Configuration', 'primary', {
                icon: 'fas fa-cog',
                onclick: 'ExamTypesManagementComponent.loadExamTypeConfiguration()'
              })}
            </div>
          </div>
        </div>

        <!-- Exam Type Configuration -->
        <div id="exam-type-configuration" class="hidden bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <!-- Configuration content will be populated here -->
        </div>

        <!-- All Exam Types Table -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">All Exam Types</h3>
              <p class="text-sm text-gray-600 mt-1">Current weight percentages for all exam types</p>
            </div>
            <div class="flex gap-3">
              ${SRDesignSystem.forms.button('bulk-edit-weights', 'Bulk Edit Weights', 'secondary', {
                icon: 'fas fa-edit',
                onclick: 'ExamTypesManagementComponent.showBulkEditWeightsModal()'
              })}
            </div>
          </div>

          <!-- Exam Types Table -->
          <div class="overflow-x-auto">
            <table class="${SRDesignSystem.responsive.table.base}">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Exam Type</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Short Name</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Weight %</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Status</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody id="exam-types-weights-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Table content will be populated by JavaScript -->
              </tbody>
            </table>
          </div>
        </div>

        <!-- System Information -->
        <div class="bg-blue-50 rounded-xl border border-blue-200 p-6">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <i class="fas fa-info-circle text-blue-400 text-xl"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800">How Exam Type Weights Work</h3>
              <div class="mt-2 text-sm text-blue-700">
                <p class="mb-3">Exam type weights determine how much each exam contributes to a student's final grade.</p>
                <ul class="list-disc list-inside space-y-1">
                  <li><strong>Global Configuration:</strong> Weights apply to all classes that use the exam type</li>
                  <li><strong>Percentage Based:</strong> All active exam types for a class should total 100%</li>
                  <li><strong>Flexible System:</strong> Different classes can use different combinations of exam types</li>
                  <li><strong>Validation:</strong> System ensures weights are logical and add up correctly</li>
                </ul>
                <div class="mt-3 p-3 bg-blue-100 rounded-lg">
                  <p class="text-xs font-medium text-blue-900">💡 Example: If a class uses "Mid Term" (30%) and "End of Term" (70%), these weights apply to all students in that class.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      `;
    } catch (error) {
      console.error('❌ Error rendering weights interface:', error);
      return `
        <div class="space-y-6">
          <div class="bg-red-50 border border-red-200 rounded-xl p-6">
            <h3 class="text-lg font-semibold text-red-900">Rendering Error</h3>
            <p class="text-red-700 mt-2">Failed to render weights interface.</p>
            <p class="text-sm text-red-600 mt-1">Error: ${error.message}</p>
          </div>
        </div>
      `;
    }
  },









  // Show class configuration
  showClassConfiguration(classData, termData) {
    const configContainer = document.getElementById('class-configuration');
    if (!configContainer) return;

    // Get current associations for this class and term
    const classAssociations = this.state.classExamTypes.filter(
      a => a.class_id == classData.id && a.term_id == termData.id
    );

    configContainer.innerHTML = `
      <div class="space-y-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Configure: ${classData.name} - ${termData.name}</h3>
            <p class="text-sm text-gray-600 mt-1">Select which exam types are active for this class in this term</p>
          </div>
          <div class="flex gap-3">
            ${SRDesignSystem.forms.button('save-class-config', 'Save', 'primary', {
              onclick: 'ExamTypesManagementComponent.saveClassConfiguration()'
            })}
            ${SRDesignSystem.forms.button('cancel-class-config', 'Cancel', 'secondary', {
              onclick: 'ExamTypesManagementComponent.hideClassConfiguration()'
            })}
          </div>
        </div>

        <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap}">
          ${this.state.examTypes.map(examType => {
            const association = classAssociations.find(a => a.exam_type_id === examType.id);
            const isActive = association ? true : false; // Association exists = active

            return `
              <div class="border border-gray-200 rounded-lg ${SRDesignSystem.responsive.spacing.padding} ${isActive ? 'bg-green-50 border-green-200' : 'bg-gray-50'}">
                <div class="flex items-center justify-between mb-3">
                  <div>
                    <h4 class="font-medium text-gray-900">${examType.name}</h4>
                    <p class="${SRDesignSystem.responsive.text.sm} text-gray-500">${examType.short_name}</p>
                  </div>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer exam-type-checkbox"
                           data-exam-type-id="${examType.id}"
                           ${isActive ? 'checked' : ''}>
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                  </label>
                </div>
                <div class="${SRDesignSystem.responsive.text.sm} text-gray-600">
                  <div class="flex items-center justify-between">
                    <span>Weight:</span>
                    <span class="font-medium">${examType.weight_percentage || 0}%</span>
                  </div>
                  <div class="flex items-center justify-between mt-1">
                    <span>Status:</span>
                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                      examType.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }">
                      ${examType.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
              </div>
            `;
          }).join('')}
        </div>

        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <i class="fas fa-exclamation-triangle text-yellow-600"></i>
            </div>
            <div class="ml-3">
              <h4 class="text-sm font-medium text-yellow-800">Configuration Notes</h4>
              <div class="mt-2 text-sm text-yellow-700">
                <ul class="list-disc list-inside space-y-1">
                  <li>Only active exam types can be selected for classes</li>
                  <li>Any exam type can be assigned to any class (flexible system)</li>
                  <li>Consider the total weight percentage when selecting exam types</li>
                  <li>Different classes can use different exam type combinations</li>
                  <li>Changes apply only to the selected class and term</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    configContainer.classList.remove('hidden');
  },

  // Hide class configuration
  hideClassConfiguration() {
    const configContainer = document.getElementById('class-configuration');
    if (configContainer) {
      configContainer.classList.add('hidden');
    }
    this.state.selectedClass = null;
    this.state.selectedTerm = null;
  },

  // Save class configuration
  async saveClassConfiguration() {
    if (!this.state.selectedClass || !this.state.selectedTerm) {
      this.showError('No class or term selected');
      return;
    }

    try {
      // Show loading state
      const saveButton = document.getElementById('save-class-config');
      if (saveButton) {
        saveButton.disabled = true;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
      }

      // Get current academic year
      const currentAcademicYear = this.state.academicYears.find(ay => ay.is_active);
      if (!currentAcademicYear) {
        this.showError('No active academic year found');
        return;
      }

      // Collect selected exam types
      const checkboxes = document.querySelectorAll('.exam-type-checkbox');
      const promises = [];

      checkboxes.forEach(checkbox => {
        const examTypeId = parseInt(checkbox.dataset.examTypeId);

        const associationData = {
          class_id: this.state.selectedClass.id,
          exam_type_id: examTypeId,
          academic_year_id: currentAcademicYear.id,
          term_id: this.state.selectedTerm.id,
        };

        promises.push(window.API.post('/academic/class-exam-types', associationData));
      });

      await Promise.all(promises);

      // Reload data and update display
      await this.loadClassExamTypes();
      this.populateClassExamTypes(); // Refresh the table after saving
      this.hideClassConfiguration();

      this.showSuccess(`Configuration saved for ${this.state.selectedClass.name} - ${this.state.selectedTerm.name}`);
    } catch (error) {
      console.error('Error saving class configuration:', error);
      this.showError('Failed to save class configuration');
    } finally {
      // Reset button state
      const saveButton = document.getElementById('save-class-config');
      if (saveButton) {
        saveButton.disabled = false;
        saveButton.innerHTML = 'Save';
      }
    }
  },

  // Render modals
  renderModals() {
    return `




      <!-- Configure Exam Types Modal -->
      <div id="configure-exam-types-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-10 mx-auto p-5 border w-full max-w-3xl shadow-lg rounded-xl bg-white">
          <div class="mt-3">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
              <h3 class="text-lg font-semibold text-gray-900">Exam Types and Weights</h3>
              <button onclick="ExamTypesManagementComponent.closeConfigureExamTypesModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
              </button>
            </div>

            <div class="mt-6 space-y-6">
              <form id="configure-exam-types-form" class="space-y-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-3">
                    Congfigure Exam Types & Weights for: <span id="selected-class-name" class="font-bold"></span>
                  </label>

                  <!-- Weight Total Indicator -->
                  <div class="mb-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                    <div class="flex items-center justify-between">
                      <span class="text-sm font-medium text-gray-700">Total Weight:</span>
                      <div class="flex items-center">
                        <span id="total-weight-display" class="text-lg font-bold text-gray-900">0</span>
                        <span class="text-sm text-gray-500 ml-1">% of 100%</span>
                        <div id="weight-status-icon" class="ml-2">
                          <i class="fas fa-exclamation-triangle text-yellow-500" title="Weights must total 100%"></i>
                        </div>
                      </div>
                    </div>
                    <div class="mt-2">
                      <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="weight-progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                      </div>
                    </div>
                    <p id="weight-validation-message" class="text-xs text-yellow-600 mt-1">
                      Weights must total exactly 100% for the Two-Tier Assessment System
                    </p>
                  </div>

                  <div id="exam-types-selection" class="space-y-3 max-h-80 overflow-y-auto border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <!-- Exam types checkboxes with weight inputs will be populated here -->
                  </div>
                </div>

                <div class="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
                  ${SRDesignSystem.forms.button('cancel-configure-exam-types', 'Cancel', 'secondary', {
                    type: 'button',
                    onclick: 'ExamTypesManagementComponent.closeConfigureExamTypesModal()'
                  })}
                  ${SRDesignSystem.forms.button('save-configure-exam-types', 'Save Configuration', 'primary', {
                    type: 'submit'
                  })}
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    `;
  },



  // Populate class-exam types table
  populateClassExamTypes() {
    const tableBody = document.getElementById('class-exam-types-table-body');
    if (!tableBody) return;

    // Group associations by class
    const classesByGroup = {};
    this.state.classExamTypes.forEach(association => {
      if (!classesByGroup[association.class_id]) {
        classesByGroup[association.class_id] = {
          class_name: association.class_name,
          exam_types: []
        };
      }
      if (association) { // Association exists = active
        classesByGroup[association.class_id].exam_types.push(association);
      }
    });

    const allClasses = this.state.classes || [];

    if (allClasses.length === 0) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="3" class="px-6 py-8 text-center text-gray-500">
            <div class="flex flex-col items-center">
              <div class="text-gray-400 mb-3">
                ${SRDesignSystem.components.icon('fas fa-clipboard-list', '4xl', 'gray-300')}
              </div>
              <p class="${SRDesignSystem.responsive.text.lg} font-medium">No classes found</p>
            </div>
          </td>
        </tr>
      `;
      return;
    }

    tableBody.innerHTML = allClasses.map(classItem => {
      const classId = classItem.id;
      const className = classItem.name;
      const classConfig = classesByGroup[classId];
      const examTypes = classConfig ? classConfig.exam_types : [];

      return `
        <tr class="${examTypes.length === 0 ? 'bg-gray-50' : ''}">
          <td class="${SRDesignSystem.responsive.table.cell}">
            <div class="flex items-center">
              ${examTypes.length > 0 ?
                '<i class="fas fa-check-circle text-green-500 mr-2" title="Configured"></i>' :
                '<i class="fas fa-exclamation-circle text-yellow-500 mr-2" title="Not Configured"></i>'
              }
              <div class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-900">${className}</div>
            </div>
          </td>
          <td class="${SRDesignSystem.responsive.table.cell}">
            <div class="flex flex-wrap gap-2">
              ${examTypes.length > 0 ? examTypes.map(examType => `
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  ${examType.exam_type_name} (${examType.weight_percentage}%)
                </span>
              `).join('') : '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Not configured</span>'}
            </div>
          </td>
          <td class="${SRDesignSystem.responsive.table.cell} text-right text-sm font-medium">
            ${SRDesignSystem.tables.generateKebabMenu(`class-exam-${classId}`, [
              {
                label: 'Configure Exams',
                icon: 'fas fa-cog',
                onclick: `ExamTypesManagementComponent.configureClassExamTypes(${classId})`,
                color: 'indigo'
              }
            ], { id: classId })}
          </td>
        </tr>
      `;
    }).join('');
  },

  // Generate dropdown action menu for class exam type actions
  generateClassExamActionDropdown(classId, actions) {
    const dropdownId = `dropdown-class-exam-${classId}`;

    return `
      <div class="relative inline-block text-left">
        <button type="button"
                onclick="ExamTypesManagementComponent.toggleDropdown('${dropdownId}')"
                class="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors duration-150"
                title="More actions">
          <i class="fas fa-ellipsis-v text-sm"></i>
        </button>

        <div id="${dropdownId}"
             class="hidden fixed z-[9999] w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1"
             style="transform: translate(-100%, -100%);">
          ${actions.map(action => `
            <button onclick="ExamTypesManagementComponent.closeDropdown('${dropdownId}'); ${action.onclick}"
                    class="flex items-center w-full px-4 py-3 text-sm text-left hover:bg-gray-50 transition-colors duration-150 ${action.color || 'text-gray-700'}">
              <i class="${action.icon} w-4 mr-3"></i>
              ${action.label}
            </button>
          `).join('')}
        </div>
      </div>
    `;
  },

  // Toggle dropdown visibility
  toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (!dropdown) return;

    // Close all other dropdowns first
    this.closeAllDropdowns();

    // Toggle current dropdown
    dropdown.classList.toggle('hidden');

    // Position dropdown next to the button if dropdown is now open
    if (!dropdown.classList.contains('hidden')) {
      const button = dropdown.previousElementSibling;
      if (button) {
        const buttonRect = button.getBoundingClientRect();

        // Position dropdown to the left of the kebab icon, aligned to its top
        dropdown.style.position = 'fixed';
        dropdown.style.right = `${window.innerWidth - buttonRect.left + 8}px`; // 8px gap to the left of button
        dropdown.style.top = `${buttonRect.top}px`; // Same vertical position as button
        dropdown.style.zIndex = '1000';
      }

      setTimeout(() => {
        document.addEventListener('click', this.handleOutsideClick);
      }, 10);
    }
  },

  // Close specific dropdown
  closeDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (dropdown) {
      dropdown.classList.add('hidden');
    }
    document.removeEventListener('click', this.handleOutsideClick);
  },

  // Close all dropdowns
  closeAllDropdowns() {
    const dropdowns = document.querySelectorAll('[id^="dropdown-class-exam-"], [id^="dropdown-exam-type-"]');
    dropdowns.forEach(dropdown => {
      dropdown.classList.add('hidden');
    });
    document.removeEventListener('click', this.handleOutsideClick);
  },

  // Handle clicks outside dropdown
  handleOutsideClick(event) {
    const isDropdownButton = event.target.closest('[onclick*="toggleDropdown"]');
    const isDropdownContent = event.target.closest('[id^="dropdown-class-exam-"], [id^="dropdown-exam-type-"]');

    if (!isDropdownButton && !isDropdownContent) {
      ExamTypesManagementComponent.closeAllDropdowns();
    }
  },

  // Initialize event listeners
  initializeEventListeners() {
    console.log('✅ Event listeners initialized for Configure Exams Sets');
  },

  // Configure exam types for a specific class
  configureClassExamTypes(classId) {
    const classData = this.state.classes.find(c => c.id == classId);
    if (!classData) return;

    // Store selected class for later use
    this.state.selectedClass = classData;

    // Show the configuration modal
    this.showConfigureExamTypesModal();
  },

  // Show configure exam types modal
  showConfigureExamTypesModal() {
    const modal = document.getElementById('configure-exam-types-modal');
    if (modal && this.state.selectedClass) {
      modal.classList.remove('hidden');

      // Set the class name in the modal
      const classNameElement = document.getElementById('selected-class-name');
      if (classNameElement) {
        classNameElement.textContent = this.state.selectedClass.name;
      }

      this.populateExamTypesSelection();
      this.initializeConfigureFormListeners();

      // Initialize weight total calculation after a short delay to ensure DOM is ready
      setTimeout(() => {
        this.updateWeightTotal();
      }, 100);
    }
  },

  // Close configure exam types modal
  closeConfigureExamTypesModal() {
    const modal = document.getElementById('configure-exam-types-modal');
    if (modal) {
      modal.classList.add('hidden');
    }
    this.state.selectedClass = null;
  },

  // Populate exam types selection for configuration
  populateExamTypesSelection() {
    const container = document.getElementById('exam-types-selection');
    if (!container || !this.state.selectedClass) return;

    const classAssociations = this.state.classExamTypes.filter(a => a.class_id == this.state.selectedClass.id);

    if (!Array.isArray(this.state.examTypes) || this.state.examTypes.length === 0) {
      container.innerHTML = '<p class="text-gray-500 text-sm">No exam types available</p>';
      return;
    }

    let html = '';

    // Add select all checkbox
    html += `
      <div class="border-b border-gray-200 pb-3 mb-3">
        <label class="flex items-center">
          <input type="checkbox" id="select-all-exam-types" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
          <span class="ml-2 text-sm font-medium text-gray-900">Activate All Exam Types</span>
        </label>
      </div>
    `;

    // Configure exam types for selected class (only active exam types)
    this.state.examTypes.filter(examType => examType.is_active).forEach(examType => {
      const association = classAssociations.find(a => a.exam_type_id === examType.id);
      const isActive = association ? true : false; // Association exists = active
      const currentWeight = association && association.weight_percentage !== null && association.weight_percentage !== undefined ? association.weight_percentage : 0;

      html += `
        <div class="border border-gray-200 rounded-lg p-3 hover:bg-gray-50">
          <div class="flex items-center justify-between mb-2">
            <label class="flex items-center">
              <input type="checkbox" name="selected_exam_types" value="${examType.id}"
                     ${isActive ? 'checked' : ''}
                     class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                     onchange="ExamTypesManagementComponent.toggleWeightInput(${examType.id}, this.checked)">
              <div class="ml-3">
                <span class="text-sm font-medium text-gray-900">${examType.name}</span>
                <div class="text-xs text-gray-500">${examType.short_name}</div>
              </div>
            </label>
            <div class="text-xs px-2 py-1 rounded-full ${isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">
              ${isActive ? 'Active' : 'Inactive'}
            </div>
          </div>
          <div class="ml-6">
            <label class="block text-xs font-medium text-gray-700 mb-1">Weight Percentage</label>
            <input type="number"
                   id="weight_${examType.id}"
                   name="exam_type_weights"
                   data-exam-type-id="${examType.id}"
                   value="${currentWeight || ''}"
                   min="1"
                   max="100"
                   ${!isActive ? 'disabled' : ''}
                   class="w-20 px-2 py-1 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                   placeholder="0"
                   oninput="ExamTypesManagementComponent.updateWeightTotal()"
                   onchange="ExamTypesManagementComponent.updateWeightTotal()">
            <span class="text-xs text-gray-500 ml-1">% of summative portion</span>
          </div>
        </div>
      `;
    });

    container.innerHTML = html;

    // Add select all functionality
    const selectAllCheckbox = document.getElementById('select-all-exam-types');
    if (selectAllCheckbox) {
      selectAllCheckbox.addEventListener('change', (e) => {
        const examTypeCheckboxes = container.querySelectorAll('input[name="selected_exam_types"]');
        examTypeCheckboxes.forEach(checkbox => {
          checkbox.checked = e.target.checked;
          // Toggle weight inputs for each checkbox
          const examTypeId = checkbox.value;
          this.toggleWeightInput(examTypeId, e.target.checked);
        });
        // Update weight total after select all
        this.updateWeightTotal();
      });
    }
  },

  // Toggle weight input based on checkbox state
  toggleWeightInput(examTypeId, isChecked) {
    const weightInput = document.getElementById(`weight_${examTypeId}`);
    if (weightInput) {
      weightInput.disabled = !isChecked;
      if (!isChecked) {
        weightInput.value = 0;
      }
    }
    // Update total after toggling
    this.updateWeightTotal();
  },

  // Update weight total and validation
  updateWeightTotal() {
    const weightInputs = document.querySelectorAll('input[name="exam_type_weights"]:not([disabled])');
    let total = 0;

    weightInputs.forEach(input => {
      // Ensure we have a valid number, default to 0 if invalid
      const value = input.value && !isNaN(input.value) ? parseInt(input.value) : 0;
      total += value;
    });

    // Update display elements
    const totalDisplay = document.getElementById('total-weight-display');
    const progressBar = document.getElementById('weight-progress-bar');
    const statusIcon = document.getElementById('weight-status-icon');
    const validationMessage = document.getElementById('weight-validation-message');

    if (totalDisplay) totalDisplay.textContent = total;
    if (progressBar) progressBar.style.width = `${Math.min(total, 100)}%`;

    // Update validation status
    if (total === 100) {
      // Perfect - weights total 100%
      if (progressBar) progressBar.className = 'bg-green-600 h-2 rounded-full transition-all duration-300';
      if (statusIcon) statusIcon.innerHTML = '<i class="fas fa-check-circle text-green-500" title="Perfect! Weights total 100%"></i>';
      if (validationMessage) {
        validationMessage.textContent = '✓ Perfect! Weights total exactly 100%';
        validationMessage.className = 'text-xs text-green-600 mt-1';
      }
    } else if (total > 100) {
      // Over 100% - error
      if (progressBar) progressBar.className = 'bg-red-600 h-2 rounded-full transition-all duration-300';
      if (statusIcon) statusIcon.innerHTML = '<i class="fas fa-exclamation-circle text-red-500" title="Error: Weights exceed 100%"></i>';
      if (validationMessage) {
        validationMessage.textContent = `⚠️ Error: Weights total ${total}% (exceeds 100%)`;
        validationMessage.className = 'text-xs text-red-600 mt-1';
      }
    } else {
      // Under 100% - warning
      if (progressBar) progressBar.className = 'bg-yellow-600 h-2 rounded-full transition-all duration-300';
      if (statusIcon) statusIcon.innerHTML = '<i class="fas fa-exclamation-triangle text-yellow-500" title="Warning: Weights must total 100%"></i>';
      if (validationMessage) {
        validationMessage.textContent = `⚠️ Weights total ${total}% (need ${100 - total}% more to reach 100%)`;
        validationMessage.className = 'text-xs text-yellow-600 mt-1';
      }
    }

    // Enable/disable unselected exam types when total reaches 100%
    const examTypeCheckboxes = document.querySelectorAll('input[name="selected_exam_types"]');
    examTypeCheckboxes.forEach(checkbox => {
      if (!checkbox.checked) {
        // Disable unselected exam types when total is 100% or more
        checkbox.disabled = (total >= 100);

        // Add visual styling to disabled checkboxes
        const parentDiv = checkbox.closest('.border');
        if (parentDiv) {
          if (total >= 100) {
            parentDiv.classList.add('opacity-50', 'bg-gray-100');
            parentDiv.classList.remove('hover:bg-gray-50');
          } else {
            parentDiv.classList.remove('opacity-50', 'bg-gray-100');
            parentDiv.classList.add('hover:bg-gray-50');
          }
        }
      }
    });

    // Disable "Select All" checkbox when weight limit is reached
    const selectAllCheckbox = document.getElementById('select-all-exam-types');
    if (selectAllCheckbox) {
      selectAllCheckbox.disabled = (total >= 100);
      const selectAllParent = selectAllCheckbox.closest('.border-b');
      if (selectAllParent) {
        if (total >= 100) {
          selectAllParent.classList.add('opacity-50');
        } else {
          selectAllParent.classList.remove('opacity-50');
        }
      }
    }

    // Enable/disable save button based on validation
    const saveButton = document.getElementById('save-configure-exam-types');
    if (saveButton) {
      if (total === 100) {
        saveButton.disabled = false;
        saveButton.className = saveButton.className.replace('opacity-50 cursor-not-allowed', '');
      } else {
        saveButton.disabled = true;
        if (!saveButton.className.includes('opacity-50')) {
          saveButton.className += ' opacity-50 cursor-not-allowed';
        }
      }
    }
  },

  // Initialize configure form listeners
  initializeConfigureFormListeners() {
    const form = document.getElementById('configure-exam-types-form');
    if (form) {
      form.addEventListener('submit', (e) => {
        e.preventDefault();
        this.saveExamTypesConfiguration();
      });
    }
  },

  // Save exam types configuration
  async saveExamTypesConfiguration() {
    if (!this.state.selectedClass) {
      this.showError('No class selected');
      return;
    }

    // Store class name at the very beginning to avoid null reference issues
    const className = this.state.selectedClass.name;

    try {
      // Show loading state
      const saveButton = document.getElementById('save-configure-exam-types');
      if (saveButton) {
        saveButton.disabled = true;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
      }

      // Get selected exam types and their weights
      const selectedExamTypes = document.querySelectorAll('input[name="selected_exam_types"]:checked');
      const examTypeConfigs = [];
      let totalWeight = 0;

      // Collect configurations and validate weights
      selectedExamTypes.forEach(checkbox => {
        const examTypeId = parseInt(checkbox.value);
        const weightInput = document.getElementById(`weight_${examTypeId}`);
        const weight = parseInt(weightInput.value) || 0;

        // Validate weight is integer between 1-100
        if (weight < 1 || weight > 100) {
          throw new Error(`Weight for exam type must be between 1 and 100. Current: ${weight}%`);
        }

        examTypeConfigs.push({
          exam_type_id: examTypeId,
          weight_percentage: weight,
        });

        totalWeight += weight;
      });

      // Validate total weight equals 100% for Two-Tier Assessment System
      if (totalWeight !== 100) {
        this.showError(`Invalid weight configuration: Total is ${totalWeight}%. Weights must total exactly 100% for the Two-Tier Assessment System.`);
        return;
      }



      // Get current academic context
      const currentAcademicYear = this.state.academicYears.find(ay => ay.is_active);
      const currentTerm = this.state.terms.find(t => t.is_active);

      if (!currentAcademicYear || !currentTerm) {
        this.showError('No active academic year or term found');
        return;
      }

      // Get currently selected exam type IDs
      const selectedExamTypeIds = examTypeConfigs.map(config => config.exam_type_id);

      // First, get fresh data from database to avoid stale state issues
      await this.loadClassExamTypes();

      // Find existing associations for this class, academic year, and term
      const existingAssociations = this.state.classExamTypes.filter(assoc =>
        assoc.class_id === this.state.selectedClass.id &&
        assoc.academic_year_id === currentAcademicYear.id &&
        assoc.term_id === currentTerm.id &&
        assoc.id // Only process real database records
      );

      // Use Set to track unique association IDs to prevent duplicate deletions
      const associationsToDelete = new Set();
      existingAssociations.forEach(assoc => {
        const shouldDelete = !selectedExamTypeIds.includes(assoc.exam_type_id);
        if (shouldDelete && assoc.id && !associationsToDelete.has(assoc.id)) {
          associationsToDelete.add(assoc.id);
        }
      });

      // Handle deletions first (with individual error handling)
      const deletePromises = Array.from(associationsToDelete).map(async (associationId) => {
        try {
          await window.API.delete(`/academic/class-exam-types/${associationId}`);
          return { success: true, id: associationId };
        } catch (error) {
          console.warn(`Failed to delete association ${associationId}:`, error.message);
          return { success: false, id: associationId, error: error.message };
        }
      });

      // Execute deletions
      const deleteResults = await Promise.all(deletePromises);
      const failedDeletions = deleteResults.filter(result => !result.success);

      if (failedDeletions.length > 0) {
        console.warn('Some deletions failed:', failedDeletions);
      }

      // Handle selected exam types (create/update)
      const upsertPromises = examTypeConfigs.map(async (config) => {
        try {
          const payload = {
            class_id: this.state.selectedClass.id,
            exam_type_id: config.exam_type_id,
            academic_year_id: currentAcademicYear.id,
            term_id: currentTerm.id,
            weight_percentage: config.weight_percentage,
          };
          await window.API.post('/academic/class-exam-types', payload);
          return { success: true, exam_type_id: config.exam_type_id };
        } catch (error) {
          console.error(`Failed to save exam type ${config.exam_type_id}:`, error.message);
          return { success: false, exam_type_id: config.exam_type_id, error: error.message };
        }
      });

      // Execute upserts
      const upsertResults = await Promise.all(upsertPromises);
      const failedUpserts = upsertResults.filter(result => !result.success);

      // Check if any critical operations failed
      if (failedUpserts.length > 0) {
        throw new Error(`Failed to save some exam type configurations: ${failedUpserts.map(f => f.error).join(', ')}`);
      }

      // Reload data and update display
      await this.loadClassExamTypes();
      this.populateClassExamTypes();
      this.closeConfigureExamTypesModal();

      this.showSuccess(`Configuration saved for ${className}`);
    } catch (error) {
      console.error('Error saving exam types configuration:', error);
      this.showError('Failed to save exam types configuration');
    } finally {
      // Reset button state
      const saveButton = document.getElementById('save-configure-exam-types');
      if (saveButton) {
        saveButton.disabled = false;
        saveButton.innerHTML = 'Save';
      }
    }
  },


  // Utility methods
  showSuccess(message) {
    // Use SRDesignSystem notifications if available, fallback to simple notification
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, 'success');
    } else {
      // Fallback notification
      const notification = document.createElement('div');
      notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-md shadow-lg z-50';
      notification.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
      document.body.appendChild(notification);

      setTimeout(() => {
        notification.remove();
      }, 3000);
    }
  },

  showError(message) {
    // Use SRDesignSystem notifications if available, fallback to simple notification
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, 'error');
    } else {
      // Fallback notification
      const notification = document.createElement('div');
      notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-md shadow-lg z-50';
      notification.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i>${message}`;
      document.body.appendChild(notification);

      setTimeout(() => {
        notification.remove();
      }, 100);
    }
  },

  // Navigate to academic year setup
  navigateToAcademicSetup() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('academic-year-setup');
    }
  },

  // =============================================


  // Show exam type weight configuration
  showExamTypeWeightConfiguration(examType) {
    const configContainer = document.getElementById('exam-type-configuration');
    if (!configContainer) return;

    configContainer.innerHTML = `
      <div class="space-y-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Configure: ${examType.name}</h3>
            <p class="text-sm text-gray-600 mt-1">Adjust weight percentage and settings for this exam type</p>
          </div>
          <div class="flex gap-3">
            ${SRDesignSystem.forms.button('save-exam-type-config', 'Save', 'primary', {
              onclick: 'ExamTypesManagementComponent.saveExamTypeWeightConfiguration(' + examType.id + ')'
            })}
            ${SRDesignSystem.forms.button('cancel-exam-type-config', 'Cancel', 'secondary', {
              onclick: 'ExamTypesManagementComponent.hideExamTypeWeightConfiguration()'
            })}
          </div>
        </div>

        <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Exam Type Name</label>
              <input type="text" id="config_exam_name" value="${examType.name}" readonly
                     class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg bg-gray-50 text-gray-500">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Short Name</label>
              <input type="text" id="config_exam_short_name" value="${examType.short_name}" readonly
                     class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg bg-gray-50 text-gray-500">
            </div>
          </div>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Weight Percentage</label>
              <div class="relative">
                <input type="number" id="config_weight_percentage" value="${examType.weight_percentage}"
                       min="0" max="100" step="0.1"
                       class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
                <span class="absolute right-3 top-2 text-gray-500">%</span>
              </div>
              <p class="text-xs text-gray-500 mt-1">Enter the percentage this exam type contributes to final grades</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <div class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
                <span class="text-sm text-gray-600">Status managed by exam type configuration</span>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <i class="fas fa-exclamation-triangle text-yellow-600"></i>
            </div>
            <div class="ml-3">
              <h4 class="text-sm font-medium text-yellow-800">Important Notes</h4>
              <div class="mt-2 text-sm text-yellow-700">
                <ul class="list-disc list-inside space-y-1">
                  <li>Weight changes affect all classes that use this exam type</li>
                  <li>Ensure total weights for each class combination add up to 100%</li>
                  <li>Inactive exam types cannot be assigned to classes</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    configContainer.classList.remove('hidden');
  },

  // Hide exam type weight configuration
  hideExamTypeWeightConfiguration() {
    const configContainer = document.getElementById('exam-type-configuration');
    if (configContainer) {
      configContainer.classList.add('hidden');
    }
  },

  // Save exam type weight configuration
  async saveExamTypeWeightConfiguration(examTypeId) {
    try {
      const weightPercentage = parseFloat(document.getElementById('config_weight_percentage').value);

      if (isNaN(weightPercentage) || weightPercentage < 0 || weightPercentage > 100) {
        this.showError('Please enter a valid weight percentage between 0 and 100');
        return;
      }

      // Show loading state
      const saveButton = document.getElementById('save-exam-type-config');
      if (saveButton) {
        saveButton.disabled = true;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
      }

      const updateData = {
        weight_percentage: weightPercentage,
      };

      const response = await window.API.put(`/academic/exam-types/${examTypeId}`, updateData);

      if (response.success) {
        // Update local state
        const examTypeIndex = this.state.examTypes.findIndex(et => et.id === examTypeId);
        if (examTypeIndex !== -1) {
          this.state.examTypes[examTypeIndex].weight_percentage = weightPercentage;
        }

        // Update displays
        this.populateWeightsInterface();
        this.hideExamTypeWeightConfiguration();

        this.showSuccess('Exam type configuration updated successfully');
      } else {
        this.showError('Failed to update exam type configuration: ' + (response.message || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error saving exam type configuration:', error);
      this.showError('Failed to save exam type configuration');
    } finally {
      // Reset button state
      const saveButton = document.getElementById('save-exam-type-config');
      if (saveButton) {
        saveButton.disabled = false;
        saveButton.innerHTML = 'Save';
      }
    }
  },

  // Edit exam type weight (quick edit)
  editExamTypeWeight(examTypeId) {
    const examType = this.state.examTypes.find(et => et.id === examTypeId);
    if (!examType) return;

    // Set the selector and load configuration
    const examTypeSelect = document.getElementById('exam_type_selector');
    if (examTypeSelect) {
      examTypeSelect.value = examTypeId;
      this.loadExamTypeConfiguration();
    }
  },

  // Show bulk edit weights modal
  showBulkEditWeightsModal() {
    // Add bulk edit weights modal to the existing modals
    const existingModal = document.getElementById('bulk-edit-weights-modal');
    if (existingModal) {
      existingModal.remove();
    }

    const modalHtml = `
      <div id="bulk-edit-weights-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-3/4 max-w-4xl shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Bulk Edit Exam Type Weights</h3>
            <div id="bulk-edit-weights-content" class="space-y-4 max-h-96 overflow-y-auto">
              ${this.state.examTypes.map(examType => `
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div class="flex-1">
                    <div class="font-medium text-gray-900">${examType.name}</div>
                    <div class="text-sm text-gray-500">${examType.short_name}</div>
                  </div>
                  <div class="flex items-center gap-4">
                    <div class="flex items-center gap-2">
                      <label class="text-sm font-medium text-gray-700">Weight:</label>
                      <div class="relative">
                        <input type="number" id="bulk_weight_${examType.id}" value="${examType.weight_percentage}"
                               min="0" max="100" step="0.1"
                               class="w-20 px-2 py-1 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded text-center focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
                        <span class="absolute right-1 top-1 text-xs text-gray-500">%</span>
                      </div>
                    </div>
                    <div class="flex items-center gap-2">
                      <label class="text-sm font-medium text-gray-700">Status:</label>
                      <div class="px-2 py-1 border border-gray-300 rounded text-sm bg-gray-50">
                        <span class="text-gray-600">Managed by exam type</span>
                      </div>
                    </div>
                  </div>
                </div>
              `).join('')}
            </div>
            <div class="flex justify-end gap-3 mt-6">
              ${SRDesignSystem.forms.button('cancel-bulk-edit-weights', 'Cancel', 'secondary', {
                onclick: 'ExamTypesManagementComponent.closeBulkEditWeightsModal()'
              })}
              ${SRDesignSystem.forms.button('save-bulk-weights', 'Save All Changes', 'primary', {
                onclick: 'ExamTypesManagementComponent.saveBulkWeights()'
              })}
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
  },

  // Close bulk edit weights modal
  closeBulkEditWeightsModal() {
    const modal = document.getElementById('bulk-edit-weights-modal');
    if (modal) modal.remove();
  },

  // Save bulk weights
  async saveBulkWeights() {
    try {
      // Show loading state
      const saveButton = document.getElementById('save-bulk-weights');
      if (saveButton) {
        saveButton.disabled = true;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
      }

      const updates = [];
      let hasErrors = false;

      // Collect all updates
      this.state.examTypes.forEach(examType => {
        const weightInput = document.getElementById(`bulk_weight_${examType.id}`);
        const statusSelect = document.getElementById(`bulk_status_${examType.id}`);

        if (weightInput && statusSelect) {
          const weightPercentage = parseFloat(weightInput.value);

          if (isNaN(weightPercentage) || weightPercentage < 0 || weightPercentage > 100) {
            this.showError(`Invalid weight percentage for ${examType.name}. Must be between 0 and 100.`);
            hasErrors = true;
            return;
          }

          updates.push({
            id: examType.id,
            weight_percentage: weightPercentage,
          });
        }
      });

      if (hasErrors) return;

      // Save all updates
      const promises = updates.map(update =>
        window.API.put(`/academic/exam-types/${update.id}`, {
          weight_percentage: update.weight_percentage,
        })
      );

      await Promise.all(promises);

      // Update local state
      updates.forEach(update => {
        const examTypeIndex = this.state.examTypes.findIndex(et => et.id === update.id);
        if (examTypeIndex !== -1) {
          this.state.examTypes[examTypeIndex].weight_percentage = update.weight_percentage;
        }
      });

      // Update displays
      this.populateWeightsInterface();
      this.closeBulkEditWeightsModal();

      this.showSuccess(`Successfully updated ${updates.length} exam type(s)`);
    } catch (error) {
      console.error('Bulk save error:', error);
      this.showError('Failed to save exam type weights');
    } finally {
      // Reset button state
      const saveButton = document.getElementById('save-bulk-weights');
      if (saveButton) {
        saveButton.disabled = false;
        saveButton.innerHTML = 'Save All Changes';
      }
    }
  }
};

// Export to global scope
window.ExamTypesManagementComponent = ExamTypesManagementComponent;
