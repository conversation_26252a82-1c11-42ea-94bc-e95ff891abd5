// SmartReport Component Lifecycle Manager
// Unified solution for component initialization, DOM waiting, cleanup, and re-initialization

const ComponentLifecycleManager = {
  // Component registry and state
  state: {
    registeredComponents: new Map(),
    activeComponents: new Map(),
    initializationQueue: new Set(),
    isInitializing: false
  },

  // Register a component with its configuration
  registerComponent(name, component, config = {}) {
    const componentConfig = {
      component,
      name,
      requiredElements: config.requiredElements || [],
      dependencies: config.dependencies || [],
      initTimeout: config.initTimeout || 5000,
      retryAttempts: config.retryAttempts || 3,
      retryDelay: config.retryDelay || 500,
      cleanupOnNavigate: config.cleanupOnNavigate !== false,
      ...config
    };

    this.state.registeredComponents.set(name, componentConfig);
    console.log(`📝 Component registered: ${name}`);
    return componentConfig;
  },

  // Wait for DOM elements to be available
  async waitForElements(selectors, timeout = 5000, retryInterval = 100) {
    if (!Array.isArray(selectors)) {
      selectors = [selectors];
    }

    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const foundElements = {};

      const checkElements = () => {
        let allFound = true;

        for (const selector of selectors) {
          if (!foundElements[selector]) {
            const element = document.querySelector(selector);
            if (element) {
              foundElements[selector] = element;
            } else {
              allFound = false;
            }
          }
        }

        if (allFound) {
          resolve(foundElements);
          return;
        }

        if (Date.now() - startTime > timeout) {
          const missingSelectors = selectors.filter(s => !foundElements[s]);
          reject(new Error(`Elements not found within ${timeout}ms: ${missingSelectors.join(', ')}`));
          return;
        }

        setTimeout(checkElements, retryInterval);
      };

      checkElements();
    });
  },

  // Initialize a component with proper lifecycle management
  async initializeComponent(name, pageId = null) {
    if (this.state.initializationQueue.has(name)) {
      console.log(`⏳ Component ${name} already in initialization queue`);
      return;
    }

    const config = this.state.registeredComponents.get(name);
    if (!config) {
      console.error(`❌ Component ${name} not registered`);
      return false;
    }

    this.state.initializationQueue.add(name);

    try {
      console.log(`🔧 Initializing component: ${name}`);

      // Check dependencies first
      if (config.dependencies.length > 0) {
        for (const dep of config.dependencies) {
          if (!this.state.activeComponents.has(dep)) {
            console.log(`⏳ Waiting for dependency: ${dep}`);
            await this.initializeComponent(dep);
          }
        }
      }

      // Check if component requires academic context and if it's available
      const requiresAcademicContext = this.componentRequiresAcademicContext(name);
      const hasAcademicContext = this.hasAcademicContext();

      if (requiresAcademicContext && !hasAcademicContext) {
        console.log(`ℹ️ Component ${name} requires academic context but none is available - skipping element checks`);
        // Component will render academic context error, so don't wait for normal elements
      } else if (config.requiredElements.length > 0) {
        try {
          console.log(`🔍 Waiting for required elements: ${config.requiredElements.join(', ')}`);
          await this.waitForElements(config.requiredElements, config.initTimeout);
          console.log(`✅ Required elements found for ${name}`);
        } catch (error) {
          console.warn(`⚠️ Some required elements not found for ${name}, but proceeding:`, error.message);
        }
      }

      // Cleanup existing instance if it exists
      await this.cleanupComponent(name);

      // Initialize the component
      const component = config.component;
      let initResult = true;

      if (component.init && typeof component.init === 'function') {
        // Add retry logic for initialization
        let attempts = 0;
        let lastError = null;

        while (attempts < config.retryAttempts) {
          try {
            await component.init();
            console.log(`✅ Component ${name} initialized successfully`);
            break;
          } catch (error) {
            attempts++;
            lastError = error;
            console.warn(`⚠️ Component ${name} initialization attempt ${attempts} failed:`, error.message);

            if (attempts < config.retryAttempts) {
              console.log(`🔄 Retrying ${name} initialization in ${config.retryDelay}ms...`);
              await new Promise(resolve => setTimeout(resolve, config.retryDelay));
            }
          }
        }

        if (attempts >= config.retryAttempts) {
          console.error(`❌ Component ${name} failed to initialize after ${config.retryAttempts} attempts:`, lastError);
          initResult = false;
        }
      }

      // Store active component
      if (initResult) {
        this.state.activeComponents.set(name, {
          config,
          pageId,
          initializedAt: Date.now()
        });
      }

      return initResult;

    } catch (error) {
      console.error(`❌ Failed to initialize component ${name}:`, error);
      return false;
    } finally {
      this.state.initializationQueue.delete(name);
    }
  },

  // Cleanup a component
  async cleanupComponent(name) {
    const activeComponent = this.state.activeComponents.get(name);
    if (!activeComponent) return;

    try {
      const component = activeComponent.config.component;
      if (component.cleanup && typeof component.cleanup === 'function') {
        console.log(`🧹 Cleaning up component: ${name}`);
        await component.cleanup();
      }
    } catch (error) {
      console.error(`❌ Error cleaning up component ${name}:`, error);
    }

    this.state.activeComponents.delete(name);
  },

  // Cleanup all components for a specific page
  async cleanupPageComponents(pageId) {
    const componentsToCleanup = [];

    for (const [name, activeComponent] of this.state.activeComponents.entries()) {
      if (activeComponent.pageId === pageId && activeComponent.config.cleanupOnNavigate) {
        componentsToCleanup.push(name);
      }
    }

    for (const name of componentsToCleanup) {
      await this.cleanupComponent(name);
    }
  },

  // Check if academic context is available
  hasAcademicContext() {
    return window.AcademicContext &&
           window.AcademicContext.getActiveAcademicYear() &&
           window.AcademicContext.getActiveTerm();
  },

  // Check if a component requires academic context
  componentRequiresAcademicContext(name) {
    const academicContextComponents = [
      'RegisterTeacherComponent',
      'ManageTeachersComponent',
      'RegisterOLevelStudentComponent',
      'RegisterALevelStudentComponent',
      'ManageStudentsComponent',
      'StudentEnrollmentComponent',
      'ManageStreamsComponent',
      'CAConfigurationComponent',
      'AssessmentManagementComponent',
      'ExamTypesManagementComponent',
      'GradeBoundariesComponent',
      'OLevelGradeBoundariesComponent',
      'ALevelGradeBoundariesComponent',
      'EnterCAScoresComponent',
      'EnterExamMarksComponent',
      'OLevelReportCardsComponent'
    ];

    return academicContextComponents.includes(name);
  },

  // Get component status
  getComponentStatus(name) {
    const isRegistered = this.state.registeredComponents.has(name);
    const isActive = this.state.activeComponents.has(name);
    const isInitializing = this.state.initializationQueue.has(name);

    return {
      isRegistered,
      isActive,
      isInitializing,
      config: this.state.registeredComponents.get(name),
      activeInfo: this.state.activeComponents.get(name)
    };
  },

  // Initialize multiple components in sequence
  async initializeComponents(componentNames, pageId = null) {
    const results = {};

    for (const name of componentNames) {
      results[name] = await this.initializeComponent(name, pageId);
    }

    return results;
  },

  // Cleanup all active components
  async cleanupAllComponents() {
    const activeComponentNames = Array.from(this.state.activeComponents.keys());
    
    for (const name of activeComponentNames) {
      await this.cleanupComponent(name);
    }
  },

  // Get system status
  getSystemStatus() {
    return {
      registeredComponents: Array.from(this.state.registeredComponents.keys()),
      activeComponents: Array.from(this.state.activeComponents.keys()),
      initializingComponents: Array.from(this.state.initializationQueue),
      isInitializing: this.state.isInitializing
    };
  },

  // Utility function for debouncing (to replace setTimeout patterns in components)
  debounce(func, delay = 300) {
    let timeoutId;
    return function (...args) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  },

  // Utility function for requestAnimationFrame with fallback
  nextFrame(callback) {
    if (typeof requestAnimationFrame !== 'undefined') {
      requestAnimationFrame(callback);
    } else {
      setTimeout(callback, 16); // ~60fps fallback
    }
  }
};

// Export to global scope
window.ComponentLifecycleManager = ComponentLifecycleManager;

// Auto-register common component configurations
document.addEventListener('DOMContentLoaded', () => {
  // Wait for components to be loaded
  setTimeout(() => {
    // Register grade boundaries components
    if (window.OLevelGradeBoundariesComponent) {
      ComponentLifecycleManager.registerComponent('OLevelGradeBoundariesComponent', window.OLevelGradeBoundariesComponent, {
        requiredElements: ['#grade-boundaries-table-body'],
        dependencies: []
      });
    }

    if (window.ALevelGradeBoundariesComponent) {
      ComponentLifecycleManager.registerComponent('ALevelGradeBoundariesComponent', window.ALevelGradeBoundariesComponent, {
        requiredElements: ['#grade-boundaries-table-body'],
        dependencies: []
      });
    }

    // Register other common components with their required elements
    const componentConfigs = [
      { name: 'Dashboard', requiredElements: [] }, // Dashboard component
      { name: 'AcademicYearSetupComponent', requiredElements: [] },
      { name: 'AcademicYearManagementComponent', requiredElements: ['#current-year-info', '#terms-management-content'] },
      { name: 'RegisterSystemUserComponent', requiredElements: ['#register-admin-form'] },
      { name: 'ManageSystemUserComponent', requiredElements: ['#admins-table-body'] },
      { name: 'ManageStreamsComponent', requiredElements: ['#search_streams', '#streams-table-body'] },
      { name: 'ManageTeachersComponent', requiredElements: ['#teachers-table-body'] },
      { name: 'StudentManagementComponents', requiredElements: [] }, // Selection interface - no specific elements required
      { name: 'ManageStudentsComponent', requiredElements: ['#students-table-body'] },
      { name: 'StudentEnrollmentComponent', requiredElements: ['#current-enrollments-container'] },
      { name: 'RegisterTeacherComponent', requiredElements: ['#register-teacher-form'] },
      { name: 'RegisterStudentComponent', requiredElements: [] },
      { name: 'RegisterOLevelStudentComponent', requiredElements: ['#register-o-level-student-form'] },
      { name: 'RegisterALevelStudentComponent', requiredElements: ['#register-a-level-student-form'] },
      { name: 'CAConfigurationComponent', requiredElements: ['#configurations-table-body'] },
      { name: 'ExamTypesManagementComponent', requiredElements: ['#class-exam-types-table-body'] },
      { name: 'EnterCAScoresComponent', requiredElements: ['#ca_academic_year'] },
      { name: 'EnterExamMarksComponent', requiredElements: ['#exam_academic_year'] },
      { name: 'SchoolSettingsManagementComponent', requiredElements: [] },
      { name: 'ImportExportDataManagementComponent', requiredElements: [] },
      { name: 'OLevelReportCardsComponent', requiredElements: ['#report-filters-form'] }
    ];

    // Super user only components
    const superUserComponents = [
      'RegisterSystemUserComponent',
      'ManageSystemUserComponent',
      'SchoolSettingsManagementComponent',
      'ImportExportDataManagementComponent'
    ];

    componentConfigs.forEach(config => {
      if (window[config.name]) {
        // Check if this is a super user component
        if (superUserComponents.includes(config.name)) {
          // Only register super user components if user is super user
          const currentUser = window.SR && window.SR.currentUser;
          const isSuperUser = currentUser && currentUser.role === 'super_user';

          if (isSuperUser) {
            ComponentLifecycleManager.registerComponent(config.name, window[config.name], {
              requiredElements: config.requiredElements,
              dependencies: []
            });
            console.log(`🔐 Super user component registered: ${config.name}`);
          } else {
            console.log(`ℹ️ Super user component not registered (user not super user): ${config.name}`);
          }
        } else {
          // Register regular components for all users
          ComponentLifecycleManager.registerComponent(config.name, window[config.name], {
            requiredElements: config.requiredElements,
            dependencies: []
          });
        }
      }
    });

    console.log('✅ ComponentLifecycleManager initialized with', ComponentLifecycleManager.state.registeredComponents.size, 'components');
  }, 100);

  // Method to refresh super user component registrations after login
  ComponentLifecycleManager.refreshSuperUserComponents = function() {
    console.log('🔄 Refreshing super user component registrations...');

    const superUserComponents = [
      { name: 'RegisterSystemUserComponent', requiredElements: ['#register-admin-form'] },
      { name: 'ManageSystemUserComponent', requiredElements: ['#admins-table-body'] },
      { name: 'SchoolSettingsManagementComponent', requiredElements: [] },
      { name: 'ImportExportDataManagementComponent', requiredElements: [] }
    ];

    const currentUser = window.SR && window.SR.currentUser;
    const isSuperUser = currentUser && currentUser.role === 'super_user';

    if (isSuperUser) {
      console.log('🔐 User is super user, registering super user components...');
      superUserComponents.forEach(config => {
        if (window[config.name]) {
          // Check if already registered
          const status = this.getComponentStatus(config.name);
          if (!status.isRegistered) {
            this.registerComponent(config.name, window[config.name], {
              requiredElements: config.requiredElements,
              dependencies: []
            });
            console.log(`✅ Super user component registered: ${config.name}`);
          } else {
            console.log(`ℹ️ Super user component already registered: ${config.name}`);
          }
        } else {
          console.warn(`⚠️ Super user component not found: ${config.name}`);
        }
      });
    } else {
      console.log('ℹ️ User is not super user, super user components not registered');
    }
  };
});
