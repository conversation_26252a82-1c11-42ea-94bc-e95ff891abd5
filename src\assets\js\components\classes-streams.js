// SmartReport - Classes & Streams Management Components
// Comprehensive class and stream management system

// Uses global API services: window.ClassesAPI, window.StreamsAPI, window.LevelsAPI, window.AcademicYearsAPI
// Uses global config: window.SRConfig
// Uses environment configuration: ../config/environment.js

const ClassesStreamsComponents = {
  // Component state
  state: {
    classes: [],
    streams: [],
    levels: [],
    academicYears: [],
    loading: false
  },

  // Initialize component
  async init() {
    await this.loadInitialData();
  },

  // Load initial data
  async loadInitialData() {
    try {
      this.state.loading = true;

      const [classes, streams, levels, academicYears] = await Promise.all([
        window.ClassesAPI.getAll(),
        window.StreamsAPI.getAll(),
        window.LevelsAPI.getAll(),
        window.AcademicYearsAPI.getAll()
      ]);

      this.state.classes = classes;
      this.state.streams = streams;
      this.state.levels = levels;
      this.state.academicYears = academicYears;

    } catch (error) {
      console.error('❌ Failed to load initial data:', error);
      if (window.SRConfig && window.SRConfig.get('development.debugMode')) {
        console.error('Debug: Classes and streams loading error details:', error);
      }


    } finally {
      this.state.loading = false;
    }
  }
};

// Manage Streams Component
const ManageStreamsComponent = {
  // Store bound event handlers to prevent duplicates
  _boundHandlers: {},

  // Render manage streams interface
  render() {
    // Check if academic context is available
    const hasAcademicContext = window.AcademicContext &&
                              window.AcademicContext.getActiveAcademicYear() &&
                              window.AcademicContext.getActiveTerm();

    if (!hasAcademicContext) {
      return this.renderAcademicContextError();
    }

    // Show loading state if data is still loading
    if (ClassesStreamsComponents.state.loading) {
      // Use centralized loading state from page router
      const container = document.getElementById('content-area');
      if (container && window.PageRouter) {
        window.PageRouter.showLoadingState(container, 'Loading classes and streams data...');
      }
      return '';
    }

    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Class Streams Management',
          'Create and manage class streams for organizing students into different divisions',
        )}

        <!-- Stream Management Actions -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
          <div class="flex items-center justify-end mb-6">
            ${SRDesignSystem.forms.button('add-stream', 'Add Streams', 'primary', {
              icon: 'fas fa-plus',
              onclick: 'ManageStreamsComponent.showAddStreamModal()'
            })}
          </div>

          <!-- Filters and Search -->
          <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap} mb-6">
            ${SRDesignSystem.forms.input('search_streams', 'Search', '', {
              placeholder: 'Search by stream name...',
              icon: 'fas fa-search'
            })}
            ${SRDesignSystem.forms.select('education_level_filter', 'Education Level', [
              { value: '', label: 'All Education Levels' },
              { value: 'o_level', label: 'O-Level' },
              { value: 'a_level', label: 'A-Level' }
            ], '')}
          </div>
        </div>

        <!-- Streams Overview -->

        <!-- Streams Table -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-4 border-b border-primary-200">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-primary-900 flex items-center">
              ${SRDesignSystem.components.icon('fas fa-stream', 'base', 'primary-600')}
              <span class="ml-3">Class Streams</span>
            </h3>
          </div>
          <div class="overflow-x-auto max-h-96 overflow-y-auto">
            <table class="${SRDesignSystem.responsive.table.base}">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Stream Name</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Education Level</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Applies To Classes</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-right text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Actions</th>
                </tr>
              </thead>
              <tbody id="streams-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Streams will be populated here -->
              </tbody>
            </table>
          </div>
        </div>

      </div>

      <!-- Add Stream Modal -->
      <div id="add-stream-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-3/4 max-w-2xl shadow-lg rounded-xl bg-white">
          <div class="mt-3">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">Add New Streams</h3>
              <button onclick="ManageStreamsComponent.closeAddStreamModal()" class="text-gray-400 hover:text-gray-600 p-2">
                ${SRDesignSystem.components.icon('fas fa-times', 'lg', 'current')}
              </button>
            </div>
            <form id="add-stream-form" class="mt-6 space-y-6">
              <div class="space-y-4">
                <!-- Stream Creation Mode Selection -->
                <div>
                  <label class="block ${SRDesignSystem.responsive.text.sm} font-medium text-gray-700 mb-3">
                    Stream Creation Mode <span class="text-red-500">*</span>
                  </label>
                  <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                    <label class="flex items-center space-x-2 cursor-pointer p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                      <input type="radio" name="creation_mode" value="new" checked
                             class="text-primary-600 focus:ring-primary-500">
                      <span class="${SRDesignSystem.responsive.text.sm} text-gray-700">Create new streams</span>
                    </label>
                    <label class="flex items-center space-x-2 cursor-pointer p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                      <input type="radio" name="creation_mode" value="copy"
                             class="text-primary-600 focus:ring-primary-500">
                      <span class="${SRDesignSystem.responsive.text.sm} text-gray-700">Copy existing streams</span>
                    </label>
                  </div>
                </div>

                <!-- New Streams Section -->
                <div id="new-streams-section">
                  <label class="block ${SRDesignSystem.responsive.text.sm} font-medium text-gray-700 mb-2">
                    Stream Names <span class="text-red-500">*</span>
                  </label>
                  <div class="space-y-2" id="stream-names-container">
                    <!-- Stream inputs will be populated dynamically -->
                  </div>
                  <div id="add-stream-button-container">
                    <!-- Add Another Stream button will be added dynamically -->
                  </div>
                </div>

                <!-- Copy Existing Streams Section -->
                <div id="copy-streams-section" class="hidden">
                  <label class="block ${SRDesignSystem.responsive.text.sm} font-medium text-gray-700 mb-2">
                    Select Source Class <span class="text-red-500">*</span>
                  </label>
                  <select id="source-class-select"
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    <option value="">Choose a class with existing streams...</option>
                  </select>
                  <div id="source-streams-preview" class="mt-3 hidden">
                    <p class="${SRDesignSystem.responsive.text.sm} text-gray-600 mb-2">Streams that will be copied:</p>
                    <div id="source-streams-list" class="flex flex-wrap gap-2"></div>
                  </div>
                </div>

                <div>
                  <label class="block ${SRDesignSystem.responsive.text.sm} font-medium text-gray-700 mb-2">
                    Apply to Class Levels <span class="text-red-500">*</span>
                  </label>
                  <div id="class-levels-selection" class="space-y-2 max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-3">
                    <!-- Class levels will be populated here -->
                  </div>
                  <p class="mt-1 ${SRDesignSystem.responsive.text.sm} text-gray-500">Select which O-Level classes these streams apply to</p>
                </div>

                <!-- Hidden field for stream type (always o_level for this component) -->
                <input type="hidden" name="stream_type" value="o_level">
              </div>

              <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                ${SRDesignSystem.forms.button('cancel-stream', 'Cancel', 'secondary', {
                  type: 'button',
                  onclick: 'ManageStreamsComponent.closeAddStreamModal()'
                })}
                ${SRDesignSystem.forms.button('save-stream', 'Create Streams', 'primary', {
                  type: 'submit'
                })}
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Edit Stream Modal -->
      <div id="edit-stream-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-medium text-gray-900 mb-4">Edit Stream</h3>
            <form id="edit-stream-form">
              <input type="hidden" id="edit-stream-id" name="stream_id">

              <div class="mb-4">
                ${SRDesignSystem.forms.input('edit_stream_name', 'Stream Name', '', {
                  type: 'text',
                  required: true,
                  placeholder: 'Enter stream name (e.g., A, B, C)'
                })}
              </div>

              <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                ${SRDesignSystem.forms.button('cancel-edit-stream', 'Cancel', 'secondary', {
                  type: 'button',
                  onclick: 'ManageStreamsComponent.closeEditStreamModal()'
                })}
                ${SRDesignSystem.forms.button('update-stream', 'Update', 'primary', {
                  type: 'submit'
                })}
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Delete Stream Modal -->
      <div id="delete-stream-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-3/4 max-w-2xl shadow-lg rounded-xl bg-white">
          <div class="mt-3">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">Delete Stream</h3>
              <button onclick="ManageStreamsComponent.closeDeleteStreamModal()" class="text-gray-400 hover:text-gray-600 p-2">
                ${SRDesignSystem.components.icon('fas fa-times', 'lg', 'current')}
              </button>
            </div>

            <div class="mt-6">
              <div class="mb-4">
                <p class="${SRDesignSystem.responsive.text.sm} text-gray-600 mb-2">Stream to delete:</p>
                <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                  <span id="delete-stream-name" class="font-medium text-red-800"></span>
                </div>
              </div>

              <div class="mb-6">
                <p class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-700 mb-3">Choose deletion option:</p>
                <div class="space-y-3">
                  <label class="flex items-start space-x-3 cursor-pointer p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <input type="radio" name="delete_option" value="remove_classes" checked
                           class="mt-1 text-red-600 focus:ring-red-500">
                    <div>
                      <span class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-900">Remove from specific classes</span>
                      <p class="${SRDesignSystem.responsive.text.xs} text-gray-500 mt-1">Keep the stream but remove it from selected classes only</p>
                    </div>
                  </label>
                  <label class="flex items-start space-x-3 cursor-pointer p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <input type="radio" name="delete_option" value="delete_completely"
                           class="mt-1 text-red-600 focus:ring-red-500">
                    <div>
                      <span class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-900">Delete completely</span>
                      <p class="${SRDesignSystem.responsive.text.xs} text-gray-500 mt-1">Permanently delete this stream from all classes</p>
                    </div>
                  </label>
                </div>
              </div>

              <div id="class-selection-section">
                <p class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-700 mb-3">Select classes to remove stream from:</p>
                <div id="delete-classes-selection" class="flex flex-wrap gap-3 max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-3">
                  <!-- Classes will be populated here -->
                </div>
              </div>

              <div class="flex justify-end space-x-3 pt-6 border-t">
                ${SRDesignSystem.forms.button('cancel-delete-stream', 'Cancel', 'secondary', {
                  type: 'button',
                  onclick: 'ManageStreamsComponent.closeDeleteStreamModal()'
                })}
                ${SRDesignSystem.forms.button('confirm-delete-stream', 'Delete Stream', 'danger', {
                  type: 'button',
                  icon: 'fas fa-trash',
                  onclick: 'ManageStreamsComponent.confirmDeleteStream()'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Render academic context error
  renderAcademicContextError() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Class Streams Management',
          'Create and manage class streams'
        )}

        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-600')}
            </div>
            <div class="ml-4">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-red-900">Academic Context Required</h3>
              <p class="text-red-700 mt-2">
                You cannot manage class streams without setting up the academic year and terms first.
              </p>
              <p class="text-red-700 mt-1">
                Please go to <strong>Academic Year → Setup</strong> to configure your academic year and terms before managing streams.
              </p>
              <div class="mt-4 text-center">
                ${SRDesignSystem.forms.button('setup-academic-streams', 'Setup Academic Year & Terms', 'danger', {
                  icon: 'fas fa-calendar-plus',
                  onclick: 'ManageStreamsComponent.navigateToAcademicSetup()'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize manage streams component
  async init() {
    console.log('🔧 Initializing Manage Streams Component...');

    try {
      // Clean up any previous state first
      this.resetComponentState();

      // Load initial data
      await ClassesStreamsComponents.loadInitialData();

      // Initialize directly - DOM should be ready due to lifecycle manager
      this.populateClassesOverview();
      this.populateStreamsTable();
      this.initializeEventListeners();

      // Ensure forms are properly reset and ready for input
      setTimeout(() => {
        this.resetAllForms();
      }, 100);

      console.log('✅ Manage Streams Component initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Manage Streams Component:', error);
      throw error;
    }
  },

  // Reset all forms to ensure clean state
  resetAllForms() {
    // Reset add stream form
    const addForm = document.getElementById('add-stream-form');
    if (addForm) {
      addForm.reset();

      // Reset creation mode to 'new'
      const newModeRadio = document.querySelector('input[name="creation_mode"][value="new"]');
      if (newModeRadio) {
        newModeRadio.checked = true;
      }

      // Show new streams section, hide copy section
      const newStreamsSection = document.getElementById('new-streams-section');
      const copyStreamsSection = document.getElementById('copy-streams-section');
      if (newStreamsSection) newStreamsSection.classList.remove('hidden');
      if (copyStreamsSection) copyStreamsSection.classList.add('hidden');

      // Reset stream names container
      this.resetStreamNamesContainer();

      // Set correct required state based on mode
      setTimeout(() => {
        this.handleCreationModeChange();
      }, 50);
    }

    // Reset edit stream form
    const editForm = document.getElementById('edit-stream-form');
    if (editForm) {
      editForm.reset();
    }

    // Reset modal titles
    const addModalTitle = document.querySelector('#add-stream-modal h3');
    if (addModalTitle) {
      addModalTitle.textContent = 'Add New Streams';
    }

    // Ensure input fields are enabled and ready
    const allInputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="number"], textarea, select');
    allInputs.forEach(input => {
      input.disabled = false;
      input.readOnly = false;
    });
  },

  // Reset component state
  resetComponentState() {
    // Reset component-specific state
    this.currentStreamToDelete = null;

    // Clear any cached data
    if (ClassesStreamsComponents && ClassesStreamsComponents.state) {
      ClassesStreamsComponents.state = {
        classes: null,
        streams: null
      };
    }
  },

  // Cleanup component - called when navigating away
  cleanup() {
    console.log('🧹 Cleaning up Manage Streams Component...');

    // Reset component state
    this.resetComponentState();

    // Hide and clean up all modals
    const modals = ['add-stream-modal', 'edit-stream-modal', 'delete-stream-modal'];
    modals.forEach(modalId => {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.classList.add('hidden');

        // Reset modal forms
        const form = modal.querySelector('form');
        if (form) {
          form.reset();
        }
      }
    });

    // Remove event listeners to prevent memory leaks
    this.removeEventListeners();

    // Clear any intervals or timeouts if they exist
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }

    console.log('✅ Manage Streams Component cleanup completed');
  },

  // Remove event listeners
  removeEventListeners() {
    // Remove class selection listeners
    const classCheckboxes = document.querySelectorAll('input[name="class_level_ids"]');
    classCheckboxes.forEach(checkbox => {
      // Clone and replace to remove all event listeners
      const newCheckbox = checkbox.cloneNode(true);
      checkbox.parentNode.replaceChild(newCheckbox, checkbox);
    });

    // Remove creation mode listeners
    const modeRadios = document.querySelectorAll('input[name="creation_mode"]');
    modeRadios.forEach(radio => {
      const newRadio = radio.cloneNode(true);
      radio.parentNode.replaceChild(newRadio, radio);
    });

    // Remove source class selection listener
    const sourceSelect = document.getElementById('source-class-select');
    if (sourceSelect) {
      const newSelect = sourceSelect.cloneNode(true);
      sourceSelect.parentNode.replaceChild(newSelect, sourceSelect);
    }

    // Remove delete option listeners
    const deleteOptions = document.querySelectorAll('input[name="delete_option"]');
    deleteOptions.forEach(option => {
      const newOption = option.cloneNode(true);
      option.parentNode.replaceChild(newOption, option);
    });
  },

  // Populate class levels selection for O-Level classes
  async populateClassLevelsSelection() {
    const container = document.getElementById('class-levels-selection');
    if (!container) return;

    try {
      // Get O-Level class levels from the existing API
      const response = await window.API.get('/academic/class-levels');

      if (response.success && response.data) {
        // Filter for O-Level classes (education_level_id = 1 for O-Level)
        const oLevelClasses = response.data.filter(level =>
          level.education_level_code === 'o_level' ||
          level.code.startsWith('s') && ['s1', 's2', 's3', 's4'].includes(level.code)
        );

        if (oLevelClasses.length > 0) {
          // Create 2x2 grid layout for class levels
          const classItems = oLevelClasses.map(level => `
            <label class="flex items-center space-x-2 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
              <input type="checkbox"
                     name="class_level_ids"
                     value="${level.id}"
                     class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
              <span class="text-sm text-gray-700">
                <span class="font-medium">${level.name}</span>
                <span class="text-gray-500 block text-xs">${level.display_name}</span>
              </span>
            </label>
          `);

          // Arrange in 2x2 grid
          container.innerHTML = `<div class="grid grid-cols-2 gap-3">${classItems.join('')}</div>`;
        } else {
          container.innerHTML = `
            <p class="text-sm text-gray-500 text-center py-4">
              <i class="fas fa-info-circle mr-2"></i>
              No O-Level class levels found
            </p>
          `;
        }
      } else {
        container.innerHTML = `
          <p class="text-sm text-gray-500 text-center py-4">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            Unable to load class levels
          </p>
        `;
      }
    } catch (error) {
      console.error('Error loading class levels:', error);
      container.innerHTML = `
        <p class="text-sm text-red-500 text-center py-4">
          <i class="fas fa-exclamation-triangle mr-2"></i>
          Error loading class levels
        </p>
      `;
    }
  },



  // Populate dropdown fields
  populateDropdowns() {
    // Populate academic years
    const academicYearSelect = document.getElementById('academic_year_filter');
    if (academicYearSelect) {
      academicYearSelect.innerHTML = '<option value="">All Academic Years</option>';
      ClassesStreamsComponents.state.academicYears.data?.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.name;
        if (year.is_active) option.selected = true;
        academicYearSelect.appendChild(option);
      });
    }

    // Populate education levels
    const levelSelect = document.getElementById('level_filter');
    if (levelSelect) {
      levelSelect.innerHTML = '<option value="">All Education Levels</option>';
      ClassesStreamsComponents.state.levels.data?.forEach(level => {
        const option = document.createElement('option');
        option.value = level.code; // Use code instead of id
        option.textContent = level.display_name;
        levelSelect.appendChild(option);
      });
    }

    // Populate applicable levels in modal
    const applicableLevelsSelect = document.getElementById('applicable_levels');
    if (applicableLevelsSelect) {
      applicableLevelsSelect.innerHTML = '';
      ClassesStreamsComponents.state.levels.data?.forEach(level => {
        const option = document.createElement('option');
        option.value = level.id;
        option.textContent = level.display_name;
        applicableLevelsSelect.appendChild(option);
      });
    }
  },

  // Populate classes overview
  populateClassesOverview() {
    this.populateOLevelClasses();
    this.populateALevelClasses();
  },

  // Populate O-Level classes
  populateOLevelClasses() {
    const container = document.getElementById('o-level-classes-container');
    if (!container) return;

    const oLevelClasses = ClassesStreamsComponents.state.classes.data?.filter(cls =>
      cls.education_level_code === 'o_level'
    ) || [];

    if (oLevelClasses.length === 0) {
      container.innerHTML = '<p class="text-gray-500 text-center py-4">No O-Level classes found</p>';
      return;
    }

    container.innerHTML = `
      <div class="space-y-4">
        ${oLevelClasses.map(cls => `
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-900">${cls.name}</h4>
                <p class="text-sm text-gray-500">
                  ${cls.stream_name || 'No stream assigned'} •
                  ${cls.actual_enrollment || 0} students
                </p>
              </div>
              <div class="flex items-center space-x-2">
                <button onclick="ManageStreamsComponent.assignStream(${cls.id})" 
                        class="text-blue-600 hover:text-blue-900 text-sm">
                  ${cls.stream_name ? 'Change Stream' : 'Assign Stream'}
                </button>
              </div>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  },

  // Populate A-Level classes
  populateALevelClasses() {
    const container = document.getElementById('a-level-classes-container');
    if (!container) return;

    const aLevelClasses = ClassesStreamsComponents.state.classes.data?.filter(cls =>
      cls.education_level_code === 'a_level'
    ) || [];

    if (aLevelClasses.length === 0) {
      container.innerHTML = '<p class="text-gray-500 text-center py-4">No A-Level classes found</p>';
      return;
    }

    container.innerHTML = `
      <div class="space-y-4">
        ${aLevelClasses.map(cls => `
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-900">${cls.name}</h4>
                <p class="text-sm text-gray-500">
                  ${cls.stream_name || 'No stream assigned'} •
                  ${cls.actual_enrollment || 0} students
                </p>
              </div>
              <div class="flex items-center space-x-2">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  cls.stream_name ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }">
                  ${cls.stream_name ? 'Stream Assigned' : 'No Stream'}
                </span>
                <button onclick="ManageStreamsComponent.assignStream(${cls.id})" 
                        class="text-blue-600 hover:text-blue-900 text-sm">
                  ${cls.stream_name ? 'Change Stream' : 'Assign Stream'}
                </button>
              </div>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  },

  // Populate streams table
  populateStreamsTable() {
    const tbody = document.getElementById('streams-table-body');
    if (!tbody) return;

    // The API returns {success: true, data: [...]} so we need to access the data property
    const streamsResponse = ClassesStreamsComponents.state.streams || {};
    const streams = streamsResponse.data || [];



    // Apply search filter
    const searchTerm = document.getElementById('search_streams')?.value?.toLowerCase() || '';
    const educationLevelFilter = document.getElementById('education_level_filter')?.value || '';

    const filteredStreams = streams.filter(stream => {
      const matchesSearch = !searchTerm || stream.name.toLowerCase().includes(searchTerm);
      const matchesType = !educationLevelFilter || stream.stream_type === educationLevelFilter;
      return matchesSearch && matchesType;
    });

    if (filteredStreams.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="4" class="px-6 py-12 text-center text-gray-500">
            <div class="text-gray-400 mb-4">
              ${SRDesignSystem.components.icon('fas fa-users', '4xl', 'gray-300')}
            </div>
            <p class="${SRDesignSystem.responsive.text.lg} font-medium">No streams found</p>
            <p class="${SRDesignSystem.responsive.text.sm}">Create new streams to organize your classes.</p>
          </td>
        </tr>
      `;
      return;
    }

    tbody.innerHTML = filteredStreams.map(stream => {
      // Define dropdown actions for O-Level streams
      const actions = stream.stream_type === 'o_level' ? [
        {
          label: 'Edit',
          icon: 'fas fa-edit',
          onclick: `ManageStreamsComponent.editStream(${stream.id})`,
          color: 'text-indigo-600'
        },
        {
          label: 'Delete',
          icon: 'fas fa-trash',
          onclick: `ManageStreamsComponent.deleteStream(${stream.id})`,
          color: 'text-red-600'
        }
      ] : [];

      return `
        <tr class="hover:bg-gray-50">
          <td class="${SRDesignSystem.responsive.table.cell}">
            <div class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-900">${stream.name}</div>
          </td>
          <td class="${SRDesignSystem.responsive.table.cell}">
            ${SRDesignSystem.components.badge(
              stream.stream_type === 'o_level' ? 'O-Level' : 'A-Level',
              stream.stream_type === 'o_level' ? 'primary' : 'success'
            )}
          </td>
          <td class="${SRDesignSystem.responsive.table.cell}">
            <div class="${SRDesignSystem.responsive.text.sm} text-gray-900">
              ${stream.class_names ? `
                <div class="flex flex-wrap gap-1">
                  ${stream.class_names.split(', ').map(className => `
                    ${SRDesignSystem.components.badge(className, 'info')}
                  `).join('')}
                </div>
              ` : `
                <span class="text-gray-400 ${SRDesignSystem.responsive.text.xs}">
                  ${stream.stream_type === 'a_level' ? 'All A-Level Classes' : 'No classes assigned'}
                </span>
              `}
            </div>
          </td>
          <td class="${SRDesignSystem.responsive.table.cell} text-right">
            ${stream.stream_type === 'o_level' ?
              SRDesignSystem.tables.generateKebabMenu(`stream-${stream.id}`, actions, stream) : `
              <div class="flex items-center justify-end text-gray-400">
                ${SRDesignSystem.components.icon('fas fa-info-circle', 'sm', 'current')}
                <span class="ml-1 ${SRDesignSystem.responsive.text.xs}">Pre-configured</span>
              </div>
            `}
          </td>
        </tr>
      `;
    }).join('');
  },

  // Get stream type badge CSS class
  getStreamTypeBadgeClass(streamType) {
    const classes = {
      'o_level': 'bg-blue-100 text-blue-800',
      'a_level': 'bg-green-100 text-green-800'
    };
    return classes[streamType] || 'bg-gray-100 text-gray-800';
  },



  // Initialize event listeners
  initializeEventListeners() {
    console.log('🔗 Setting up event listeners...');

    // Search input with debounce for better performance
    const searchInput = document.getElementById('search_streams');
    if (searchInput) {
      const debouncedSearch = window.ComponentLifecycleManager?.debounce(() => {
        this.populateStreamsTable();
      }, 300) || (() => this.populateStreamsTable());

      searchInput.addEventListener('input', debouncedSearch);
    }

    const educationLevelFilter = document.getElementById('education_level_filter');
    if (educationLevelFilter) {
      educationLevelFilter.addEventListener('change', () => {
        this.populateStreamsTable();
      });
    }

    // Initialize form event listeners immediately
    this.initializeFormEventListeners();
    this.initializeEditFormEventListeners();

    console.log('✅ Event listeners initialized');
  },

  // Initialize form event listeners (called when modal opens)
  initializeFormEventListeners() {
    // Add stream form submission
    const addStreamForm = document.getElementById('add-stream-form');
    if (addStreamForm) {
      // Create bound handler if it doesn't exist
      if (!this._boundHandlers.handleAddStream) {
        this._boundHandlers.handleAddStream = this.handleAddStream.bind(this);
      }

      // Remove any existing event listeners to prevent duplicates
      if (this._boundHandlers.handleAddStream) {
        addStreamForm.removeEventListener('submit', this._boundHandlers.handleAddStream);
      }

      // Add the event listener
      addStreamForm.addEventListener('submit', this._boundHandlers.handleAddStream);
    }
  },

  // Initialize edit form event listeners (called when edit modal opens)
  initializeEditFormEventListeners() {
    // Edit stream form submission
    const editStreamForm = document.getElementById('edit-stream-form');
    if (editStreamForm) {
      // Create bound handler if it doesn't exist
      if (!this._boundHandlers.handleEditStream) {
        this._boundHandlers.handleEditStream = this.handleEditStream.bind(this);
      }

      // Remove any existing event listeners to prevent duplicates
      if (this._boundHandlers.handleEditStream) {
        editStreamForm.removeEventListener('submit', this._boundHandlers.handleEditStream);
      }

      // Add the event listener
      editStreamForm.addEventListener('submit', this._boundHandlers.handleEditStream);
    }
  },

  // Show add stream modal
  async showAddStreamModal() {
    const modal = document.getElementById('add-stream-modal');
    if (modal) {
      modal.classList.remove('hidden');

      // Ensure default stream inputs are present (old logic)
      this.resetStreamNamesContainer();

      // Populate class levels when modal opens
      await this.populateClassLevelsSelection();
      // Populate source classes for copying
      await this.populateSourceClassesSelection();
      // Initialize form event listeners directly
      this.initializeFormEventListeners();
      // Set up class selection change listener to populate existing streams (after class levels are populated)
      this.setupClassSelectionListener();
      // Set up creation mode listeners
      this.setupCreationModeListeners();

      // Set initial required state based on current mode
      this.handleCreationModeChange();

      // Enable create button by default (will be disabled if needed)
      this.enableCreateButton();
    }
  },

  // Setup class selection listener to populate existing streams
  setupClassSelectionListener() {
    // Use setTimeout to ensure DOM is ready
    setTimeout(() => {
      const classCheckboxes = document.querySelectorAll('input[name="class_level_ids"]');


      classCheckboxes.forEach(checkbox => {
        // Remove existing listeners to prevent duplicates
        checkbox.removeEventListener('change', this.handleClassSelectionChange);
        // Add new listener
        checkbox.addEventListener('change', () => {
          this.handleClassSelectionChange();

          // Also update stream names container for new mode
          const selectedMode = document.querySelector('input[name="creation_mode"]:checked')?.value;
          if (selectedMode === 'new') {
            this.populateStreamNamesWithExisting();
          }
        });
      });
    }, 100);
  },

  // Handle class selection change
  async handleClassSelectionChange() {
    try {
      // Check if user has already entered stream names
      const hasUserInput = this.hasUserEnteredStreamNames();

      // Only update UI if user hasn't entered names - preserve user input
      if (!hasUserInput) {
        // Check if selected classes have existing streams to show readonly view
        const selectedClassLevels = Array.from(document.querySelectorAll('input[name="class_level_ids"]:checked'))
          .map(checkbox => parseInt(checkbox.value));

        if (selectedClassLevels.length > 0) {
          // Update the display based on whether classes have existing streams
          this.populateStreamNamesWithExisting();
        } else {
          // No classes selected, show default inputs
          this.resetStreamNamesContainer();
        }

        // Reset modal title
        const modalTitle = document.querySelector('#add-stream-modal h3');
        if (modalTitle) {
          modalTitle.textContent = 'Add New Streams';
        }
      } else {
        // User has input - show all hidden fields since class selection changed
        this.showAllStreamInputs();

        // Re-check all inputs for the new class selection
        const streamInputs = document.querySelectorAll('input[name="stream_names[]"]');
        streamInputs.forEach((input, index) => {
          if (input.value.trim()) {
            this.handleStreamNameInput(index);
          }
        });
      }
      // If user has input, don't change anything - preserve their work
    } catch (error) {
      console.error('Error handling class selection change:', error);
      // Only reset if user hasn't entered stream names
      if (!this.hasUserEnteredStreamNames()) {
        this.resetStreamNamesContainer();
        const modalTitle = document.querySelector('#add-stream-modal h3');
        if (modalTitle) {
          modalTitle.textContent = 'Add New Streams';
        }
      }
    }
  },

  // Check if user has entered stream names manually
  hasUserEnteredStreamNames() {
    const streamInputs = document.querySelectorAll('input[name="stream_names[]"]');

    // Check if any input has a non-empty value
    for (const input of streamInputs) {
      if (input.value.trim().length > 0) {
        return true;
      }
    }

    return false;
  },



  // Setup creation mode listeners
  setupCreationModeListeners() {
    const modeRadios = document.querySelectorAll('input[name="creation_mode"]');
    modeRadios.forEach(radio => {
      radio.addEventListener('change', () => {
        this.handleCreationModeChange();
      });
    });

    // Setup source class selection listener
    const sourceClassSelect = document.getElementById('source-class-select');
    if (sourceClassSelect) {
      sourceClassSelect.addEventListener('change', () => {
        this.handleSourceClassChange();
      });
    }
  },

  // Handle creation mode change
  handleCreationModeChange() {
    const selectedMode = document.querySelector('input[name="creation_mode"]:checked')?.value;
    const newStreamsSection = document.getElementById('new-streams-section');
    const copyStreamsSection = document.getElementById('copy-streams-section');

    if (selectedMode === 'new') {
      newStreamsSection?.classList.remove('hidden');
      copyStreamsSection?.classList.add('hidden');

      // Enable required validation for stream name inputs
      this.setStreamNameInputsRequired(true);

      // Refresh stream names container to show existing streams as readonly
      this.populateStreamNamesWithExisting();
    } else if (selectedMode === 'copy') {
      newStreamsSection?.classList.add('hidden');
      copyStreamsSection?.classList.remove('hidden');

      // Disable required validation for stream name inputs (they're hidden)
      this.setStreamNameInputsRequired(false);

      // Hide the Add Another Stream button in copy mode
      this.addStreamButton();
    }
  },

  // Set required attribute on stream name inputs
  setStreamNameInputsRequired(required) {
    const streamNameInputs = document.querySelectorAll('input[name="stream_names[]"]');
    streamNameInputs.forEach(input => {
      if (required) {
        input.setAttribute('required', '');
        input.setAttribute('aria-required', 'true');
      } else {
        input.removeAttribute('required');
        input.setAttribute('aria-required', 'false');
      }
    });
  },

  // Populate source classes dropdown
  async populateSourceClassesSelection() {
    const sourceSelect = document.getElementById('source-class-select');
    if (!sourceSelect) return;

    try {
      // Get all streams data
      const streamsResponse = ClassesStreamsComponents.state.streams || {};
      const allStreams = streamsResponse.data || [];

      // Group streams by class
      const classesWithStreams = {};
      allStreams.forEach(stream => {
        if (stream.stream_type === 'o_level' && stream.class_names && stream.class_level_ids) {
          const classIds = stream.class_level_ids.split(',');
          const classNames = stream.class_names.split(', ');

          classIds.forEach((classId, index) => {
            const className = classNames[index];
            if (!classesWithStreams[classId]) {
              classesWithStreams[classId] = {
                id: classId,
                name: className,
                streams: []
              };
            }
            classesWithStreams[classId].streams.push(stream);
          });
        }
      });

      // Populate dropdown - only include classes with 2+ streams
      const options = Object.values(classesWithStreams)
        .filter(classInfo => classInfo.streams.length >= 2)
        .map(classInfo =>
          `<option value="${classInfo.id}">${classInfo.name} (${classInfo.streams.length} streams)</option>`
        ).join('');

      if (options.length > 0) {
        sourceSelect.innerHTML = `
          <option value="">Choose a class with existing streams...</option>
          ${options}
        `;
      } else {
        sourceSelect.innerHTML = `
          <option value="">No classes with 2+ streams available</option>
        `;
      }

    } catch (error) {
      console.error('Error populating source classes:', error);
    }
  },

  // Handle source class selection change
  handleSourceClassChange() {
    const sourceSelect = document.getElementById('source-class-select');
    const previewDiv = document.getElementById('source-streams-preview');
    const streamsList = document.getElementById('source-streams-list');

    if (!sourceSelect || !previewDiv || !streamsList) return;

    const selectedClassId = sourceSelect.value;

    if (!selectedClassId) {
      previewDiv.classList.add('hidden');
      return;
    }

    // Get streams for selected class
    const streamsResponse = ClassesStreamsComponents.state.streams || {};
    const allStreams = streamsResponse.data || [];

    const classStreams = allStreams.filter(stream => {
      if (stream.stream_type !== 'o_level' || !stream.class_level_ids) return false;
      const classIds = stream.class_level_ids.split(',');
      return classIds.includes(selectedClassId);
    });

    if (classStreams.length > 0) {
      // Show preview
      streamsList.innerHTML = classStreams.map(stream =>
        `<span class="inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">${stream.name}</span>`
      ).join('');
      previewDiv.classList.remove('hidden');
    } else {
      previewDiv.classList.add('hidden');
    }
  },

  // Close add stream modal
  closeAddStreamModal() {
    const modal = document.getElementById('add-stream-modal');
    if (modal) modal.classList.add('hidden');

    // Reset form
    const form = document.getElementById('add-stream-form');
    if (form) {
      form.reset();
      // Uncheck all class level checkboxes
      const checkboxes = form.querySelectorAll('input[name="class_level_ids"]');
      checkboxes.forEach(checkbox => checkbox.checked = false);

      // Reset stream names to default (2 fields)
      this.resetStreamNamesContainer();

      // Reset creation mode to 'new'
      const newModeRadio = document.querySelector('input[name="creation_mode"][value="new"]');
      if (newModeRadio) {
        newModeRadio.checked = true;
      }

      // Show new streams section, hide copy section
      const newStreamsSection = document.getElementById('new-streams-section');
      const copyStreamsSection = document.getElementById('copy-streams-section');
      newStreamsSection?.classList.remove('hidden');
      copyStreamsSection?.classList.add('hidden');

      // Reset source class selection
      const sourceSelect = document.getElementById('source-class-select');
      if (sourceSelect) {
        sourceSelect.value = '';
      }

      // Hide source streams preview
      const previewDiv = document.getElementById('source-streams-preview');
      if (previewDiv) {
        previewDiv.classList.add('hidden');
      }
    }

    // Reset modal title
    const modalTitle = document.querySelector('#add-stream-modal h3');
    if (modalTitle) {
      modalTitle.textContent = 'Add New Streams';
    }
  },

  // Add stream name field
  addStreamName() {
    const container = document.getElementById('stream-names-container');
    if (!container) return;

    // Count existing editable inputs (not readonly ones)
    const currentEditableInputs = container.querySelectorAll('input[name="stream_names[]"]:not([readonly])');
    const newIndex = currentEditableInputs.length;

    // Create new editable input
    this.createNewStreamInput(newIndex);

    // Apply current mode's required state to all inputs
    const selectedMode = document.querySelector('input[name="creation_mode"]:checked')?.value;
    if (selectedMode === 'new') {
      this.setStreamNameInputsRequired(true);
    } else {
      this.setStreamNameInputsRequired(false);
    }

    // Ensure the Add Another Stream button is properly displayed
    this.addStreamButton();
  },

  // Remove stream name field
  removeStreamName(index) {
    const container = document.getElementById('stream-names-container');
    if (!container) return;

    // Count total streams (readonly + editable)
    const readonlyInputs = container.querySelectorAll('input[readonly]');
    const editableInputs = container.querySelectorAll('input[name="stream_names[]"]:not([readonly])');
    const totalStreams = readonlyInputs.length + editableInputs.length;

    // Check removal rules based on whether there are existing streams
    if (readonlyInputs.length > 0) {
      // If there are existing streams, just ensure at least 1 editable input remains
      if (editableInputs.length <= 1) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('You must have at least 1 new stream to add', 'warning');
        } else {
          alert('You must have at least 1 new stream to add');
        }
        return;
      }
    } else {
      // If no existing streams, ensure at least 2 total streams remain
      if (totalStreams <= 2) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('O-Level classes must have at least 2 streams when creating streams for the first time', 'warning');
        } else {
          alert('O-Level classes must have at least 2 streams when creating streams for the first time');
        }
        return;
      }
    }

    // Find and remove the specific div
    const targetInput = document.getElementById(`stream_name_${index}`);
    if (targetInput && !targetInput.readOnly) {
      const targetDiv = targetInput.closest('.flex.items-center.space-x-2');
      if (targetDiv) {
        targetDiv.remove();
        // Re-index remaining editable inputs
        this.reindexStreamInputs();
      }
    }
  },

  // Re-index stream inputs after removal
  reindexStreamInputs() {
    const container = document.getElementById('stream-names-container');
    if (!container) return;

    const streamInputs = container.querySelectorAll('input[name="stream_names[]"]');
    streamInputs.forEach((input, newIndex) => {
      input.id = `stream_name_${newIndex}`;

      // Update remove button onclick if it exists
      const removeButton = input.parentElement.querySelector('button[onclick*="removeStreamName"]');
      if (removeButton) {
        removeButton.setAttribute('onclick', `ManageStreamsComponent.removeStreamName(${newIndex})`);
      }
    });
  },

  // Reset stream names container to default state (2 fields)
  resetStreamNamesContainer() {
    const container = document.getElementById('stream-names-container');
    if (!container) return;

    // Clear existing content
    container.innerHTML = '';

    // Create default inputs instead of calling populateStreamNamesWithExisting
    this.createDefaultStreamInputs();
  },

  // Populate stream names container - show existing streams as readonly if any class has streams
  populateStreamNamesWithExisting() {
    const container = document.getElementById('stream-names-container');
    if (!container) return;

    // Check if user has already entered stream names - if so, don't clear them
    const hasUserInput = this.hasUserEnteredStreamNames();
    if (hasUserInput) {
      // User has input, just update button state based on selection
      const selectedClassLevels = Array.from(document.querySelectorAll('input[name="class_level_ids"]:checked'))
        .map(checkbox => parseInt(checkbox.value));

      if (selectedClassLevels.length > 0) {
        this.enableCreateButton();
      }
      return; // Don't clear user input
    }

    // Clear the container only if no user input
    container.innerHTML = '';

    const selectedClassLevels = Array.from(document.querySelectorAll('input[name="class_level_ids"]:checked'))
      .map(checkbox => parseInt(checkbox.value));

    if (selectedClassLevels.length === 0) {
      // No classes selected, show default empty inputs
      this.createDefaultStreamInputs();
      this.enableCreateButton();
      return;
    }

    // Get existing streams for selected classes
    const streamsResponse = ClassesStreamsComponents.state.streams || {};
    const allStreams = streamsResponse.data || [];

    // Check if ANY selected class has existing streams
    let hasAnyExistingStreams = false;
    const allExistingStreams = [];

    for (const classLevelId of selectedClassLevels) {
      const classExistingStreams = allStreams.filter(stream =>
        stream.stream_type === 'o_level' &&
        stream.class_level_ids &&
        stream.class_level_ids.split(',').includes(classLevelId.toString())
      );

      if (classExistingStreams.length > 0) {
        hasAnyExistingStreams = true;
        // Add streams with class info
        classExistingStreams.forEach(stream => {
          if (!allExistingStreams.find(s => s.name === stream.name)) {
            allExistingStreams.push(stream);
          }
        });
      }
    }

    if (hasAnyExistingStreams) {
      // Show existing streams as readonly and disable creation
      this.showExistingStreamsReadonly(allExistingStreams);
      this.disableCreateButton();
    } else {
      // No existing streams, show default inputs
      this.createDefaultStreamInputs();
      this.enableCreateButton();
    }
  },

  // Create default stream inputs when no classes are selected
  createDefaultStreamInputs() {
    const container = document.getElementById('stream-names-container');
    if (!container) return;

    container.innerHTML = `
      <div class="flex items-center space-x-2">
        <input type="text"
               id="stream_name_0"
               name="stream_names[]"
               value=""
               class="flex-1 px-4 py-3 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
               placeholder="e.g., A"
               tabindex="0"
               oninput="ManageStreamsComponent.handleStreamNameInput(0)">
      </div>
      <div class="flex items-center space-x-2">
        <input type="text"
               id="stream_name_1"
               name="stream_names[]"
               value=""
               class="flex-1 px-4 py-3 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
               placeholder="e.g., B"
               tabindex="0"
               oninput="ManageStreamsComponent.handleStreamNameInput(1)">
        <button type="button" onclick="ManageStreamsComponent.removeStreamName(1)"
                class="px-3 py-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                tabindex="0">
          ${SRDesignSystem.components.icon('fas fa-trash', 'base', 'current')}
        </button>
      </div>
    `;

    // Add the "Add Another Stream" button
    this.addStreamButton();
  },

  // Create a new editable stream input
  createNewStreamInput(index) {
    const container = document.getElementById('stream-names-container');
    if (!container) return;

    const newStreamDiv = document.createElement('div');
    newStreamDiv.className = 'flex items-center space-x-2';
    newStreamDiv.innerHTML = `
      <input type="text"
             id="stream_name_${index}"
             name="stream_names[]"
             value=""
             class="flex-1 px-4 py-3 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
             placeholder="e.g., ${String.fromCharCode(65 + index)}"
             tabindex="0"
             oninput="ManageStreamsComponent.handleStreamNameInput(${index})">
      ${index > 0 ? `
        <button type="button" onclick="ManageStreamsComponent.removeStreamName(${index})"
                class="px-3 py-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                tabindex="0">
          ${SRDesignSystem.components.icon('fas fa-trash', 'base', 'current')}
        </button>
      ` : ''}
    `;

    container.appendChild(newStreamDiv);
  },

  // Handle stream name input - hide field if stream already exists for selected classes
  handleStreamNameInput(index) {
    const input = document.getElementById(`stream_name_${index}`);
    if (!input) return;

    const streamName = input.value.trim();
    if (!streamName) return;

    // Get selected class levels
    const selectedClassLevels = Array.from(document.querySelectorAll('input[name="class_level_ids"]:checked'))
      .map(checkbox => parseInt(checkbox.value));

    if (selectedClassLevels.length === 0) return;

    // Check if this stream exists in ALL selected classes
    const streamsResponse = ClassesStreamsComponents.state.streams || {};
    const allStreams = streamsResponse.data || [];

    const streamExists = allStreams.some(stream => {
      if (stream.stream_type !== 'o_level' || !stream.class_level_ids) return false;

      // Check if stream name matches
      if (stream.name.toLowerCase() !== streamName.toLowerCase()) return false;

      // Check if stream exists in ALL selected classes
      const streamClassIds = stream.class_level_ids.split(',').map(id => parseInt(id));
      return selectedClassLevels.every(classId => streamClassIds.includes(classId));
    });

    const inputContainer = input.closest('.flex.items-center.space-x-2');
    if (!inputContainer) return;

    if (streamExists) {
      // Stream exists in all selected classes - hide this input field
      inputContainer.style.display = 'none';
      // Mark as hidden for later processing
      inputContainer.setAttribute('data-hidden', 'true');
    } else {
      // Stream doesn't exist or input is empty - show the field
      inputContainer.style.display = 'flex';
      inputContainer.removeAttribute('data-hidden');
    }
  },

  // Show all hidden stream input fields (called when class selection changes)
  showAllStreamInputs() {
    const hiddenContainers = document.querySelectorAll('[data-hidden="true"]');
    hiddenContainers.forEach(container => {
      container.style.display = 'flex';
      container.removeAttribute('data-hidden');
    });
  },

  // Show existing streams as readonly
  showExistingStreamsReadonly(existingStreams) {
    const container = document.getElementById('stream-names-container');
    if (!container) return;

    // Clear container
    container.innerHTML = '';

    // Add a header
    const headerDiv = document.createElement('div');
    headerDiv.className = 'mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg';
    headerDiv.innerHTML = `
      <div class="flex items-center">
        <i class="fas fa-info-circle text-yellow-600 mr-2"></i>
        <span class="text-sm font-medium text-yellow-800">
          Selected class already has streams. Cannot add more streams.
        </span>
      </div>
    `;
    container.appendChild(headerDiv);

    // Show existing streams as readonly
    existingStreams.forEach((stream, index) => {
      const existingStreamDiv = document.createElement('div');
      existingStreamDiv.className = 'flex items-center space-x-2 mb-2';
      existingStreamDiv.innerHTML = `
        <input type="text"
               id="existing_stream_${index}"
               value="${stream.name}"
               class="flex-1 px-4 py-3 text-sm border border-gray-200 rounded-lg bg-gray-50 text-gray-600 cursor-not-allowed"
               readonly
               tabindex="-1">
        <span class="px-3 py-2 text-sm text-gray-500 bg-gray-100 rounded-lg">
          <i class="fas fa-lock mr-1"></i>Existing
        </span>
      `;
      container.appendChild(existingStreamDiv);
    });

    // Hide the Add Another Stream button
    this.hideAddStreamButton();
  },

  // Enable create button
  enableCreateButton() {
    const createButton = document.getElementById('save-stream');
    if (createButton) {
      createButton.disabled = false;
      createButton.classList.remove('opacity-50', 'cursor-not-allowed');
    }
  },

  // Disable create button
  disableCreateButton() {
    const createButton = document.getElementById('save-stream');
    if (createButton) {
      createButton.disabled = true;
      createButton.classList.add('opacity-50', 'cursor-not-allowed');
    }
  },

  // Hide Add Another Stream button
  hideAddStreamButton() {
    const buttonContainer = document.getElementById('add-stream-button-container');
    if (buttonContainer) {
      buttonContainer.innerHTML = '';
    }
  },

  // Add the "Add Another Stream" button
  addStreamButton() {
    const buttonContainer = document.getElementById('add-stream-button-container');
    if (!buttonContainer) return;

    // Clear any existing button
    buttonContainer.innerHTML = '';

    // Only show the button in "new" mode
    const selectedMode = document.querySelector('input[name="creation_mode"]:checked')?.value;
    if (selectedMode === 'new') {
      buttonContainer.innerHTML = `
        <button type="button" onclick="ManageStreamsComponent.addStreamName()"
                class="mt-2 text-blue-600 hover:text-blue-800 text-sm">
          <i class="fas fa-plus mr-1"></i> Add Another Stream
        </button>
      `;
    }
  },

  // Show edit stream modal
  showEditStreamModal(stream) {
    const modal = document.getElementById('edit-stream-modal');
    if (modal) {
      modal.classList.remove('hidden');

      // Use setTimeout to ensure DOM is ready
      setTimeout(() => {
        // Populate form with stream data
        const streamIdInput = document.getElementById('edit-stream-id');
        const streamNameInput = document.getElementById('edit_stream_name');

        if (streamIdInput) {
          streamIdInput.value = stream.id;
        }

        if (streamNameInput) {
          streamNameInput.value = stream.name;
          // Focus the input field without selecting text
          streamNameInput.focus();
          // Position cursor at end of text
          streamNameInput.setSelectionRange(streamNameInput.value.length, streamNameInput.value.length);
        }


      }, 50);

      // Initialize form event listeners directly
      this.initializeEditFormEventListeners();
    }
  },

  // Close edit stream modal
  closeEditStreamModal() {
    const modal = document.getElementById('edit-stream-modal');
    if (modal) {
      modal.classList.add('hidden');
      // Reset form
      const form = document.getElementById('edit-stream-form');
      if (form) {
        form.reset();
      }
    }
  },

  // Handle add stream form submission (multiple streams)
  async handleAddStream(event) {
    event.preventDefault();
    event.stopPropagation();

    // Check if create button is disabled
    const createButton = document.getElementById('save-stream');
    if (createButton && createButton.disabled) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Cannot create streams for classes that already have streams', 'warning');
      } else {
        alert('Cannot create streams for classes that already have streams');
      }
      return;
    }

    // Get creation mode
    const creationMode = document.querySelector('input[name="creation_mode"]:checked')?.value;

    let streamNames = [];

    if (creationMode === 'new') {
      // Get stream names from inputs (excluding hidden ones)
      const streamNameInputs = document.querySelectorAll('input[name="stream_names[]"]');
      const visibleInputs = Array.from(streamNameInputs).filter(input => {
        const container = input.closest('.flex.items-center.space-x-2');
        return !container || container.getAttribute('data-hidden') !== 'true';
      });

      streamNames = visibleInputs
        .map(input => input.value.trim())
        .filter(name => name.length > 0);

      // Check if all inputs were hidden (all streams already exist)
      const allInputsHidden = streamNameInputs.length > 0 && visibleInputs.length === 0;
      if (allInputsHidden) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(
            'Please add one or more new stream names. All entered streams already exist for the selected classes.',
            'warning',
            6000
          );
        } else {
          alert('Please add one or more new stream names. All entered streams already exist for the selected classes.');
        }
        return;
      }

    } else if (creationMode === 'copy') {
      // Get stream names from source class
      const sourceClassId = document.getElementById('source-class-select')?.value;
      if (!sourceClassId) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Please select a source class to copy streams from', 'error');
        }
        return;
      }

      // Get streams from source class
      const streamsResponse = ClassesStreamsComponents.state.streams || {};
      const allStreams = streamsResponse.data || [];

      const sourceStreams = allStreams.filter(stream => {
        if (stream.stream_type !== 'o_level' || !stream.class_level_ids) return false;
        const classIds = stream.class_level_ids.split(',');
        return classIds.includes(sourceClassId);
      });

      streamNames = sourceStreams.map(stream => stream.name);
    }

    // Get selected class levels first to check existing streams
    const selectedClassLevels = Array.from(document.querySelectorAll('input[name="class_level_ids"]:checked'))
      .map(checkbox => parseInt(checkbox.value));

    if (selectedClassLevels.length === 0) {
      alert('Please select at least one class level for these streams');
      return;
    }

    // Get existing streams data for validation
    const streamsResponse = ClassesStreamsComponents.state.streams || {};
    const existingStreams = streamsResponse.data || [];



    // Check if any selected class already has existing streams
    let hasAnyExistingStreams = false;
    for (const classLevelId of selectedClassLevels) {
      const classExistingStreams = existingStreams.filter(stream =>
        stream.stream_type === 'o_level' &&
        stream.class_level_ids &&
        stream.class_level_ids.split(',').includes(classLevelId.toString())
      );
      if (classExistingStreams.length > 0) {
        hasAnyExistingStreams = true;
        break;
      }
    }

    // Validation based on whether classes have existing streams
    if (hasAnyExistingStreams) {
      // Classes have existing streams - allow adding just 1 new stream
      if (streamNames.length < 1) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Please enter at least one new stream name', 'error');
        } else {
          alert('Please enter at least one new stream name');
        }
        return;
      }
    } else {
      // No existing streams - require at least 2 streams for first-time creation
      if (streamNames.length < 2) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('O-Level classes must have at least 2 streams when creating streams for the first time', 'error');
        } else {
          alert('O-Level classes must have at least 2 streams when creating streams for the first time');
        }
        return;
      }
    }

    // Check for duplicate stream names within the form
    const uniqueNames = [...new Set(streamNames.map(name => name.toLowerCase()))];
    if (uniqueNames.length !== streamNames.length) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Stream names must be unique', 'error');
      } else {
        alert('Stream names must be unique');
      }
      return;
    }

    // Handle different creation modes
    if (creationMode === 'copy') {
      // Simple copy mode: only works for classes with NO existing streams
      const classesWithStreams = [];

      for (const classLevelId of selectedClassLevels) {
        const classExistingStreams = existingStreams.filter(stream =>
          stream.stream_type === 'o_level' &&
          stream.class_level_ids &&
          stream.class_level_ids.split(',').includes(classLevelId.toString())
        );

        if (classExistingStreams.length > 0) {
          // Find class name for error message
          const classesResponse = ClassesStreamsComponents.state.classes || {};
          const allClasses = classesResponse.data || [];
          const className = allClasses.find(c => c.id === classLevelId)?.name || `Class ${classLevelId}`;
          classesWithStreams.push(className);
        }
      }

      // If any selected class has existing streams, show error and abort
      if (classesWithStreams.length > 0) {
        const classNames = classesWithStreams.join(', ');
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(
            `Copy mode only works for classes without existing streams. The following classes already have streams: ${classNames}`,
            'error',
            8000
          );
        } else {
          alert(`Copy mode only works for classes without existing streams. The following classes already have streams: ${classNames}`);
        }
        return;
      }
    }

    // Check if any selected class already has streams
    const classesWithStreams = [];
    for (const classLevelId of selectedClassLevels) {
      const classExistingStreams = existingStreams.filter(stream =>
        stream.stream_type === 'o_level' &&
        stream.class_level_ids &&
        stream.class_level_ids.split(',').includes(classLevelId.toString())
      );
      if (classExistingStreams.length > 0) {
        // Find class name for display
        const classesResponse = ClassesStreamsComponents.state.classes || {};
        const allClasses = classesResponse.data || [];
        const className = allClasses.find(c => c.id === classLevelId)?.name || `Class ${classLevelId}`;
        classesWithStreams.push({
          id: classLevelId,
          name: className,
          streams: classExistingStreams.map(s => s.name)
        });
      }
    }

    // Only show dialog if multiple classes are selected and some have existing streams
    if (classesWithStreams.length > 0 && selectedClassLevels.length > 1) {
      const classNames = classesWithStreams.map(c => `${c.name} (${c.streams.join(', ')})`).join('\n');
      const confirmMessage = `ℹ️ INFO: The following classes already have streams:\n\n${classNames}\n\nThe streams you're creating will be added to the other selected classes that don't have them yet. Existing stream associations will not be affected.\n\nDo you want to continue?`;

      if (!confirm(confirmMessage)) {
        return; // User cancelled
      }
    }

    try {
      if (window.SRDesignSystem && window.SRDesignSystem.forms) {
        window.SRDesignSystem.forms.setButtonLoading('save-stream', true);
      }

      // Create or update stream associations (no replacement logic)
      console.log('Creating stream associations for selected classes...');
      const createdStreams = [];

      if (creationMode === 'copy') {
        // Simple copy mode - copy streams to classes without existing streams
        for (const streamName of streamNames) {
          const existingStream = existingStreams.find(stream =>
            stream.stream_type === 'o_level' &&
            stream.name.toLowerCase() === streamName.toLowerCase()
          );

          if (existingStream) {
            // Stream exists - add selected classes to its associations
            const currentClassIds = existingStream.class_level_ids ?
              existingStream.class_level_ids.split(',').map(id => parseInt(id)) : [];

            // Add selected classes to existing stream (remove duplicates)
            const updatedClassIds = [...new Set([...currentClassIds, ...selectedClassLevels])];

            const updateData = {
              name: existingStream.name,
              stream_type: 'o_level',
              class_level_ids: updatedClassIds
            };

            const result = await window.StreamsAPI.update(existingStream.id, updateData);
            if (result.success) {
              createdStreams.push(result.data);
              console.log(`Copied existing stream "${streamName}" to selected classes`);
            } else {
              throw new Error(`Failed to copy stream "${streamName}": ${result.message}`);
            }
          } else {
            // Stream doesn't exist - create new one for selected classes
            const streamData = {
              name: streamName.trim().toUpperCase(),
              stream_type: 'o_level',
              class_level_ids: selectedClassLevels
            };

            const result = await window.StreamsAPI.create(streamData);
            if (result.success) {
              createdStreams.push(result.data);
              console.log(`Created new stream "${streamName}" for selected classes`);
            } else {
              throw new Error(`Failed to create stream "${streamName}": ${result.message}`);
            }
          }
        }
      } else {
        // New mode - create associations for existing streams, create new streams for non-existing ones
        for (const streamName of streamNames) {
          const existingStream = existingStreams.find(stream =>
            stream.stream_type === 'o_level' &&
            stream.name.toLowerCase() === streamName.toLowerCase()
          );

          if (existingStream) {
            // Stream exists - add only classes that don't already have this stream
            const currentClassIds = existingStream.class_level_ids ?
              existingStream.class_level_ids.split(',').map(id => parseInt(id)) : [];

            // Find classes that don't already have this stream
            const newClassIds = selectedClassLevels.filter(classId =>
              !currentClassIds.includes(classId)
            );

            if (newClassIds.length > 0) {
              // Add new classes to existing stream
              const updatedClassIds = [...currentClassIds, ...newClassIds];

              const updateData = {
                name: existingStream.name,
                stream_type: 'o_level',
                class_level_ids: updatedClassIds
              };

              const result = await window.StreamsAPI.update(existingStream.id, updateData);
              if (result.success) {
                createdStreams.push(result.data);
                console.log(`Added class associations to existing stream "${streamName}" for ${newClassIds.length} new classes`);
              } else {
                throw new Error(`Failed to update stream "${streamName}": ${result.message}`);
              }
            } else {
              console.log(`Stream "${streamName}" already exists in all selected classes - skipping`);
            }
          } else {
            // Stream doesn't exist - create new one
            const streamData = {
              name: streamName.trim().toUpperCase(),
              stream_type: 'o_level',
              class_level_ids: selectedClassLevels
            };

            const result = await window.StreamsAPI.create(streamData);
            if (result.success) {
              createdStreams.push(result.data);
              console.log(`Created new stream "${streamName}"`);
            } else {
              throw new Error(`Failed to create stream "${streamName}": ${result.message}`);
            }
          }
        }
      }

      // Success message with context
      let successMessage = '';
      if (creationMode === 'copy') {
        // Simple copy mode success message
        successMessage = `${createdStreams.length} streams copied successfully to selected classes!`;
      } else {
        // New mode - count associations vs new creations
        const associationCount = createdStreams.filter(stream =>
          existingStreams.some(existing => existing.name.toLowerCase() === stream.name.toLowerCase())
        ).length;
        const newStreamCount = createdStreams.length - associationCount;

        if (associationCount > 0 && newStreamCount > 0) {
          successMessage = `Success! Created ${newStreamCount} new streams and added associations for ${associationCount} existing streams.`;
        } else if (associationCount > 0) {
          successMessage = `Success! Added class associations for ${associationCount} existing streams.`;
        } else {
          successMessage = `${newStreamCount} new streams created successfully!`;
        }
      }

      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show(successMessage, 'success', 6000); // Show longer for replacement message
      } else {
        alert(successMessage);
      }

      // Clean up analysis data
      if (this.copyAnalysis) {
        delete this.copyAnalysis;
      }

      this.closeAddStreamModal();
      await ClassesStreamsComponents.loadInitialData();
      this.populateStreamsTable();

    } catch (error) {
      console.error('Create streams error:', error);

      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to create streams', 'error');
      } else {
        alert('Error: Failed to create streams');
      }
    } finally {
      if (window.SRDesignSystem && window.SRDesignSystem.forms) {
        window.SRDesignSystem.forms.setButtonLoading('save-stream', false);
      }
    }
  },

  // Handle edit stream form submission
  async handleEditStream(event) {
    event.preventDefault();
    event.stopPropagation();

    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());

    // Validate stream name
    if (!data.edit_stream_name || data.edit_stream_name.trim() === '') {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Please enter a stream name', 'error');
      }
      return;
    }

    // Check for case-insensitive duplicates in existing streams (excluding current stream)
    const editStreamsResponse = ClassesStreamsComponents.state.streams || {};
    const editExistingStreams = editStreamsResponse.data || [];
    const editStreamName = data.edit_stream_name.trim();
    const currentStreamId = parseInt(data.stream_id);

    const editDuplicateStream = editExistingStreams.find(stream =>
      stream.stream_type === 'o_level' &&
      stream.id !== currentStreamId &&
      stream.name.toLowerCase() === editStreamName.toLowerCase()
    );

    if (editDuplicateStream) {
      const duplicateClassNames = editDuplicateStream.class_names || 'Unknown Class';
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show(
          `Stream name "${editDuplicateStream.name}" already exists in ${duplicateClassNames}. Stream names must be unique across all O-Level classes. Please choose a different name.`,
          'error',
          6000
        );
      } else {
        alert(`Stream name "${editDuplicateStream.name}" already exists in ${duplicateClassNames}. Stream names must be unique across all O-Level classes. Please choose a different name.`);
      }
      return;
    }

    const streamId = data.stream_id;

    const updateData = {
      name: data.edit_stream_name.trim(),
      stream_type: 'o_level'
    };

    try {
      if (window.SRDesignSystem && window.SRDesignSystem.forms) {
        window.SRDesignSystem.forms.setButtonLoading('update-stream', true);
      }

      // Use the API service
      const result = await window.StreamsAPI.update(streamId, updateData);

      if (result.success) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Stream updated successfully!', 'success');
        } else {
          alert('Stream updated successfully!');
        }
        this.closeEditStreamModal();
        await ClassesStreamsComponents.loadInitialData();
        this.populateStreamsTable();
      } else {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to update stream', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to update stream'));
        }
      }
    } catch (error) {
      console.error('Update stream error:', error);

      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to update stream', 'error');
      } else {
        alert('Error: Failed to update stream');
      }
    } finally {
      if (window.SRDesignSystem && window.SRDesignSystem.forms) {
        window.SRDesignSystem.forms.setButtonLoading('update-stream', false);
      }
    }
  },

  // Edit stream
  editStream(streamId) {
    // Find the stream data
    const streamsResponse = ClassesStreamsComponents.state.streams || {};
    const streams = streamsResponse.data || [];
    const stream = streams.find(s => s.id == streamId);

    if (!stream) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Stream not found', 'error');
      }
      return;
    }

    // Only allow editing O-Level streams
    if (stream.stream_type !== 'o_level') {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('A-Level streams cannot be edited', 'warning');
      }
      return;
    }

    // Show edit modal
    this.showEditStreamModal(stream);
  },



  // Delete stream - show modal for class selection
  deleteStream(streamId) {
    // Find the stream data
    const streamsResponse = ClassesStreamsComponents.state.streams || {};
    const streams = streamsResponse.data || [];
    const stream = streams.find(s => s.id == streamId);

    if (!stream) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Stream not found', 'error');
      }
      return;
    }

    // Only allow deleting O-Level streams
    if (stream.stream_type !== 'o_level') {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('A-Level streams cannot be deleted', 'warning');
      }
      return;
    }

    // Show delete modal
    this.showDeleteStreamModal(stream);
  },

  // Show delete stream modal
  showDeleteStreamModal(stream) {
    const modal = document.getElementById('delete-stream-modal');
    if (!modal) return;

    // Store stream data for later use
    this.currentStreamToDelete = stream;

    // Populate stream name
    const streamNameElement = document.getElementById('delete-stream-name');
    if (streamNameElement) {
      streamNameElement.textContent = stream.name;
    }

    // Populate classes that have this stream
    this.populateDeleteClassesSelection(stream);

    // Set up delete option listeners
    this.setupDeleteOptionListeners();

    // Show modal
    modal.classList.remove('hidden');
  },

  // Populate classes selection for deletion
  populateDeleteClassesSelection(stream) {
    const container = document.getElementById('delete-classes-selection');
    if (!container) return;

    // Get classes that have this stream
    const classIds = stream.class_level_ids ? stream.class_level_ids.split(',') : [];
    const classNames = stream.class_names ? stream.class_names.split(', ') : [];

    if (classIds.length === 0) {
      container.innerHTML = '<p class="text-sm text-gray-500">No classes found for this stream</p>';
      return;
    }

    // Create checkboxes for each class (unchecked by default)
    container.innerHTML = classIds.map((classId, index) => {
      const className = classNames[index] || `Class ${classId}`;
      return `
        <label class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer whitespace-nowrap">
          <input type="checkbox"
                 name="delete_class_ids"
                 value="${classId}"
                 class="rounded border-gray-300 text-red-600 focus:ring-red-500">
          <span class="text-sm text-gray-700">${className}</span>
        </label>
      `;
    }).join('');
  },

  // Setup delete option listeners
  setupDeleteOptionListeners() {
    const deleteOptions = document.querySelectorAll('input[name="delete_option"]');
    const classSelectionSection = document.getElementById('class-selection-section');

    deleteOptions.forEach(option => {
      option.addEventListener('change', () => {
        if (option.value === 'delete_completely') {
          classSelectionSection.style.display = 'none';
        } else {
          classSelectionSection.style.display = 'block';
        }
      });
    });
  },

  // Close delete stream modal
  closeDeleteStreamModal() {
    const modal = document.getElementById('delete-stream-modal');
    if (modal) {
      modal.classList.add('hidden');
    }
    this.currentStreamToDelete = null;
  },

  // Confirm and execute stream deletion
  async confirmDeleteStream() {
    if (!this.currentStreamToDelete) return;

    const deleteOption = document.querySelector('input[name="delete_option"]:checked')?.value;

    if (deleteOption === 'delete_completely') {
      // Delete stream completely from all classes
      await this.executeCompleteStreamDeletion();
    } else {
      // Proceed with partial deletion
      await this.executePartialStreamDeletion();
    }
  },



  // Execute complete stream deletion
  async executeCompleteStreamDeletion() {
    const stream = this.currentStreamToDelete;

    try {
      const result = await window.StreamsAPI.delete(stream.id);

      if (result.success) {
        const data = result.data || {};
        let message = `Stream "${stream.name}" deleted completely from all classes.`;

        if (data.affected_students && data.affected_students.total > 0) {
          message += ` ${data.affected_students.total} students were unassigned from streams.`;
        }

        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(message, 'success', 6000);
        }

        this.closeDeleteStreamModal();
        await ClassesStreamsComponents.loadInitialData();
        this.populateStreamsTable();
      } else {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to delete stream', 'error');
        }
      }
    } catch (error) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to delete stream', 'error');
      }
    }
  },

  // Execute partial stream deletion (remove from selected classes)
  async executePartialStreamDeletion() {
    const stream = this.currentStreamToDelete;
    const checkedClassIds = Array.from(document.querySelectorAll('input[name="delete_class_ids"]:checked'))
      .map(checkbox => parseInt(checkbox.value));

    // Get all class IDs that have this stream
    const allClassIds = stream.class_level_ids ? stream.class_level_ids.split(',').map(id => parseInt(id)) : [];

    // Classes to remove stream from = checked classes
    const selectedClassIds = checkedClassIds;

    if (selectedClassIds.length === 0) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Please select at least one class to remove the stream from', 'error');
      }
      return;
    }

    // If all classes are checked, ask if user wants to delete the entire stream
    if (selectedClassIds.length === allClassIds.length) {
      const confirmDelete = confirm(`All classes are selected to remove the stream "${stream.name}".\n\nThis will delete the entire stream from all classes.\n\nDo you want to continue?`);

      if (confirmDelete) {
        // User wants to delete the entire stream
        await this.executeCompleteStreamDeletion();
        return;
      } else {
        // User cancelled
        return;
      }
    }

    try {
      // Get current stream data
      const currentClassIds = stream.class_level_ids ? stream.class_level_ids.split(',').map(id => parseInt(id)) : [];
      // Remaining classes = classes not selected for removal (unchecked classes)
      const remainingClassIds = currentClassIds.filter(id => !selectedClassIds.includes(id));

      if (remainingClassIds.length === 0) {
        // If no classes remain, delete the stream completely
        await this.executeCompleteStreamDeletion();
        return;
      }

      // Simple stream removal - just remove the stream from selected classes
      const updateData = {
        name: stream.name,
        stream_type: 'o_level',
        class_level_ids: remainingClassIds
      };

      console.log('Updating stream with data:', updateData);
      console.log('Original class IDs:', currentClassIds);
      console.log('Selected class IDs to remove:', selectedClassIds);
      console.log('Remaining class IDs:', remainingClassIds);

      const result = await window.StreamsAPI.update(stream.id, updateData);

      if (result.success) {
        let message = `Stream "${stream.name}" removed from selected classes.`;

        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(message, 'success', 6000);
        }
      } else {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to update stream', 'error');
        }
      }

      // Refresh the data and close modal
      this.closeDeleteStreamModal();
      await ClassesStreamsComponents.loadInitialData();
      this.populateStreamsTable();
    } catch (error) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to update stream', 'error');
      }
    }
  },

  // Navigate to academic year setup
  navigateToAcademicSetup() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('academic-year-setup');
    }
  }
};

// Export components to global scope
window.ManageStreamsComponent = ManageStreamsComponent;
window.ClassesStreamsComponents = ClassesStreamsComponents;
