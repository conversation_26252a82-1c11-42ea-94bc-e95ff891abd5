const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// =============================================
// ENROLLMENT MANAGEMENT ROUTES (Using Student Tables Directly)
// =============================================

// Get all enrollments with filters (now using student tables directly)
router.get('/', async (req, res) => {
  try {
    const { 
      academic_year_id, 
      term_id, 
      class_id, 
      stream_id, 
      status, 
      student_type,
      page = 1, 
      limit = 50 
    } = req.query;

    let whereClause = 'WHERE 1=1';
    let params = [];

    // Build where clause (same for both student types)
    if (academic_year_id) {
      whereClause += ' AND s.current_academic_year_id = ?';
      params.push(academic_year_id);
    }

    if (term_id) {
      whereClause += ' AND s.current_term_id = ?';
      params.push(term_id);
    }

    if (class_id) {
      whereClause += ' AND s.current_class_id = ?';
      params.push(class_id);
    }

    if (stream_id) {
      whereClause += ' AND s.stream_id = ?';
      params.push(stream_id);
    }

    if (status) {
      const statusValues = status.split(',').map(s => s.trim());
      const placeholders = statusValues.map(() => '?').join(',');
      whereClause += ` AND s.status IN (${placeholders})`;
      params.push(...statusValues);
    }

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // O-Level students query
    const oLevelQuery = `
      SELECT 
        s.id as student_id,
        'o_level' as student_type,
        s.current_class_id as class_id,
        s.stream_id,
        s.current_academic_year_id as academic_year_id,
        s.current_term_id as term_id,
        s.status,
        s.enrollment_date,
        s.created_at,
        s.updated_at,
        c.name as class_name,
        cl.name as class_level_name,
        st.name as stream_name,
        ay.name as academic_year_name,
        t.name as term_name,
        CONCAT(s.first_name, ' ', COALESCE(s.middle_name, ''), ' ', s.last_name) as student_name,
        s.admission_number,
        s.gender
      FROM o_level_students s
      LEFT JOIN classes c ON s.current_class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN streams st ON s.stream_id = st.id
      LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
      LEFT JOIN terms t ON s.current_term_id = t.id
      ${whereClause}
    `;

    // A-Level students query
    const aLevelQuery = `
      SELECT
        s.id as student_id,
        'a_level' as student_type,
        s.current_class_id as class_id,
        s.stream_id,
        s.current_academic_year_id as academic_year_id,
        s.current_term_id as term_id,
        s.status,
        s.registration_date as enrollment_date,
        s.created_at,
        s.updated_at,
        c.name as class_name,
        cl.name as class_level_name,
        st.name as stream_name,
        ay.name as academic_year_name,
        t.name as term_name,
        CONCAT(s.first_name, ' ', COALESCE(s.middle_name, ''), ' ', s.last_name) as student_name,
        s.admission_number,
        s.gender
      FROM a_level_students s
      LEFT JOIN classes c ON s.current_class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN streams st ON s.stream_id = st.id
      LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
      LEFT JOIN terms t ON s.current_term_id = t.id
      ${whereClause}
    `;

    let finalQuery = '';
    let countQuery = '';
    let queryParams = [];
    let countParams = [];

    if (!student_type) {
      // Both student types - need to duplicate params for UNION
      const unionParams = [...params, ...params]; // Duplicate params for both queries
      finalQuery = `(${oLevelQuery}) UNION ALL (${aLevelQuery}) ORDER BY created_at DESC LIMIT ? OFFSET ?`;
      countQuery = `
        SELECT (ol.total + al.total) as total
        FROM (SELECT COUNT(*) as total FROM o_level_students s ${whereClause}) ol,
             (SELECT COUNT(*) as total FROM a_level_students s ${whereClause}) al
      `;
      // Add limit and offset to union params
      queryParams = [...unionParams, parseInt(limit), offset];
      // For count query, duplicate params for both count queries
      countParams = [...params, ...params];
    } else if (student_type === 'o_level') {
      // O-Level only
      finalQuery = `${oLevelQuery} ORDER BY s.created_at DESC LIMIT ? OFFSET ?`;
      countQuery = `SELECT COUNT(*) as total FROM o_level_students s ${whereClause}`;
      queryParams = [...params, parseInt(limit), offset];
      countParams = params;
    } else {
      // A-Level only
      finalQuery = `${aLevelQuery} ORDER BY s.created_at DESC LIMIT ? OFFSET ?`;
      countQuery = `SELECT COUNT(*) as total FROM a_level_students s ${whereClause}`;
      queryParams = [...params, parseInt(limit), offset];
      countParams = params;
    }

    console.log('🔍 Enrollments Query Debug:', {
      finalQuery: finalQuery.substring(0, 200) + '...',
      queryParams,
      filters: req.query
    });

    const result = await executeQuery(finalQuery, queryParams);

    if (!result.success) {
      console.error('❌ Enrollments query failed:', result.error);
      throw new Error(result.error);
    }

    console.log(`✅ Found ${result.data.length} enrollments`);

    // Get total count for pagination
    const countResult = await executeQuery(countQuery, countParams);
    const total = countResult.success ? countResult.data[0].total : 0;

    res.json({
      success: true,
      data: {
        enrollments: result.data,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get enrollments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch enrollments',
      error: error.message
    });
  }
});

// Auto-promote all students to new term (when term is activated)
router.post('/auto-promote-term', async (req, res) => {
  try {
    const { academic_year_id, new_term_id } = req.body;

    if (!academic_year_id || !new_term_id) {
      return res.status(400).json({
        success: false,
        message: 'Academic year ID and new term ID are required'
      });
    }

    console.log(`🔄 Auto-promoting all students to new term ${new_term_id} in academic year ${academic_year_id}`);

    let promotedCount = 0;
    let errors = [];

    // Update O-Level students
    const updateOLevelQuery = `
      UPDATE o_level_students 
      SET current_term_id = ?, updated_at = NOW()
      WHERE current_academic_year_id = ? AND status = 'active'
    `;

    const oLevelResult = await executeQuery(updateOLevelQuery, [new_term_id, academic_year_id]);
    
    if (oLevelResult.success) {
      promotedCount += oLevelResult.data.affectedRows || 0;
      console.log(`✅ Updated ${oLevelResult.data.affectedRows || 0} O-Level students`);
    } else {
      errors.push(`Failed to update O-Level students: ${oLevelResult.error}`);
    }

    // Update A-Level students
    const updateALevelQuery = `
      UPDATE a_level_students 
      SET current_term_id = ?, updated_at = NOW()
      WHERE current_academic_year_id = ? AND status = 'active'
    `;

    const aLevelResult = await executeQuery(updateALevelQuery, [new_term_id, academic_year_id]);
    
    if (aLevelResult.success) {
      promotedCount += aLevelResult.data.affectedRows || 0;
      console.log(`✅ Updated ${aLevelResult.data.affectedRows || 0} A-Level students`);
    } else {
      errors.push(`Failed to update A-Level students: ${aLevelResult.error}`);
    }

    console.log(`✅ Successfully promoted ${promotedCount} students to new term`);

    res.json({
      success: true,
      message: `Successfully promoted ${promotedCount} students to new term`,
      promoted_count: promotedCount,
      errors: errors.length > 0 ? errors : undefined
    });

  } catch (error) {
    console.error('Auto-promote term error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to auto-promote students to new term',
      error: error.message
    });
  }
});

// Update student stream assignment
router.put('/change-stream', async (req, res) => {
  try {
    const { student_id, student_type, new_stream_id } = req.body;

    if (!student_id || !student_type) {
      return res.status(400).json({
        success: false,
        message: 'Student ID and student type are required'
      });
    }

    const studentTable = student_type === 'o_level' ? 'o_level_students' : 'a_level_students';

    const updateQuery = `
      UPDATE ${studentTable}
      SET stream_id = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [new_stream_id, student_id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      message: 'Stream assignment updated successfully'
    });

  } catch (error) {
    console.error('Change stream error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to change stream assignment',
      error: error.message
    });
  }
});

// Bulk promote students to new class/academic year
router.post('/bulk-promote', async (req, res) => {
  try {
    const { promotions } = req.body;

    if (!promotions || !Array.isArray(promotions)) {
      return res.status(400).json({
        success: false,
        message: 'Promotions array is required'
      });
    }

    let successCount = 0;
    let errors = [];

    for (const promotion of promotions) {
      try {
        const {
          student_id,
          student_type,
          target_class_id,
          target_stream_id,
          target_academic_year_id,
          target_term_id
        } = promotion;

        const studentTable = student_type === 'o_level' ? 'o_level_students' : 'a_level_students';

        const updateQuery = `
          UPDATE ${studentTable}
          SET
            current_class_id = ?,
            stream_id = ?,
            current_academic_year_id = ?,
            current_term_id = ?,
            updated_at = NOW()
          WHERE id = ?
        `;

        const result = await executeQuery(updateQuery, [
          target_class_id,
          target_stream_id,
          target_academic_year_id,
          target_term_id,
          student_id
        ]);

        if (result.success) {
          successCount++;
        } else {
          errors.push(`Student ${student_id}: ${result.error}`);
        }

      } catch (error) {
        errors.push(`Student ${promotion.student_id}: ${error.message}`);
      }
    }

    res.json({
      success: true,
      message: `Successfully promoted ${successCount} students`,
      promoted_count: successCount,
      total_requested: promotions.length,
      errors: errors.length > 0 ? errors : undefined
    });

  } catch (error) {
    console.error('Bulk promote error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to bulk promote students',
      error: error.message
    });
  }
});

// Get students for a specific class (used by assessment components)
router.get('/class/:class_id/academic-students', async (req, res) => {
  try {
    const { class_id } = req.params;
    const { academic_year_id, term_id, stream_id, level, search } = req.query;

    console.log('Loading students for class:', class_id, 'with params:', { academic_year_id, term_id, stream_id, level, search });

    // For report generation, be more flexible - find students who have assessment data
    // for the specified academic context, regardless of their current enrollment status
    const queryOLevel = !level || level === 'o_level';
    const queryALevel = !level || level === 'a_level';

    let oLevelWhereClause = 'WHERE s.current_class_id = ?';
    let aLevelWhereClause = 'WHERE s.current_class_id = ?';

    // If academic year and term are specified, try to find students with assessment data
    // for that period, even if they're not currently enrolled in that term
    if (academic_year_id && term_id) {
      // For O-Level: Check if students have CA scores or exam marks for this academic context
      oLevelWhereClause = `
        WHERE s.current_class_id = ?
        AND s.status IN ("active", "suspended", "completed")
        AND (
          s.current_academic_year_id = ? AND s.current_term_id = ?
          OR EXISTS (
            SELECT 1 FROM o_level_subject_continuous_assessments_scores ca
            WHERE ca.student_id = s.id AND ca.academic_year_id = ? AND ca.term_id = ?
          )
          OR EXISTS (
            SELECT 1 FROM o_level_student_exam_marks em
            JOIN o_level_term_examinations te ON em.examination_id = te.id
            WHERE em.student_id = s.id AND te.academic_year_id = ? AND te.term_id = ?
          )
        )
      `;

      // For A-Level: Similar check for A-Level assessment data
      aLevelWhereClause = `
        WHERE s.current_class_id = ?
        AND s.status IN ("active", "suspended", "completed")
        AND (
          s.current_academic_year_id = ? AND s.current_term_id = ?
          OR EXISTS (
            SELECT 1 FROM a_level_paper_continuous_assessments_scores ca
            WHERE ca.student_id = s.id AND ca.academic_year_id = ? AND ca.term_id = ?
          )
          OR EXISTS (
            SELECT 1 FROM a_level_student_paper_exam_marks em
            JOIN a_level_paper_examinations te ON em.examination_id = te.id
            WHERE em.student_id = s.id AND te.academic_year_id = ? AND te.term_id = ?
          )
        )
      `;
    } else {
      // Fallback to current enrollment if no specific academic context
      if (academic_year_id) {
        oLevelWhereClause += ' AND s.current_academic_year_id = ?';
        aLevelWhereClause += ' AND s.current_academic_year_id = ?';
      }

      if (term_id) {
        oLevelWhereClause += ' AND s.current_term_id = ?';
        aLevelWhereClause += ' AND s.current_term_id = ?';
      }

      oLevelWhereClause += ' AND s.status IN ("active", "suspended")';
      aLevelWhereClause += ' AND s.status IN ("active", "suspended")';
    }

    if (stream_id) {
      oLevelWhereClause += ' AND s.stream_id = ?';
      aLevelWhereClause += ' AND s.stream_id = ?';
    }

    let queries = [];
    let finalParams = [];

    if (queryOLevel) {
      // O-Level students query
      const oLevelQuery = `
        SELECT
          s.id as student_id,
          'o_level' as student_type,
          s.stream_id,
          st.name as stream_name,
          CONCAT(s.first_name, ' ', COALESCE(s.middle_name, ''), ' ', s.last_name) as student_name,
          s.first_name,
          s.last_name,
          s.gender,
          s.admission_number
        FROM o_level_students s
        LEFT JOIN streams st ON s.stream_id = st.id
        ${oLevelWhereClause}
      `;
      queries.push(`(${oLevelQuery})`);

      // Add O-Level parameters
      let oLevelParams = [class_id];
      if (academic_year_id && term_id) {
        // Parameters for the flexible query with assessment data check
        oLevelParams.push(academic_year_id, term_id, academic_year_id, term_id, academic_year_id, term_id);
      } else {
        if (academic_year_id) oLevelParams.push(academic_year_id);
        if (term_id) oLevelParams.push(term_id);
      }
      if (stream_id) oLevelParams.push(stream_id);
      finalParams.push(...oLevelParams);
    }

    if (queryALevel) {
      // A-Level students query
      const aLevelQuery = `
        SELECT
          s.id as student_id,
          'a_level' as student_type,
          s.stream_id,
          st.name as stream_name,
          CONCAT(s.first_name, ' ', COALESCE(s.middle_name, ''), ' ', s.last_name) as student_name,
          s.first_name,
          s.last_name,
          s.gender,
          s.admission_number
        FROM a_level_students s
        LEFT JOIN streams st ON s.stream_id = st.id
        ${aLevelWhereClause}
      `;
      queries.push(`(${aLevelQuery})`);

      // Add A-Level parameters
      let aLevelParams = [class_id];
      if (academic_year_id && term_id) {
        // Parameters for the flexible query with assessment data check
        aLevelParams.push(academic_year_id, term_id, academic_year_id, term_id, academic_year_id, term_id);
      } else {
        if (academic_year_id) aLevelParams.push(academic_year_id);
        if (term_id) aLevelParams.push(term_id);
      }
      if (stream_id) aLevelParams.push(stream_id);
      finalParams.push(...aLevelParams);
    }

    if (queries.length === 0) {
      return res.json({
        success: true,
        data: []
      });
    }

    const finalQuery = `${queries.join(' UNION ALL ')} ORDER BY student_name`;

    console.log('Final query:', finalQuery);
    console.log('Final params:', finalParams);

    const result = await executeQuery(finalQuery, finalParams);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get class students error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch class students',
      error: error.message
    });
  }
});

// Get enrollment statistics
router.get('/statistics', async (req, res) => {
  try {
    const { academic_year_id, term_id } = req.query;

    let whereClause = '';
    let params = [];

    if (academic_year_id) {
      whereClause += ' WHERE current_academic_year_id = ?';
      params.push(academic_year_id);
    }

    if (term_id) {
      whereClause += whereClause ? ' AND current_term_id = ?' : ' WHERE current_term_id = ?';
      params.push(term_id);
    }

    // Get O-Level statistics
    const oLevelStatsQuery = `
      SELECT
        'o_level' as student_type,
        COUNT(*) as total_students,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_students,
        COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_students,
        COUNT(CASE WHEN status = 'transferred' THEN 1 END) as transferred_students,
        COUNT(CASE WHEN status = 'graduated' THEN 1 END) as graduated_students,
        COUNT(CASE WHEN status = 'dropped' THEN 1 END) as dropped_students
      FROM o_level_students${whereClause}
    `;

    // Get A-Level statistics
    const aLevelStatsQuery = `
      SELECT
        'a_level' as student_type,
        COUNT(*) as total_students,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_students,
        COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_students,
        COUNT(CASE WHEN status = 'transferred' THEN 1 END) as transferred_students,
        COUNT(CASE WHEN status = 'graduated' THEN 1 END) as graduated_students,
        COUNT(CASE WHEN status = 'dropped' THEN 1 END) as dropped_students
      FROM a_level_students${whereClause}
    `;

    const [oLevelResult, aLevelResult] = await Promise.all([
      executeQuery(oLevelStatsQuery, params),
      executeQuery(aLevelStatsQuery, params)
    ]);

    if (!oLevelResult.success || !aLevelResult.success) {
      throw new Error('Failed to fetch statistics');
    }

    res.json({
      success: true,
      data: {
        o_level: oLevelResult.data[0],
        a_level: aLevelResult.data[0]
      }
    });

  } catch (error) {
    console.error('Get statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch enrollment statistics',
      error: error.message
    });
  }
});

module.exports = router;
