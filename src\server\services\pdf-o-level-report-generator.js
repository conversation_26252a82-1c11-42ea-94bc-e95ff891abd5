const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs').promises;

class PDFOLevelReportGenerator {
  constructor() {
    this.browser = null;
  }

  // Initialize browser instance
  async initBrowser() {
    if (!this.browser) {
      try {
        console.log('Initializing browser for PDF generation...');
        this.browser = await puppeteer.launch({
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-web-security',
            '--allow-running-insecure-content',
            '--disable-features=VizDisplayCompositor'
          ]
        });
        console.log('Browser initialized successfully');
      } catch (error) {
        console.error('Failed to launch browser:', error);
        throw new Error(`PDF generation service unavailable: ${error.message}`);
      }
    }
    return this.browser;
  }

  // Close browser instance
  async closeBrowser() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  // Get Downloads directory path
  getDownloadsPath() {
    try {
      const { app } = require('electron');
      return app.getPath('downloads');
    } catch (error) {
      console.log('Electron not available, using fallback Downloads path');
      const os = require('os');
      return path.join(os.homedir(), 'Downloads');
    }
  }

  // Get the default school logo path
  getDefaultLogoPath() {
    return 'http://localhost:3001/assets/images/default-school-logo.png';
  }

  // Get school logo URL for PDF generation
  getSchoolLogoUrl(logoPath) {
    if (!logoPath) return this.getDefaultLogoPath();
    if (logoPath.startsWith('http://') || logoPath.startsWith('https://')) return logoPath;
    if (logoPath.startsWith('/')) return `http://localhost:3001${logoPath}`;
    return `http://localhost:3001/assets/images/uploads/school/${logoPath}`;
  }

  // Get student photo URL for PDF generation
  getStudentPhotoUrl(photoPath) {
    if (!photoPath) return null;
    if (photoPath.startsWith('http://') || photoPath.startsWith('https://')) return photoPath;
    if (photoPath.startsWith('/')) return `http://localhost:3001${photoPath}`;
    return `http://localhost:3001/assets/images/uploads/o-level-students/${photoPath}`;
  }

  // Create unique directory name to avoid conflicts
  async createUniqueDirectory(basePath, baseName) {
    let dirName = baseName;
    let counter = 1;
    let fullPath = path.join(basePath, dirName);

    while (true) {
      try {
        await fs.access(fullPath);
        dirName = `${baseName} (${counter})`;
        fullPath = path.join(basePath, dirName);
        counter++;
      } catch (error) {
        break;
      }
    }

    await fs.mkdir(fullPath, { recursive: true });
    return fullPath;
  }

  // Create unique file name to avoid conflicts
  async createUniqueFileName(dirPath, baseName, extension) {
    let fileName = `${baseName}.${extension}`;
    let counter = 1;
    let fullPath = path.join(dirPath, fileName);

    while (true) {
      try {
        await fs.access(fullPath);
        fileName = `${baseName} (${counter}).${extension}`;
        fullPath = path.join(dirPath, fileName);
        counter++;
      } catch (error) {
        break;
      }
    }

    return { fileName, fullPath };
  }

  // Generate O-Level report card PDF and save to Downloads
  async generateOLevelReportCard(reportData, academicContext) {
    console.log('🚀 Starting single PDF generation...');
    let page;

    try {
      const downloadsPath = this.getDownloadsPath();
      const browser = await this.initBrowser();
      page = await browser.newPage();
      await page.setViewport({ width: 1200, height: 1600 });

      const htmlContent = this.generateReportCardHTML(reportData);
      await page.setContent(htmlContent, {
        waitUntil: 'networkidle0',
        timeout: 30000
      });

      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: false,
        margin: {
          top: '0.3in',
          right: '0.3in',
          bottom: '0.3in',
          left: '0.3in'
        },
        displayHeaderFooter: false,
        preferCSSPageSize: true
      });

      const student = reportData.student;
      const fileName = `${student.first_name}_${student.last_name}_${academicContext.academic_year_name}_${academicContext.term_name}`;
      const { fullPath } = await this.createUniqueFileName(downloadsPath, fileName, 'pdf');
      await fs.writeFile(fullPath, pdfBuffer);

      console.log('✅ PDF saved successfully:', fullPath);
      return {
        success: true,
        filePath: fullPath,
        fileName: path.basename(fullPath)
      };

    } catch (error) {
      console.error('❌ PDF generation error:', error);
      console.error('❌ Error stack:', error.stack);
      throw new Error(`Failed to generate PDF report card: ${error.message}`);
    } finally {
      if (page) {
        await page.close();
      }
    }
  }

  // Generate multiple report cards and save to Downloads folder
  async generateBulkOLevelReportCards(reportsData, academicContext, shouldCreateFolder = true) {
    console.log('🚀 Starting bulk PDF generation for', reportsData.length, 'students...');
    let page;

    try {
      const downloadsPath = this.getDownloadsPath();
      const browser = await this.initBrowser();
      page = await browser.newPage();
      await page.setViewport({ width: 1200, height: 1600 });

      let folderPath = downloadsPath;
      let folderName = null;

      if (shouldCreateFolder) {
        folderName = `${academicContext.class_name} Report Cards ${academicContext.academic_year_name}_${academicContext.term_name}`;
        folderPath = await this.createUniqueDirectory(downloadsPath, folderName);
        console.log('📂 Created folder:', folderPath);
      }

      const savedFiles = [];
      for (let i = 0; i < reportsData.length; i++) {
        const reportData = reportsData[i];
        const student = reportData.student;

        console.log(`📄 Generating PDF ${i + 1}/${reportsData.length} for ${student.first_name} ${student.last_name}...`);

        try {
          const htmlContent = this.generateReportCardHTML(reportData);
          await page.setContent(htmlContent, {
            waitUntil: 'networkidle0',
            timeout: 30000
          });

          const pdfBuffer = await page.pdf({
            format: 'A4',
            printBackground: false,
            margin: {
              top: '0.3in',
              right: '0.3in',
              bottom: '0.3in',
              left: '0.3in'
            },
            displayHeaderFooter: false,
            preferCSSPageSize: true
          });

          const fileName = `${student.first_name}_${student.last_name}_Report_Card`;
          const { fullPath } = await this.createUniqueFileName(folderPath, fileName, 'pdf');
          await fs.writeFile(fullPath, pdfBuffer);

          savedFiles.push({
            studentName: `${student.first_name} ${student.last_name}`,
            filePath: fullPath,
            fileName: path.basename(fullPath)
          });

          console.log(`✅ Generated PDF for ${student.first_name} ${student.last_name}`);
        } catch (studentError) {
          console.error(`❌ Failed to generate PDF for ${student.first_name} ${student.last_name}:`, studentError.message);
        }
      }

      console.log(`🎉 Bulk PDF generation completed. Generated ${savedFiles.length}/${reportsData.length} files.`);

      return {
        success: true,
        folderPath: shouldCreateFolder ? folderPath : downloadsPath,
        folderName: shouldCreateFolder ? path.basename(folderPath) : null,
        filesGenerated: savedFiles.length,
        files: savedFiles
      };

    } catch (error) {
      console.error('❌ Bulk PDF generation error:', error);
      console.error('❌ Error stack:', error.stack);
      throw new Error(`Failed to generate bulk PDF report cards: ${error.message}`);
    } finally {
      if (page) {
        await page.close();
      }
    }
  }

  // Generate HTML content for a single report card
  generateReportCardHTML(reportData) {
    if (!reportData) {
      throw new Error('Report data is required');
    }

    const { student = {} } = reportData;

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Report Card - ${student.first_name || 'Student'} ${student.last_name || ''}</title>
        <style>
          ${this.getReportCardCSS()}
        </style>
      </head>
      <body>
        <div class="report-card">
          ${this.generateComprehensiveReportCardContent(reportData)}
        </div>
      </body>
      </html>
    `;
  }

  // Generate comprehensive report card content with fixed layout
  generateComprehensiveReportCardContent(reportData) {
    const {
      student = {},
      subjects = [],
      formativeAssessments = [],
      summativeAssessments = [],
      termAverages = [],
      classExamTypes = [],
      schoolSettings = {},
      academicContext = {},
      gradingScale = [],
      overallSummary = {}
    } = reportData || {};

    return `
      <!-- PAGE ONE -->
      <div class="page-one">
        <!-- School Name at Top of Page -->
        <div class="page-header">
          <h1 class="school-name-top">${schoolSettings?.school_name ? schoolSettings.school_name.toUpperCase() : 'SCHOOL NAME'}</h1>
        </div>

        <!-- HEADER SECTION -->
        <div class="header">
          <div class="header-grid">
            <!-- Left Column: School Logo -->
            <div class="header-logo">
              <img src="${this.getSchoolLogoUrl(schoolSettings?.school_logo)}" alt="School Logo" class="school-logo">
            </div>

            <!-- Middle Column: School Information -->
            <div class="header-middle">
              <p class="school-address">${schoolSettings?.school_address || 'School Address'}</p>
              <p class="school-contacts">
                Email: ${schoolSettings?.school_email || '<EMAIL>'}, Website: ${schoolSettings?.school_website || 'www.school.ac.ug'}
              </p>
              <p class="school-contacts">
                Tel: ${schoolSettings?.school_contacts || '+256 xxx xxxxxx'}
              </p>
            </div>

            <!-- Right Column: Student Photo -->
            <div class="header-photo">
              ${student.passport_photo ? `
                <img src="${this.getStudentPhotoUrl(student.passport_photo)}" alt="Student Photo" class="student-photo">
              ` : `
                <div class="photo-placeholder">[STUDENT PHOTO]</div>
              `}
            </div>
          </div>
        </div>

        <!-- DOCUMENT TITLE SECTION -->
        <div class="document-title-section">
          <hr class="title-separator">
          <div class="report-title">
            <h2>END OF ${(academicContext?.term_name || 'TERM').toUpperCase()} ACADEMIC REPORT CARD ${(academicContext?.academic_year_name || 'YEAR').toUpperCase()}</h2>
          </div>
          <hr class="title-separator">
        </div>

        <!-- STUDENT INFORMATION SECTION -->
        <div class="student-information-section">
          <div class="student-details">
            <div class="info-row">
              <span class="label">Student Name:</span>
              <span class="value">${student.first_name || '-'} ${student.last_name || '-'}</span>
              <span class="label">Admission Number:</span>
              <span class="value">${student.admission_number || '-'}</span>
            </div>
            <div class="info-row">
              <span class="label">Class:</span>
              <span class="value">${academicContext?.class_name || '-'}</span>
              <span class="label">Stream:</span>
              <span class="value">${academicContext?.stream_name || '-'}</span>
            </div>
          </div>
        </div>

        <!-- Comprehensive Assessment Table -->
        <div class="assessment-section">
          <table class="assessment-table">
            <thead>
              <tr class="main-header">
                <th rowspan="2" class="subject-header">SUBJECTS</th>
                <th colspan="8" class="formative-header">FORMATIVE ASSESSMENT</th>
                <th colspan="${this.getSummativeColumnsCount(classExamTypes)}" class="summative-header">SUMMATIVE ASSESSMENT</th>
                <th colspan="2" class="term-header">TERM AVERAGE</th>
                <th rowspan="2" class="initials-header">INITIALS</th>
              </tr>
              <tr class="sub-header">
                <!-- Formative Assessment Sub-headers -->
                <th>CA 1</th>
                <th>CA 2</th>
                <th>CA 3</th>
                <th>CA 4</th>
                <th>CA 5</th>
                <th>CA 6</th>
                <th>AVE</th>
                <th>Total<br>(Out of 20%)</th>

                <!-- Summative Assessment Sub-headers -->
                ${this.renderSummativeHeaders(classExamTypes)}

                <!-- Term Average Sub-headers -->
                <th>Total Mark<br>(20%) + (80%)</th>
                <th>Grade</th>
              </tr>
            </thead>
            <tbody>
              ${this.renderSubjectRows(subjects, formativeAssessments, summativeAssessments, termAverages, classExamTypes)}

              <!-- Totals Row -->
              <tr class="totals-row">
                <td class="totals-label">TOTAL</td>
                ${this.renderTotalsRow(overallSummary, classExamTypes)}
              </tr>

              <!-- Averages Row -->
              <tr class="totals-row">
                <td class="totals-label">AVERAGE</td>
                ${this.renderAveragesRow(overallSummary, classExamTypes)}
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Student Competency Level -->
        <div class="student-competency-section">
          <table class="student-competency-table">
            <thead>
              <tr>
                <th>IDENTIFIER</th>
                <th>DESCRIPTOR</th>
              </tr>
            </thead>
            <tbody>
              ${this.renderStudentCompetencyRow(formativeAssessments, subjects, reportData.gradingScale)}
            </tbody>
          </table>
        </div>

        <!-- Comments Section -->
        <div class="comments-section">
          <div class="comment-signature-row">
            <span class="comment-label">Class Teacher's Comment:</span>
            <span class="comment-dots">................................................................</span>
            <span class="signature-label">Signature:</span>
            <span class="signature-dots">................................</span>
          </div>

          <div class="comment-signature-row">
            <span class="comment-label">Head Teacher's Comment:</span>
            <span class="comment-dots">................................................................</span>
            <span class="signature-label">Signature:</span>
            <span class="signature-dots">................................</span>
          </div>
        </div>

        <!-- Administrative Information -->
        <div class="admin-section">
          <div class="term-dates-row">
            <span class="term-date-left">Next Term Begins on: ${reportData.nextTerm ? new Date(reportData.nextTerm.start_date).toLocaleDateString() : 'TBD'}</span>
            <span class="term-date-right">Next Term Ends on: ${reportData.nextTerm ? new Date(reportData.nextTerm.end_date).toLocaleDateString() : 'TBD'}</span>
          </div>
        </div>

        <!-- School Motto Footer -->
        ${schoolSettings?.school_motto ? `
          <div class="school-motto-footer">
            <p>"${schoolSettings.school_motto.charAt(0).toUpperCase() + schoolSettings.school_motto.slice(1).toLowerCase()}"</p>
          </div>
        ` : ''}

        <!-- Document Footer -->
        <div class="document-footer">
          <span class="date-printed">Date Printed: ${new Date().toLocaleDateString()}</span>
        </div>
      </div>

      <!-- PAGE BREAK -->
      <div class="page-break"></div>

      <!-- PAGE TWO: COMPETENCY DESCRIPTORS AND GRADING SCALE ONLY -->
      <div class="page-two">
        <div class="competency-section">
          <h3>COMPETENCY DESCRIPTORS</h3>
          <table class="competency-table">
            <thead>
              <tr>
                <th>IDENTIFIER</th>
                <th>SCORE RANGE</th>
                <th>DESCRIPTOR</th>
              </tr>
            </thead>
            <tbody>
              ${this.renderCompetencyTable(reportData.gradingScale)}
            </tbody>
          </table>
        </div>

        <div class="grading-scale-section">
          <h3>GRADING SCALE</h3>
          <table class="grading-scale-table">
            <thead>
              <tr>
                <th>GRADE</th>
                <th>RANGE</th>
                <th>DESCRIPTOR</th>
              </tr>
            </thead>
            <tbody>
              ${this.renderGradingScaleTable(reportData.gradeBoundaries)}
            </tbody>
          </table>
        </div>
      </div>
    `;
  }

  // Helper methods for table rendering
  getSummativeColumnsCount(classExamTypes) {
    return classExamTypes ? classExamTypes.length + 1 : 3;
  }

  renderSummativeHeaders(classExamTypes) {
    if (!classExamTypes || classExamTypes.length === 0) {
      return `<th>No Exams<br>Configured</th>`;
    }

    let headers = '';
    classExamTypes.forEach(examType => {
      headers += `<th>${examType.short_name}<br>${examType.weight_percentage}%</th>`;
    });
    headers += `<th>Total<br>(Out of 80%)</th>`;
    return headers;
  }

  renderSubjectRows(subjects, formativeAssessments, summativeAssessments, termAverages, classExamTypes) {
    if (!subjects || subjects.length === 0) return '';

    return subjects.map(subject => {
      const formative = formativeAssessments.find(fa => fa.subject_id === subject.id) || {};
      const summative = summativeAssessments.find(sa => sa.subject_id === subject.id) || {};
      const termAvg = termAverages.find(ta => ta.subject_id === subject.id) || {};

      return `
        <tr class="subject-row">
          <td class="subject-name">${subject.name || '-'}</td>
          <td class="ca-score">${formative.ca1 !== null && formative.ca1 !== undefined ? Number(formative.ca1).toFixed(1) : '-'}</td>
          <td class="ca-score">${formative.ca2 !== null && formative.ca2 !== undefined ? Number(formative.ca2).toFixed(1) : '-'}</td>
          <td class="ca-score">${formative.ca3 !== null && formative.ca3 !== undefined ? Number(formative.ca3).toFixed(1) : '-'}</td>
          <td class="ca-score">${formative.ca4 !== null && formative.ca4 !== undefined ? Number(formative.ca4).toFixed(1) : '-'}</td>
          <td class="ca-score">${formative.ca5 !== null && formative.ca5 !== undefined ? Number(formative.ca5).toFixed(1) : '-'}</td>
          <td class="ca-score">${formative.ca6 !== null && formative.ca6 !== undefined ? Number(formative.ca6).toFixed(1) : '-'}</td>
          <td class="ca-average">${formative.average !== null && formative.average !== undefined ? Number(formative.average).toFixed(1) : '-'}</td>
          <td class="ca-points">${formative.totalPoints !== null && formative.totalPoints !== undefined ? Math.round(Number(formative.totalPoints)) : '-'}</td>
          ${this.renderSummativeColumns(summative, classExamTypes)}
          <td class="total-mark">${termAvg.totalMark !== null && termAvg.totalMark !== undefined ? Math.round(Number(termAvg.totalMark)) : '-'}</td>
          <td class="grade">${termAvg.grade || '-'}</td>
          <td class="teacher-initials">${subject.teacher_initials || '-'}</td>
        </tr>
      `;
    }).join('');
  }

  renderSummativeColumns(summative, classExamTypes) {
    let columns = '';
    let totalWeightedScore = 0;
    let hasValidScores = false;

    if (summative.examScores && summative.examScores.length > 0 && classExamTypes && classExamTypes.length > 0) {
      summative.examScores.forEach((score, index) => {
        if (index < classExamTypes.length) {
          const examType = classExamTypes[index];
          if (score !== null && score !== undefined) {
            const weightedScore = Math.round((Number(score) * (examType.weight_percentage || 0)) / 100);
            totalWeightedScore += weightedScore;
            hasValidScores = true;
            columns += `<td class="exam-score">${weightedScore}</td>`;
          } else {
            columns += `<td class="exam-score">-</td>`;
          }
        }
      });
    } else if (classExamTypes && classExamTypes.length > 0) {
      classExamTypes.forEach(() => {
        columns += `<td class="exam-score">-</td>`;
      });
    } else {
      columns += `<td class="exam-score">-</td>`;
    }

    // Calculate Total (Out of 80%) as (sum of weighted scores / 100) × 80
    const summativeTotal = hasValidScores ? Math.round((totalWeightedScore / 100) * 80) : '-';
    columns += `<td class="summative-total">${summativeTotal}</td>`;
    return columns;
  }

  renderTotalsRow(overallSummary, classExamTypes) {
    const examColumnsCount = classExamTypes ? classExamTypes.length : 2;

    // Merged CA1-CA6 columns (6 columns merged into 1)
    let formativeTotals = '<td colspan="6" class="merged-cell"></td>';

    // AVE and Total (Out of 20%) columns (with borders)
    formativeTotals += '<td class="total-cell"></td>'; // AVE
    formativeTotals += '<td class="total-cell"></td>'; // Total (Out of 20%)

    // Merged exam type columns
    let summativeTotals = '';
    if (examColumnsCount > 0) {
      summativeTotals += `<td colspan="${examColumnsCount}" class="merged-cell"></td>`;
    }

    // Total (Out of 80%) column (with border)
    summativeTotals += '<td class="total-cell"></td>';

    return `
      ${formativeTotals}
      ${summativeTotals}
      <td class="overall-total">${overallSummary.totalMarks !== null && overallSummary.totalMarks !== undefined ? Math.round(Number(overallSummary.totalMarks)) : ''}</td>
      <td colspan="2" class="merged-cell"></td>
    `;
  }

  renderAveragesRow(overallSummary, classExamTypes) {
    const examColumnsCount = classExamTypes ? classExamTypes.length : 2;

    // Merged CA1-CA6 columns (6 columns merged into 1)
    let formativeAverages = '<td colspan="6" class="merged-cell"></td>';

    // AVE and Total (Out of 20%) columns (with borders)
    formativeAverages += '<td class="average-cell"></td>'; // AVE
    formativeAverages += '<td class="average-cell"></td>'; // Total (Out of 20%)

    // Merged exam type columns
    let summativeAverages = '';
    if (examColumnsCount > 0) {
      summativeAverages += `<td colspan="${examColumnsCount}" class="merged-cell"></td>`;
    }

    // Total (Out of 80%) column (with border)
    summativeAverages += '<td class="average-cell"></td>';

    return `
      ${formativeAverages}
      ${summativeAverages}
      <td class="overall-average">${overallSummary.average !== null && overallSummary.average !== undefined ? Math.round(Number(overallSummary.average)) + '%' : ''}</td>
      <td colspan="2" class="merged-cell"></td>
    `;
  }

  renderGradingScaleTable(gradeBoundaries) {
    if (!gradeBoundaries || gradeBoundaries.length === 0) {
      return `<tr><td colspan="3">No grading scale configured</td></tr>`;
    }

    return gradeBoundaries.map(boundary => `
      <tr>
        <td>${boundary.grade_letter || '-'}</td>
        <td>${boundary.min_percentage || 0}-${boundary.max_percentage || 0}%</td>
        <td>${boundary.grade_descriptor || '-'}</td>
      </tr>
    `).join('');
  }

  renderCompetencyTable(gradingScale) {
    if (!gradingScale || gradingScale.length === 0) {
      return `<tr><td colspan="3">No competency descriptors configured</td></tr>`;
    }

    return gradingScale.map(scale => `
      <tr>
        <td>${scale.competency_level || '-'}</td>
        <td>${scale.min_score || 0} - ${scale.max_score || 0}</td>
        <td>${scale.competency_description || '-'}</td>
      </tr>
    `).join('');
  }

  renderStudentCompetencyRow(formativeAssessments, subjects, gradingScale) {
    if (!formativeAssessments || !subjects || formativeAssessments.length === 0 || subjects.length === 0) {
      return `<tr><td colspan="2">No assessment data available</td></tr>`;
    }

    let totalCAAverage = 0;
    let subjectsWithData = 0;

    subjects.forEach(subject => {
      const subjectAssessments = formativeAssessments.filter(fa => fa.subject_id === subject.id);
      if (subjectAssessments.length > 0) {
        const subjectAverage = subjectAssessments.reduce((sum, assessment) => sum + (assessment.average_score || 0), 0) / subjectAssessments.length;
        totalCAAverage += subjectAverage;
        subjectsWithData++;
      }
    });

    const overallCAAverage = subjectsWithData > 0 ? totalCAAverage / subjectsWithData : 0;

    let matchingCompetency = null;
    if (gradingScale && gradingScale.length > 0) {
      for (const scale of gradingScale) {
        if (overallCAAverage >= (scale.min_score || 0) && overallCAAverage <= (scale.max_score || 0)) {
          matchingCompetency = scale;
          break;
        }
      }
    }

    if (!matchingCompetency) {
      return `<tr><td colspan="2">No matching competency level found</td></tr>`;
    }

    return `
      <tr>
        <td>${matchingCompetency.competency_level || '-'}</td>
        <td>${matchingCompetency.competency_description || '-'}</td>
      </tr>
    `;
  }

  // Generate CSS styles for the comprehensive report card
  getReportCardCSS() {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Bookman Old Style', 'Times New Roman', serif;
        font-size: 14px;
        line-height: 1.3;
        color: #000;
        background: white;
      }

      .report-card {
        width: 210mm;
        height: 297mm;
        margin: 0 auto;
        padding: 5mm 10mm;
        background: white;
        position: relative;
      }

      /* PAGE ONE STYLES */
      .page-one {
        height: 277mm;
        display: flex;
        flex-direction: column;
      }

      /* PAGE HEADER STYLES */
      .page-header {
        text-align: center;
        margin-bottom: 5px;
      }

      .school-name-top {
        font-size: 24px;
        font-weight: bold;
        color: #000;
        text-transform: uppercase;
        margin: 0;
        letter-spacing: 1px;
        padding: 5px 0;
      }

      /* HEADER SECTION STYLES */
      .header {
        margin-bottom: 10px;
      }

      .header-grid {
        display: grid;
        grid-template-columns: 1fr 2fr 1fr;
        gap: 10px;
        align-items: center;
        padding: 5px 0;
      }

      .school-logo {
        width: 80px;
        height: 80px;
        object-fit: contain;
      }

      .school-address {
        font-size: 14px;
        margin-bottom: 5px;
        color: #000;
        font-weight: 500;
      }

      .school-contacts {
        font-size: 12px;
        color: #000;
        margin-bottom: 3px;
      }

      .student-photo {
        width: 80px;
        height: 100px;
        object-fit: cover;
        filter: grayscale(100%);
      }

      .photo-placeholder {
        width: 80px;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        color: #000;
        background-color: #fff;
        font-weight: bold;
      }

      /* DOCUMENT TITLE SECTION STYLES */
      .document-title-section {
        margin: 5px 0 10px 0;
      }

      .title-separator {
        border: none;
        border-top: 2px solid #000;
        margin: 5px 0;
      }

      .report-title {
        text-align: center;
        margin: 5px 0;
      }

      .report-title h2 {
        font-size: 16px;
        font-weight: bold;
        color: #000;
        text-transform: uppercase;
        margin: 0;
        letter-spacing: 1px;
        padding: 3px 0;
      }

      /* STUDENT INFORMATION SECTION STYLES */
      .student-information-section {
        margin-bottom: 10px;
        background-color: #fff;
        padding: 8px;
      }

      .info-row {
        display: grid;
        grid-template-columns: auto 1fr auto 1fr;
        gap: 10px;
        margin-bottom: 8px;
        font-size: 13px;
        align-items: center;
      }

      .info-row .label {
        font-weight: bold;
        color: #000;
      }

      .info-row .value {
        color: #000;
        font-weight: 500;
      }

      /* Assessment Table Styles */
      .assessment-section {
        margin-bottom: 10px;
        flex-grow: 1;
      }

      .assessment-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 11px;
        margin-bottom: 10px;
      }

      .assessment-table th,
      .assessment-table td {
        border: 1px solid #000;
        padding: 5px 3px;
        text-align: center;
        vertical-align: middle;
        color: #000;
        background-color: #fff;
      }

      .main-header th {
        font-weight: bold;
        font-size: 10px;
        color: #000;
      }

      .sub-header th {
        font-weight: bold;
        font-size: 9px;
        color: #000;
      }

      .subject-header {
        width: 100px;
      }

      .subject-row td {
        font-size: 10px;
      }

      .subject-name {
        text-align: left !important;
        font-weight: 500;
        padding-left: 5px !important;
      }

      .ca-score,
      .exam-score {
        font-size: 10px;
      }

      .ca-average,
      .ca-points,
      .summative-total,
      .total-mark,
      .grade {
        font-weight: 600;
      }

      .teacher-initials {
        font-weight: 500;
        font-size: 10px;
      }

      .totals-row {
        font-weight: bold;
        color: #000;
      }

      .totals-label {
        text-align: center !important;
        font-weight: bold;
        color: #000;
      }

      .total-cell-no-border,
      .average-cell-no-border {
        border: none !important;
        background-color: #fff;
      }

      .merged-cell {
        border: 1px solid #000;
        background-color: #fff;
        text-align: center;
        vertical-align: middle;
      }

      .total-cell,
      .average-cell {
        border: 1px solid #000;
        background-color: #fff;
        text-align: center;
        vertical-align: middle;
      }

      /* Student Competency Section Styles */
      .student-competency-section {
        margin-bottom: 8px;
      }

      .student-competency-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 8px;
      }

      .student-competency-table th,
      .student-competency-table td {
        border: 2px solid #000;
        padding: 8px;
        text-align: center;
        vertical-align: middle;
        color: #000;
        background-color: #fff;
        font-size: 12px;
      }

      .student-competency-table th {
        font-weight: bold;
        font-size: 13px;
      }

      .student-competency-table td:nth-child(2) {
        text-align: left;
        padding-left: 10px;
      }

      /* Comments Section Styles */
      .comments-section {
        margin-bottom: 8px;
      }

      .comment-signature-row {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;
        padding: 3px 0;
      }

      .comment-label {
        font-weight: 600;
        color: #000;
        margin-right: 10px;
        white-space: nowrap;
      }

      .comment-dots {
        flex: 1;
        color: #000;
        margin-right: 10px;
      }

      .signature-label {
        font-weight: 600;
        color: #000;
        margin-right: 10px;
        white-space: nowrap;
      }

      .signature-dots {
        color: #000;
        white-space: nowrap;
      }

      /* Administrative Section Styles */
      .admin-section {
        margin-bottom: 8px;
      }

      .term-dates-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #000;
        font-weight: 600;
        padding: 5px 0;
      }

      /* School Motto Footer */
      .school-motto-footer {
        text-align: center;
        margin-top: 5px;
        padding-top: 5px;
        border-top: 1px solid #000;
      }

      .school-motto-footer p {
        font-size: 14px;
        font-style: italic;
        color: #000;
        margin: 0;
      }

      /* Document Footer */
      .document-footer {
        margin-top: 5px;
        text-align: right;
      }

      .date-printed {
        font-size: 10px;
        color: #000;
        font-weight: 600;
      }

      /* Page Break Styles */
      .page-break {
        page-break-before: always;
        break-before: page;
      }

      /* PAGE TWO STYLES */
      .page-two {
        padding-top: 10px;
        height: 277mm;
      }

      .competency-section {
        margin-bottom: 20px;
      }

      .competency-section h3,
      .grading-scale-section h3 {
        font-size: 14px;
        font-weight: bold;
        color: #000;
        margin-bottom: 10px;
        text-align: center;
        text-transform: uppercase;
      }

      .competency-table,
      .grading-scale-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 15px;
      }

      .competency-table th,
      .competency-table td,
      .grading-scale-table th,
      .grading-scale-table td {
        border: 1px solid #000;
        padding: 8px;
        text-align: left;
        font-size: 12px;
        color: #000;
      }

      .competency-table th,
      .grading-scale-table th {
        font-weight: bold;
        font-size: 13px;
        text-align: center;
      }

      .competency-table td:first-child {
        text-align: center;
        font-weight: bold;
        width: 80px;
      }

      .competency-table td:nth-child(2) {
        text-align: center;
        width: 120px;
      }

      /* Print Styles */
      @media print {
        .report-card {
          margin: 0;
          padding: 0;
        }

        body {
          font-size: 12px;
        }

        .assessment-table {
          font-size: 10px;
        }

        .subject-row td {
          font-size: 9px;
        }
      }
    `;
  }
}

module.exports = PDFOLevelReportGenerator;