const fs = require('fs').promises;
const path = require('path');
const mysql = require('mysql2/promise');
const { pool, testConnection } = require('./connection');

// Create database if it doesn't exist
async function createDatabase() {
  try {
    const dbName = process.env.DB_NAME || 'smartreport_db';
    console.log(`🗄️ Creating database '${dbName}' if it doesn't exist...`);

    // Connect without specifying database
    const connectionConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      charset: 'utf8mb4'
    };

    console.log(`🔌 Connecting to MySQL server at ${connectionConfig.host}:${connectionConfig.port}...`);
    const connection = await mysql.createConnection(connectionConfig);

    // Create database
    const createDbQuery = `CREATE DATABASE IF NOT EXISTS \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`;
    console.log(`📊 Executing: ${createDbQuery}`);
    await connection.query(createDbQuery);

    console.log(`✅ Database '${dbName}' created successfully`);
    await connection.end();

  } catch (error) {
    console.error('❌ Failed to create database:', error.message);
    console.error('💡 Make sure MariaDB/MySQL is running and credentials are correct');
    throw error;
  }
}

// Drop database (for testing/reset purposes)
async function dropDatabase() {
  try {
    const dbName = process.env.DB_NAME || 'smartreport_db';
    console.log(`🗑️ Dropping database '${dbName}' if it exists...`);

    const connectionConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      charset: 'utf8mb4'
    };

    const connection = await mysql.createConnection(connectionConfig);
    await connection.query(`DROP DATABASE IF EXISTS \`${dbName}\``);
    console.log(`✅ Database '${dbName}' dropped successfully`);
    await connection.end();

  } catch (error) {
    console.error('❌ Failed to drop database:', error.message);
    throw error;
  }
}

// Initialize database with schema and default data
async function initializeDatabase() {
  try {
    console.log('🚀 Starting database initialization...');

    // Create database first
    await createDatabase();

    // Test connection to the created database
    const isConnected = await testConnection();
    if (!isConnected) {
      throw new Error('Database connection failed');
    }

    // Read and execute schema
    await executeSchemaFile();

    // Verify critical tables were created
    await verifyTables();

    // Insert default subjects
    await insertDefaultSubjects();

    // Create default admin user
    await createDefaultAdmin();

    // Initialize academic year and terms (production ready)
    await initializeAcademicStructure();

    // Initialize system structure (classes, streams) on first startup
    await initializeSystemStructure();

    console.log('✅ Database initialization completed successfully!');
    return { success: true, message: 'Database initialized successfully' };

  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    return { success: false, error: error.message };
  }
}

// Execute schema file
async function executeSchemaFile() {
  try {
    console.log('📄 Executing database schema...');

    const schemaPath = path.join(__dirname, 'schema.sql');
    const schemaSQL = await fs.readFile(schemaPath, 'utf8');

    // Get a connection from the pool
    const connection = await pool.getConnection();

    try {
      // Remove triggers and stored procedures for initial setup (they can be added later via admin interface)
      // This simplifies the initial database setup and avoids complex delimiter handling
      let cleanSQL = schemaSQL;

      // Remove trigger and procedure sections (from DELIMITER // to DELIMITER ;)
      cleanSQL = cleanSQL.replace(/DELIMITER \/\/[\s\S]*?DELIMITER ;/g, '');

      // Remove comments more carefully
      cleanSQL = cleanSQL.replace(/\/\*[\s\S]*?\*\//g, ''); // Remove block comments
      // Remove line comments but preserve the line structure
      cleanSQL = cleanSQL.replace(/--.*$/gm, ''); // Remove line comments

      // Split SQL into statements, properly handling triggers and procedures
      const statements = [];
      let currentStatement = '';
      let inTrigger = false;
      let delimiterStack = [];

      const lines = cleanSQL.split('\n');

      for (const line of lines) {
        const trimmedLine = line.trim();

        // Skip empty lines
        if (!trimmedLine) continue;

        // Handle DELIMITER changes
        if (trimmedLine.startsWith('DELIMITER')) {
          const newDelimiter = trimmedLine.split(' ')[1];
          if (newDelimiter === '$$') {
            delimiterStack.push('$$');
          } else if (newDelimiter === ';') {
            delimiterStack.pop();
          }
          continue;
        }

        // Check if we're entering a trigger or procedure
        if (trimmedLine.includes('CREATE TRIGGER') || trimmedLine.includes('CREATE PROCEDURE')) {
          inTrigger = true;
          currentStatement = line;
          continue;
        }

        // If we're in a trigger/procedure, accumulate lines until we hit the end delimiter
        if (inTrigger) {
          currentStatement += '\n' + line;

          // Check for end of trigger/procedure
          if (delimiterStack.length > 0 && trimmedLine.endsWith('$$')) {
            // Complete trigger/procedure found, add it as a single statement
            statements.push(currentStatement);
            currentStatement = '';
            inTrigger = false;
          }
          continue;
        }

        // Regular statement handling
        currentStatement += (currentStatement ? '\n' : '') + line;

        // Check for statement end
        if (trimmedLine.endsWith(';')) {
          const stmt = currentStatement.trim();
          if (stmt && !stmt.startsWith('DELIMITER')) {
            statements.push(stmt);
          }
          currentStatement = '';
        }
      }

      // Add any remaining statement
      if (currentStatement.trim()) {
        const stmt = currentStatement.trim();
        if (!stmt.startsWith('DELIMITER')) {
          statements.push(stmt);
        }
      }

      // Filter out empty statements
      const filteredStatements = statements.filter(stmt => {
        const trimmed = stmt.trim();
        return trimmed.length > 0 && !trimmed.startsWith('DELIMITER');
      });

      console.log(`📊 Executing ${filteredStatements.length} SQL statements...`);

      for (let i = 0; i < filteredStatements.length; i++) {
        const statement = filteredStatements[i].trim();
        if (statement) {
          try {
            // Clean up the statement - remove $$ delimiters for programmatic execution
            let cleanStatement = statement;
            if (statement.toLowerCase().includes('create trigger')) {
              // Remove $$ delimiter from the end if present
              cleanStatement = statement.replace(/\$\$\s*$/, '');

              const triggerName = statement.match(/create trigger\s+(\w+)/i)?.[1];
              await connection.query(cleanStatement);
              console.log(`✅ Created trigger: ${triggerName}`);
            } else {
              const result = await connection.query(cleanStatement);
              if (statement.toLowerCase().includes('create table')) {
                const tableName = statement.match(/create table(?:\s+if not exists)?\s+(\w+)/i)?.[1];
                console.log(`✅ Created table: ${tableName}`);
              } else if (statement.toLowerCase().includes('insert')) {
                const tableName = statement.match(/insert\s+(?:ignore\s+)?into\s+(\w+)/i)?.[1];
                const affectedRows = result[0]?.affectedRows || 0;
                console.log(`✅ Inserted data into: ${tableName} (${affectedRows} rows)`);
              }
            }
          } catch (statementError) {
            console.error(`❌ Error executing statement ${i + 1}:`, statementError.message);
            console.error(`Statement: ${statement.substring(0, 100)}...`);

            // Skip user_sessions table for now if it has timestamp issues
            if (statement.toLowerCase().includes('user_sessions')) {
              console.log('⚠️ Skipping user_sessions table due to timestamp issues');
              continue;
            }

            throw statementError;
          }
        }
      }

      console.log('✅ Schema executed successfully');
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('❌ Schema execution failed:', error);
    throw error;
  }
}

// Verify that critical tables were created
async function verifyTables() {
  try {
    console.log('🔍 Verifying database tables...');

    const criticalTables = [
      'system_users', 'school_settings', 'education_levels', 'class_levels',
      'o_level_subjects', 'a_level_subjects', 'a_level_subject_papers', 'academic_years', 'terms',
      'streams', 'classes', 'teachers', 'teacher_subjects',
      'o_level_students', 'a_level_students',
      'o_level_grade_boundaries', 'a_level_paper_grade_boundaries',
      'o_level_grading_scale', 'o_level_subject_continuous_assessments_scores',
      'a_level_paper_continuous_assessments_scores', 'o_level_term_examinations',
      'a_level_paper_examinations', 'o_level_ca_configuration', 'a_level_ca_configuration',
      'o_level_subject_ca_weights', 'a_level_paper_ca_weights',
      'o_level_subject_exam_weights', 'a_level_paper_exam_weights'
    ];

    for (const tableName of criticalTables) {
      const [tables] = await pool.execute(`SHOW TABLES LIKE '${tableName}'`);
      if (tables.length === 0) {
        console.error(`❌ Critical table '${tableName}' was not created`);
        throw new Error(`Critical table '${tableName}' was not created during schema execution`);
      } else {
        console.log(`✅ Table '${tableName}' verified`);
      }
    }

    console.log('✅ All critical tables verified');
  } catch (error) {
    console.error('❌ Table verification failed:', error);
    throw error;
  }
}



// Verify default subjects were inserted via schema
async function insertDefaultSubjects() {
  try {
    console.log('📚 Verifying default subjects were inserted...');

    // Check O-Level subjects
    const [oLevelSubjects] = await pool.execute('SELECT COUNT(*) as count FROM o_level_subjects');
    const [aLevelSubjects] = await pool.execute('SELECT COUNT(*) as count FROM a_level_subjects');

    console.log(`✅ Found ${oLevelSubjects[0].count} O-Level subjects`);
    console.log(`✅ Found ${aLevelSubjects[0].count} A-Level subjects`);

    if (oLevelSubjects[0].count === 0 || aLevelSubjects[0].count === 0) {
      console.log('⚠️ Some subjects are missing. This should have been handled by the schema.');
    } else {
      console.log('✅ Default subjects verification completed');
    }

  } catch (error) {
    console.error('❌ Failed to insert default subjects:', error);
    throw error;
  }
}


// Create default admin user
async function createDefaultAdmin() {
  try {
    console.log('👤 Creating default admin user...');

    // Check if admin already exists
    const [existingAdmin] = await pool.execute(
      'SELECT id FROM system_users WHERE role = "system_admin" LIMIT 1'
    );

    if (existingAdmin.length > 0) {
      console.log('ℹ️ Admin user already exists, skipping creation');
      return;
    }

    // Create default admin with specific hash
    const hashedPassword = '$2b$10$cDybGDbPesavR6qRabZty.ARDwQGJ5pf1UqSyvmc32Oc19Ci9feaK';
    
    const query = `
      INSERT INTO system_users (
        username, email, password, first_name, last_name, gender, role,
        is_active, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `;

    await pool.execute(query, [
      'admin',
      '<EMAIL>',
      hashedPassword,
      'John',
      'Doe',
      'Male',
      'super_user',
      true
    ]);

    console.log('✅ Default admin user created successfully');
    console.log('📝 Login credentials: username=admin, password=admin123');
    console.log('📧 Email: <EMAIL>');
    console.log('👑 Role: Super User (can manage other administrators)');
    console.log('⚠️ Please change the default password after first login!');

  } catch (error) {
    console.error('❌ Failed to create default admin:', error);
    throw error;
  }
}

// Initialize academic structure (production ready)
async function initializeAcademicStructure() {
  try {
    console.log('📅 Initializing academic structure...');

    // Check if any academic year exists
    const [existingYears] = await pool.execute('SELECT COUNT(*) as count FROM academic_years');

    if (existingYears[0].count > 0) {
      console.log('ℹ️ Academic years already exist, skipping initialization');
      return;
    }

    console.log('ℹ️ No academic years found. System is ready for admin to create academic years and terms.');
    console.log('💡 Admin should use the Academic Setup page to create the first academic year and terms.');

  } catch (error) {
    console.error('❌ Failed to initialize academic structure:', error);
    throw error;
  }
}

// Initialize system classes and streams on first startup
async function initializeSystemStructure() {
  try {
    console.log('🏗️ Initializing system structure (classes, streams, levels)...');

    // Check if classes and streams already exist
    const [classCount] = await pool.execute('SELECT COUNT(*) as count FROM classes');
    const [streamCount] = await pool.execute('SELECT COUNT(*) as count FROM streams WHERE stream_type = "a_level"');

    if (classCount[0].count > 0 || streamCount[0].count > 0) {
      console.log('ℹ️ Classes and streams already exist, skipping initialization');
      return;
    }

    console.log('🏗️ Creating default classes and streams for first-time setup...');

    // Create permanent A-Level streams (only once)
    await pool.execute(`
      INSERT IGNORE INTO streams (name, stream_type)
      VALUES ('Sciences', 'a_level')
    `);

    await pool.execute(`
      INSERT IGNORE INTO streams (name, stream_type)
      VALUES ('Arts', 'a_level')
    `);

    // Get the stream IDs for A-Level streams
    const [sciencesStream] = await pool.execute(`
      SELECT id FROM streams WHERE name = 'Sciences' AND stream_type = 'a_level' LIMIT 1
    `);
    const [artsStream] = await pool.execute(`
      SELECT id FROM streams WHERE name = 'Arts' AND stream_type = 'a_level' LIMIT 1
    `);

    const sciencesStreamId = sciencesStream[0]?.id;
    const artsStreamId = artsStream[0]?.id;

    if (!sciencesStreamId || !artsStreamId) {
      throw new Error('Failed to create A-Level streams');
    }

    // Get the current academic year ID
    const [academicYearResult] = await pool.execute(`
      SELECT id FROM academic_years WHERE is_active = TRUE LIMIT 1
    `);

    if (academicYearResult.length === 0) {
      throw new Error('No active academic year found. Please ensure an academic year is set as active.');
    }

    const academicYearId = academicYearResult[0].id;

    // Link Sciences stream to S.5 and S.6 class levels
    await pool.execute(`
      INSERT IGNORE INTO stream_classes (stream_id, class_level_id, academic_year_id, is_active)
      SELECT ?, cl.id, ?, TRUE
      FROM class_levels cl
      WHERE cl.code IN ('s5', 's6')
    `, [sciencesStreamId, academicYearId]);

    // Link Arts stream to S.5 and S.6 class levels
    await pool.execute(`
      INSERT IGNORE INTO stream_classes (stream_id, class_level_id, academic_year_id, is_active)
      SELECT ?, cl.id, ?, TRUE
      FROM class_levels cl
      WHERE cl.code IN ('s5', 's6')
    `, [artsStreamId, academicYearId]);

    // Create permanent O-Level base classes (S.1 to S.4) without streams
    await pool.execute(`
      INSERT IGNORE INTO classes (name, class_level_id, stream_id, is_active)
      SELECT cl.name, cl.id, NULL, TRUE
      FROM class_levels cl
      WHERE cl.code IN ('s1', 's2', 's3', 's4')
      ORDER BY cl.sort_order
    `);

    // Create A-Level base classes (without stream specification in name)
    // The streams will be used for subject assignment and student enrollment
    await pool.execute(`
      INSERT IGNORE INTO classes (name, class_level_id, stream_id, is_active)
      SELECT cl.name, cl.id, NULL, TRUE
      FROM class_levels cl
      WHERE cl.code IN ('s5', 's6')
      ORDER BY cl.sort_order
    `);

    console.log('✅ Default classes and streams created successfully');
    console.log('📊 Created 6 permanent classes:');
    console.log('   - O-Level: S.1, S.2, S.3, S.4 (4 base classes)');
    console.log('   - A-Level: S.5, S.6 (2 base classes with Arts/Sciences stream divisions)');

  } catch (error) {
    console.error('❌ Failed to initialize system structure:', error);
    throw error;
  }
}



// Check if database is initialized
async function isDatabaseInitialized() {
  try {
    const [tables] = await pool.execute("SHOW TABLES LIKE 'system_users'");
    return tables.length > 0;
  } catch (error) {
    console.warn('⚠️ Could not check database initialization status:', error.message);
    return false;
  }
}

// Drop database (for reset operations)
async function dropDatabase() {
  try {
    const dbName = process.env.DB_NAME || 'smartreport_db';
    console.log(`🗑️ Dropping database '${dbName}'...`);

    // Connect without specifying database
    const connectionConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      charset: 'utf8mb4'
    };

    const connection = await mysql.createConnection(connectionConfig);
    await connection.query(`DROP DATABASE IF EXISTS \`${dbName}\``);
    await connection.end();

    console.log(`✅ Database '${dbName}' dropped successfully`);
  } catch (error) {
    console.error('❌ Failed to drop database:', error.message);
    throw error;
  }
}

// Reset and initialize database (drops existing, then creates fresh)
async function resetAndInitialize() {
  try {
    console.log('🔄 Starting database reset and initialization...');
    console.log('⚠️  WARNING: This will completely delete the existing database!');

    // Step 1: Drop existing database
    console.log('\n📋 Step 1: Dropping existing database...');
    await dropDatabase();
    console.log('✅ Database dropped successfully');

    // Step 2: Initialize fresh database
    console.log('\n📋 Step 2: Creating and initializing fresh database...');
    const result = await initializeDatabase();

    if (result.success) {
      console.log('\n🎉 Database reset and initialization completed successfully!');
      return result;
    } else {
      console.log('\n❌ Database initialization failed!');
      return result;
    }

  } catch (error) {
    console.error('\n💥 Fatal error during database reset:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  initializeDatabase,
  initializeSystemStructure,
  isDatabaseInitialized,
  dropDatabase,
  resetAndInitialize
};
