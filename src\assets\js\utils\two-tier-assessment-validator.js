// Two-Tier Assessment System Validation Utilities
// Validates the CA (20%) + Summative Assessment (80%) system

const TwoTierAssessmentValidator = {
  
  // Validate exam weights for a class
  validateClassExamWeights(examWeights) {
    if (!examWeights || !Array.isArray(examWeights) || examWeights.length === 0) {
      return {
        isValid: false,
        message: 'No exam weights provided for validation',
        details: []
      };
    }

    const details = [];
    let totalWeight = 0;
    let hasInvalidWeights = false;

    // Validate individual weights
    examWeights.forEach((weight, index) => {
      const weightValue = parseFloat(weight);
      
      if (isNaN(weightValue)) {
        hasInvalidWeights = true;
        details.push(`Exam ${index + 1}: Invalid weight value "${weight}"`);
      } else if (weightValue < 1 || weightValue > 100) {
        hasInvalidWeights = true;
        details.push(`Exam ${index + 1}: Weight must be between 1-100%, got ${weightValue}%`);
      } else if (!Number.isInteger(weightValue)) {
        hasInvalidWeights = true;
        details.push(`Exam ${index + 1}: Weight must be an integer, got ${weightValue}%`);
      } else {
        totalWeight += weightValue;
        details.push(`Exam ${index + 1}: ${weightValue}% ✓`);
      }
    });

    // Validate total weight
    const totalWeightValid = totalWeight === 100;
    if (!totalWeightValid && !hasInvalidWeights) {
      details.push(`Total weight: ${totalWeight}% (should be 100%)`);
    }

    return {
      isValid: !hasInvalidWeights && totalWeightValid,
      message: hasInvalidWeights 
        ? 'Invalid weight values detected'
        : totalWeightValid 
          ? 'Exam weights are valid for two-tier assessment system'
          : `Total weight is ${totalWeight}% but must be 100% for summative assessment portion`,
      totalWeight: totalWeight,
      details: details
    };
  },

  // Calculate final grade using two-tier system
  calculateFinalGrade(caScore, examScores, examWeights) {
    // Validate inputs
    if (caScore === null || caScore === undefined) {
      return { error: 'CA score is required' };
    }

    if (!examScores || !examWeights || examScores.length !== examWeights.length) {
      return { error: 'Exam scores and weights must be provided and have same length' };
    }

    // Validate CA score (0.0 for absent, 0.9-3.0 scale with one decimal place)
    const roundedCAScore = Math.round(caScore * 10) / 10;
    if (roundedCAScore !== 0.0 && (roundedCAScore < 0.9 || roundedCAScore > 3.0)) {
      return { error: 'CA score must be 0.0 (absent) or between 0.9 and 3.0' };
    }

    // Check decimal precision
    if (caScore.toString().includes('.') && caScore.toString().split('.')[1].length > 1) {
      return { error: 'CA score must have at most one decimal place' };
    }

    // Validate exam weights
    const weightValidation = this.validateClassExamWeights(examWeights);
    if (!weightValidation.isValid) {
      return { error: weightValidation.message };
    }

    // Note: This validator only validates inputs - actual calculations are done by database triggers
    // Return validation success with input summary for display purposes only
    const examContributions = [];

    for (let i = 0; i < examScores.length; i++) {
      const score = parseFloat(examScores[i]);
      const weight = parseFloat(examWeights[i]);

      if (isNaN(score) || score < 0 || score > 100) {
        return { error: `Invalid exam score: ${examScores[i]}. Must be between 0-100` };
      }

      examContributions.push({
        score: score,
        weight: weight,
        display: `${score}% (${weight}% weight)`
      });
    }

    return {
      success: true,
      caScore: caScore,
      examContributions: examContributions,
      message: 'Input validation successful. Final grades will be calculated automatically by the system.',
      breakdown: {
        formative: `CA: ${caScore}/3.0 (20% weight)`,
        summative: `${examContributions.length} exams (80% weight)`,
        note: 'Final calculation handled by database triggers'
      }
    };
  },

  // Validate assessment configuration for a class
  validateClassAssessmentConfig(classConfig) {
    const errors = [];
    const warnings = [];

    // Check if class has exam types configured
    if (!classConfig.examTypes || classConfig.examTypes.length === 0) {
      errors.push('No exam types configured for this class');
      return { isValid: false, errors, warnings };
    }

    // Validate exam weights
    const weights = classConfig.examTypes.map(et => et.weight_percentage);
    const weightValidation = this.validateClassExamWeights(weights);
    
    if (!weightValidation.isValid) {
      errors.push(weightValidation.message);
    }

    // Check for minimum exam types (at least 1)
    if (classConfig.examTypes.length < 1) {
      warnings.push('Consider having at least 2 exam types for better assessment');
    }

    // Check for maximum exam types (practical limit)
    if (classConfig.examTypes.length > 5) {
      warnings.push('Having more than 5 exam types may be difficult to manage');
    }

    return {
      isValid: errors.length === 0,
      errors: errors,
      warnings: warnings,
      weightValidation: weightValidation
    };
  },

  // Generate assessment breakdown report
  generateAssessmentBreakdown(caScore, examScores, examWeights, examNames = []) {
    const calculation = this.calculateFinalGrade(caScore, examScores, examWeights);

    if (calculation.error) {
      return { error: calculation.error };
    }

    const breakdown = {
      system: 'Two-Tier Assessment System',
      components: [
        {
          name: 'Continuous Assessment (CA)',
          weight: '20%',
          score: `${calculation.caScore}/3`,
          percentage: `${calculation.caPercentage}%`,
          contribution: `${calculation.caContribution}%`
        },
        {
          name: 'Summative Assessment (All Exams)',
          weight: '80%',
          totalContribution: `${calculation.summativeContribution}%`,
          exams: calculation.examContributions.map((exam, index) => ({
            name: examNames[index] || `Exam ${index + 1}`,
            score: `${exam.score}%`,
            weight: `${exam.weight}%`,
            contribution: `${exam.finalContribution.toFixed(2)}%`
          }))
        }
      ],
      finalScore: `${calculation.finalScore}%`,
      formula: 'Final Score = CA (20%) + (Exam1×weight1 + Exam2×weight2 + ...) × 0.8'
    };

    return { success: true, breakdown };
  },

  // Calculate grade letter and points for A-Level subjects
  calculateALevelGrade(finalScore, isSubsidiary = false) {
    if (finalScore === null || finalScore === undefined) {
      return { error: 'Final score is required' };
    }

    if (finalScore < 0 || finalScore > 100) {
      return { error: 'Final score must be between 0 and 100' };
    }

    // Use the grade boundaries component for calculation
    if (window.GradeBoundariesComponents && window.GradeBoundariesComponents.getGradeLetterForPercentage) {
      const gradeLetter = window.GradeBoundariesComponents.getGradeLetterForPercentage(finalScore, isSubsidiary);

      if (gradeLetter && gradeLetter !== 'N/A') {
        let points = 0;
        let gradeDescriptor = '';

        if (isSubsidiary) {
          points = window.GradeBoundariesComponents.calculateALevelSubsidiaryPoints(finalScore);
          gradeDescriptor = gradeLetter === 'Pass' ? 'Pass' : 'Fail';
        } else {
          points = window.GradeBoundariesComponents.calculateALevelPrincipalPoints(gradeLetter);
          // Get descriptor from boundaries
          const boundaries = window.GradeBoundariesComponents.state.aLevelPrincipalBoundaries?.data;
          if (boundaries) {
            const boundary = boundaries.find(b => b.grade_letter === gradeLetter);
            gradeDescriptor = boundary ? boundary.grade_descriptor : '';
          }
        }

        return {
          success: true,
          finalScore: finalScore,
          gradeLetter: gradeLetter,
          gradeDescriptor: gradeDescriptor,
          points: points,
          subjectType: isSubsidiary ? 'Subsidiary' : 'Principal'
        };
      }
    }

    return { error: 'Grade boundaries not available or configured properly' };
  },

  // Calculate complete A-Level assessment with grade
  calculateCompleteALevelAssessment(caScore, examScores, examWeights, examNames = [], isSubsidiary = false) {
    // First calculate the final score using two-tier system
    const scoreCalculation = this.calculateFinalGrade(caScore, examScores, examWeights);

    if (scoreCalculation.error) {
      return { error: scoreCalculation.error };
    }

    // Then calculate the grade and points
    const gradeCalculation = this.calculateALevelGrade(scoreCalculation.finalScore, isSubsidiary);

    if (gradeCalculation.error) {
      return { error: gradeCalculation.error };
    }

    // Combine both calculations
    return {
      success: true,
      assessment: {
        caScore: scoreCalculation.caScore,
        caPercentage: scoreCalculation.caPercentage,
        caContribution: scoreCalculation.caContribution,
        summativeContribution: scoreCalculation.summativeContribution,
        finalScore: scoreCalculation.finalScore,
        gradeLetter: gradeCalculation.gradeLetter,
        gradeDescriptor: gradeCalculation.gradeDescriptor,
        points: gradeCalculation.points,
        subjectType: gradeCalculation.subjectType,
        examContributions: scoreCalculation.examContributions
      },
      breakdown: this.generateAssessmentBreakdown(caScore, examScores, examWeights, examNames).breakdown
    };
  }
};

// Export to global scope
window.TwoTierAssessmentValidator = TwoTierAssessmentValidator;
