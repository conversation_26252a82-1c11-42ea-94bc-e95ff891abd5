// SmartReport - Teacher Management Components
// Comprehensive teacher management system with registration and management

// Uses global API services: window.TeachersAPI, window.SubjectsAPI, window.AcademicYearsAPI
// Uses global config: window.SRConfig
// Uses environment configuration: ../config/environment.js

const TeacherManagementComponents = {
  // Component state
  state: {
    teachers: [],
    subjects: [],
    academicYears: [],
    loading: false,
    filters: {
      teacherType: '',
      employmentStatus: '',
      search: ''
    }
  },

  // Initialize component
  async init() {
    await this.loadInitialData();
  },

  // Load initial data
  async loadInitialData() {
    try {
      this.state.loading = true;



      // Use the API services with proper error handling
      const [teachers, oLevelSubjects, aLevelSubjects, academicYears, classes] = await Promise.all([
        window.TeachersAPI.getAll(),
        window.SubjectsAPI.oLevel.getAll(),
        window.SubjectsAPI.aLevel.getAll(),
        window.AcademicYearsAPI.getAll(),
        window.ClassesAPI.getAll()
      ]);

      // Store teachers data properly
      this.state.teachers = teachers;
      // Combine O-Level and A-Level subjects
      const allSubjects = [
        ...(oLevelSubjects.data || []).map(s => ({ ...s, level: 'o_level' })),
        ...(aLevelSubjects.data || []).map(s => ({ ...s, level: 'a_level' }))
      ];
      this.state.subjects = { success: true, data: allSubjects };
      this.state.academicYears = academicYears;
      this.state.classes = classes;



    } catch (error) {
      console.error('❌ Failed to load initial data:', error);

      // Show error notification if available
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to load data', 'error');
      }
    } finally {
      this.state.loading = false;
    }
  }
};

// Register Teacher Component
const RegisterTeacherComponent = {
  // Render register teacher form
  render() {
    // Check if academic context is available
    const hasAcademicContext = window.AcademicContext &&
                              window.AcademicContext.getActiveAcademicYear() &&
                              window.AcademicContext.getActiveTerm();

    if (!hasAcademicContext) {
      return this.renderAcademicContextError();
    }

    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Teacher Registration',
          'Add Class Teacher or Subject teachers to the system'
        )}
        <!-- Registration Form -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <form id="register-teacher-form" class="${SRDesignSystem.responsive.spacing.padding} space-y-8">
            <!-- Hidden fields for auto-populated values -->
            <input type="hidden" id="employment_status" name="employment_status" value="active">

            <!-- Personal Information Section -->
            <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900 mb-6">Personal Information</h3>

              <!-- Passport Photo - First row (full width) -->
              <div class="mb-4">
                <div class="form-field">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Passport Photo
                  </label>
                  <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center space-x-4">
                      <div class="flex-shrink-0">
                        <img id="passport-photo-preview"
                             class="h-20 w-20 rounded-full object-cover border-2 border-gray-200"
                             src="${window.SR.serverUrl}/assets/images/default-avatar.png"
                             alt="Teacher Photo"
                             onerror="this.src='${window.SR.serverUrl}/assets/images/default-avatar.png'; this.onerror=null;">
                      </div>
                      <div class="flex-1">
                        <input type="file" id="passport_photo" name="passport_photo" accept="image/*"
                               onchange="RegisterTeacherComponent.previewPhoto(this)"
                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                        <p class="mt-1 text-xs text-gray-500">Upload teacher profile picture (JPG, PNG - Max 2MB)</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Names - Second row (3 columns) -->
              <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap} mb-4">
                ${SRDesignSystem.forms.input('first_name', 'First Name', '', {
                  required: true,
                  placeholder: 'Enter first name'
                })}
                ${SRDesignSystem.forms.input('middle_name', 'Middle Name', '', {
                  placeholder: 'Enter middle name (optional)'
                })}
                ${SRDesignSystem.forms.input('last_name', 'Last Name', '', {
                  required: true,
                  placeholder: 'Enter last name'
                })}
              </div>

              <!-- Gender and Initials - Third row (2 columns) -->
              <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                ${SRDesignSystem.forms.select('gender', 'Gender', [
                  { value: '', label: 'Select Gender' },
                  { value: 'Male', label: 'Male' },
                  { value: 'Female', label: 'Female' }
                ], '', { required: true })}
                ${SRDesignSystem.forms.input('initials', 'Initials', '', {
                  placeholder: 'Auto-generated',
                  readonly: true,
                  style: 'background-color: #f9fafb; cursor: not-allowed;'
                })}
              </div>
            </div>

            <!-- Professional Information Section -->
            <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900 mb-6">Professional Information</h3>

              <!-- Teacher Type and Joining Date Row (Dynamic Layout) -->
              <div id="teacher-info-row" class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap} mb-4">
                <div class="col-span-1">
                  ${SRDesignSystem.forms.select('teacher_type', 'Teacher Type', [
                    { value: '', label: 'Select Teacher Type' },
                    { value: 'Class Teacher', label: 'Class Teacher' },
                    { value: 'Subject Teacher', label: 'Subject Teacher' }
                  ], '', {
                    required: true
                  })}
                </div>
                <div class="col-span-1" id="class-assignment-dropdown" style="display: none;">
                  <!-- Class dropdown will be populated here when Class Teacher is selected -->
                </div>
                <div class="col-span-1">
                  ${SRDesignSystem.forms.input('joining_date', 'Joining Date', '', {
                    type: 'date',
                    required: true,
                  })}
                </div>
              </div>
            </div>

            <!-- Subject Assignments Section -->
            <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900 mb-6">Subject Assignments</h3>

              <div class="bg-white border border-gray-200 rounded-lg p-4 mb-6">
                <h4 class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-900 mb-2">Subject Assignment Guidelines</h4>
                <div class="mt-2 ${SRDesignSystem.responsive.text.sm} text-gray-600">
                  <ul class="list-disc list-inside space-y-1">
                    <li>Select subjects the teacher is qualified to teach</li>
                    <li>Both O-Level and A-Level subjects can be assigned</li>
                    <li>Subject assignments can be modified later</li>
                  </ul>
                </div>
              </div>

              <div id="subject-assignments-container">
                <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap}">
                  <!-- Subject checkboxes will be populated here -->
                </div>
              </div>
            </div>



            <!-- Form Actions -->
            <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
              <div class="flex items-center justify-end">
                <div class="flex justify-end">
                  ${SRDesignSystem.forms.button('register', 'Register Teacher', 'primary', {
                    type: 'submit',
                    loading: false,
                    icon: 'fas fa-chalkboard-teacher'
                  })}
                </div>
              </div>
            </div>
          </form>
        </div>

      </div>
    `;
  },

  // Render academic context error
  renderAcademicContextError() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Teacher Registration',
          'Register new teachers'
        )}

        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-600')}
            </div>
            <div class="ml-4">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-red-900">Academic Context Required</h3>
              <p class="text-red-700 mt-2">
                You cannot register teachers without setting up the academic year and terms first.
              </p>
              <p class="text-red-700 mt-1">
                Please go to <strong>Academic Year → Setup</strong> to configure your academic year and terms before registering teachers.
              </p>
              <div class="mt-4 text-center">
                ${SRDesignSystem.forms.button('setup-academic-teacher', 'Setup Academic Year & Terms', 'danger', {
                  icon: 'fas fa-calendar-plus',
                  onclick: 'RegisterTeacherComponent.navigateToAcademicSetup()'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize register teacher component
  async init() {


    // Reset component state
    this.resetComponentState();

    // Check academic context first
    await window.AcademicContext.initialize();
    const activeYear = window.AcademicContext.getActiveAcademicYear();
    const activeTerm = window.AcademicContext.getActiveTerm();

    if (!activeYear || !activeTerm) {

      return;
    }

    // Load initial data first
    await TeacherManagementComponents.loadInitialData();

    // Initialize functionality after DOM is ready
    // Note: Page router handles rendering, we just initialize functionality
    setTimeout(() => {
      this.populateSubjects();
      this.initializeEventListeners();

    }, 100);
  },

  // Reset component state
  resetComponentState() {
    // Reset any component-specific state if needed

  },

  // Cleanup component
  cleanup() {

    this.resetComponentState();

    // Reset form if it exists
    const form = document.getElementById('register-teacher-form');
    if (form) {
      form.reset();
    }


  },





  // Populate subjects for assignment
  populateSubjects() {
    const container = document.getElementById('subject-assignments-container');
    if (!container) return;

    const subjects = TeacherManagementComponents.state.subjects.data || [];

    // Group subjects by level
    const oLevelSubjects = subjects.filter(s => s.level === 'o_level');
    const aLevelSubjects = subjects.filter(s => s.level === 'a_level');

    container.innerHTML = `
      <div class="space-y-6">
        <!-- O-Level Subjects -->
        ${oLevelSubjects.length > 0 ? `
          <div>
            <h4 class="text-md font-medium text-gray-900 mb-3">O-Level Subjects</h4>
            <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gapSm}">
              ${oLevelSubjects.map(subject => `
                <div class="flex items-center space-x-3 ${SRDesignSystem.responsive.spacing.paddingSm} border border-gray-200 rounded-lg hover:bg-gray-50">
                  <input type="checkbox"
                         id="subject_${subject.id}_${subject.level}"
                         name="subject_assignments[]"
                         value="${subject.id}:${subject.level}"
                         data-subject-id="${subject.id}"
                         data-subject-level="${subject.level}"
                         class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                  <label for="subject_${subject.id}_${subject.level}" class="flex-1 cursor-pointer">
                    <div class="font-medium text-gray-900 ${SRDesignSystem.responsive.text.sm}">${subject.name}</div>
                    <div class="text-xs text-gray-500">${subject.short_name}</div>
                  </label>
                </div>
              `).join('')}
            </div>
          </div>
        ` : ''}

        <!-- A-Level Subjects -->
        ${aLevelSubjects.length > 0 ? `
          <div>
            <h4 class="text-md font-medium text-gray-900 mb-3">A-Level Subjects</h4>
            <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gapSm}">
              ${aLevelSubjects.map(subject => `
                <div class="flex items-center space-x-3 ${SRDesignSystem.responsive.spacing.paddingSm} border border-gray-200 rounded-lg hover:bg-gray-50">
                  <input type="checkbox"
                         id="subject_${subject.id}_${subject.level}"
                         name="subject_assignments[]"
                         value="${subject.id}:${subject.level}"
                         data-subject-id="${subject.id}"
                         data-subject-level="${subject.level}"
                         class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                  <label for="subject_${subject.id}_${subject.level}" class="flex-1 cursor-pointer">
                    <div class="font-medium text-gray-900 ${SRDesignSystem.responsive.text.sm}">${subject.name}</div>
                    <div class="text-xs text-gray-500">${subject.short_name}${subject.subject_type ? ` (${subject.subject_type})` : ''}</div>
                  </label>
                </div>
              `).join('')}
            </div>
          </div>
        ` : ''}
      </div>
    `;
  },

  // Handle teacher type change
  handleTeacherTypeChange(teacherType) {
    const classDropdownContainer = document.getElementById('class-assignment-dropdown');
    const teacherInfoRow = document.getElementById('teacher-info-row');

    if (teacherType === 'Class Teacher') {
      // Show class dropdown and change to 3-column layout
      if (classDropdownContainer) {
        classDropdownContainer.style.display = 'block';
        this.populateClasses();
      }
      if (teacherInfoRow) {
        // Change to 3-column layout: Teacher Type | Class | Joining Date
        teacherInfoRow.className = `${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap} mb-4`;
      }
    } else {
      // Hide class dropdown and change to 2-column layout
      if (classDropdownContainer) {
        classDropdownContainer.style.display = 'none';
        // Clear the dropdown content when hidden
        classDropdownContainer.innerHTML = '';
      }
      if (teacherInfoRow) {
        // Change to 2-column layout: Teacher Type | Joining Date
        teacherInfoRow.className = `${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap} mb-4`;
      }
    }
  },

  // Populate classes for assignment
  populateClasses() {
    const container = document.getElementById('class-assignment-dropdown');
    if (!container) return;

    const classes = TeacherManagementComponents.state.classes.data || [];

    // Filter out classes that already have a Class Teacher assigned
    const availableClasses = classes.filter(cls => cls.is_active);

    const classOptions = [
      { value: '', label: 'Select a Class' },
      ...availableClasses.map(cls => ({
        value: cls.id,
        label: cls.name + (cls.stream_name ? ` (${cls.stream_name})` : '')
      }))
    ];

    container.innerHTML = SRDesignSystem.forms.select('assigned_class_id', 'Assigned Class', classOptions, '', {
      required: true,
    });
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Handle form submission
    const form = document.getElementById('register-teacher-form');
    if (form) {
      form.addEventListener('submit', this.handleSubmit.bind(this));
    }

    // Handle teacher type change
    const teacherTypeSelect = document.getElementById('teacher_type');
    if (teacherTypeSelect) {
      teacherTypeSelect.addEventListener('change', (e) => {
        this.handleTeacherTypeChange(e.target.value);
      });
    }

    // Auto-generate initials when name fields change
    this.setupInitialsGeneration();

    // Auto-uppercase and remove spaces from name fields
    this.setupNameFieldFormatting();
  },

  // Setup automatic initials generation
  setupInitialsGeneration() {
    const firstNameField = document.getElementById('first_name');
    const middleNameField = document.getElementById('middle_name');
    const lastNameField = document.getElementById('last_name');
    const initialsField = document.getElementById('initials');

    const generateInitials = () => {
      const firstName = (firstNameField?.value || '').trim();
      const middleName = (middleNameField?.value || '').trim();
      const lastName = (lastNameField?.value || '').trim();

      let initials = '';
      if (firstName) initials += firstName.charAt(0).toUpperCase() + '.';
      if (middleName) initials += middleName.charAt(0).toUpperCase() + '.';
      if (lastName) initials += lastName.charAt(0).toUpperCase() + '.';

      if (initialsField) {
        initialsField.value = initials;
      }
    };

    // Make initials field truly readonly - prevent all input
    if (initialsField) {
      // Prevent typing, pasting, and any input
      initialsField.addEventListener('keydown', (e) => {
        e.preventDefault();
      });

      initialsField.addEventListener('keypress', (e) => {
        e.preventDefault();
      });

      initialsField.addEventListener('input', (e) => {
        e.preventDefault();
        generateInitials(); // Regenerate from names if somehow changed
      });

      initialsField.addEventListener('paste', (e) => {
        e.preventDefault();
      });

      // Set readonly attribute and styling
      initialsField.setAttribute('readonly', true);
      initialsField.style.backgroundColor = '#f9fafb';
      initialsField.style.cursor = 'not-allowed';
    }

    // Add event listeners to name fields
    [firstNameField, middleNameField, lastNameField].forEach(field => {
      if (field) {
        field.addEventListener('input', generateInitials);
        field.addEventListener('blur', generateInitials);
      }
    });
  },

  // Setup name field formatting (uppercase, letters only)
  setupNameFieldFormatting() {
    const nameFields = ['first_name', 'middle_name', 'last_name'];

    nameFields.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (field) {
        // Prevent non-letter characters from being typed
        field.addEventListener('keydown', (e) => {
          // Allow navigation and control keys
          const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];

          // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z
          if ((e.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(e.key.toLowerCase())) ||
              allowedKeys.includes(e.key)) {
            return;
          }

          // Prevent spaces and only allow letters (a-z, A-Z)
          if (e.key === ' ' || !/^[a-zA-Z]$/.test(e.key)) {
            e.preventDefault();
          }
        });

        // Convert to uppercase and remove any non-letter characters including spaces
        field.addEventListener('input', (e) => {
          let value = e.target.value.toUpperCase().replace(/[^A-Z]/g, '');
          e.target.value = value;
        });

        // Handle paste events - only allow letters, no spaces
        field.addEventListener('paste', (e) => {
          e.preventDefault();
          let paste = '';
          if (e.clipboardData) {
            paste = e.clipboardData.getData('text');
          }
          paste = paste.toUpperCase().replace(/[^A-Z]/g, '');
          e.target.value = paste;
          // Trigger input event to update initials
          e.target.dispatchEvent(new Event('input'));
        });
      }
    });
  },

  // Preview passport photo
  previewPhoto(input) {
    if (input.files && input.files[0]) {
      const file = input.files[0];

      // Validate file size (2MB max)
      if (file.size > 2 * 1024 * 1024) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('File size must be less than 2MB', 'error');
        } else {
          alert('File size must be less than 2MB');
        }
        input.value = '';
        return;
      }

      // Validate file type (only JPG and PNG)
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select a JPG or PNG image file', 'error');
        } else {
          alert('Please select a JPG or PNG image file');
        }
        input.value = '';
        return;
      }

      const reader = new FileReader();
      reader.onload = function(e) {
        const preview = document.getElementById('passport-photo-preview');
        if (preview) {
          preview.src = e.target.result;
        }
      };
      reader.readAsDataURL(file);
    }
  },

  // Handle form submission
  async handleSubmit(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    let data = Object.fromEntries(formData.entries());

    // Handle passport photo upload if provided
    const passportPhotoFile = formData.get('passport_photo');
    if (passportPhotoFile && passportPhotoFile.size > 0) {
      try {
        // Check if ImageUploadUtil is available
        if (!window.ImageUploadUtil) {
          throw new Error('ImageUploadUtil is not available. Please ensure the image upload utility is loaded.');
        }

        SRDesignSystem.forms.setButtonLoading('register', true);

        // Upload the photo first
        const uploadResult = await window.ImageUploadUtil.uploadImage(
          passportPhotoFile,
          'teacher-photo'
        );

        if (uploadResult.success) {
          data.passport_photo = uploadResult.filePath;
        } else {
          throw new Error('Failed to upload passport photo');
        }
      } catch (uploadError) {
        console.error('Photo upload error:', uploadError);
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(
            'Failed to upload passport photo: ' + uploadError.message,
            'error'
          );
        }
        SRDesignSystem.forms.setButtonLoading('register', false);
        return;
      }
    }

    // Format name fields (uppercase, letters only)
    if (data.first_name) data.first_name = data.first_name.toUpperCase().replace(/[^A-Z]/g, '');
    if (data.middle_name) data.middle_name = data.middle_name.toUpperCase().replace(/[^A-Z]/g, '');
    if (data.last_name) data.last_name = data.last_name.toUpperCase().replace(/[^A-Z]/g, '');

    // Generate initials from names
    let initials = '';
    if (data.first_name) initials += data.first_name.charAt(0) + '.';
    if (data.middle_name) initials += data.middle_name.charAt(0) + '.';
    if (data.last_name) initials += data.last_name.charAt(0) + '.';
    data.initials = initials;

    // Collect selected subject assignments
    const subjectAssignments = Array.from(document.querySelectorAll('input[name="subject_assignments[]"]:checked'))
      .map(input => ({
        subject_id: parseInt(input.dataset.subjectId),
        subject_level: input.dataset.subjectLevel
      }));

    // Prepare subject assignments for API
    data.subject_assignments = subjectAssignments;

    // Handle class assignment for Class Teachers
    const classAssignmentId = data.assigned_class_id;
    if (data.teacher_type === 'Class Teacher' && classAssignmentId) {
      data.assigned_class_id = parseInt(classAssignmentId);
    } else {
      delete data.assigned_class_id;
    }

    try {
      SRDesignSystem.forms.setButtonLoading('register', true);

      // Use the API service
      const result = await window.TeachersAPI.create(data);

      if (result.success) {
        // Show success notification if available
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(
            `Teacher registered successfully with ${subjectAssignments.length} teaching subject(s)!`,
            'success'
          );
        }
        event.target.reset();
        // Optionally redirect to manage teachers
        setTimeout(() => {
          if (window.PageRouter) {
            window.PageRouter.loadPage('manage-teachers');
          }
        }, 1500);
      } else {
        // Show error notification if available
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to register teacher', 'error');
        }
      }
    } catch (error) {
      console.error('Registration error:', error);

      // Handle academic context validation errors
      let errorMessage = 'Failed to register teacher';
      if (error.message && error.message.includes('Academic context not set')) {
        errorMessage = 'Academic context not set. Please set up an active academic year and term before registering teachers.';
      } else if (error.message && error.message.includes('setupRequired')) {
        errorMessage = 'Academic setup required. Please configure academic year and term settings first.';
      }

      // Show error notification if available
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show(errorMessage, 'error');
      }
    } finally {
      SRDesignSystem.forms.setButtonLoading('register', false);
    }
  },



  // Navigate to academic year setup
  navigateToAcademicSetup() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('academic-year-setup');
    }
  }
};

// Manage Teachers Component
const ManageTeachersComponent = {
  // Render manage teachers interface
  render() {
    // Check if academic context is available
    const hasAcademicContext = window.AcademicContext &&
                              window.AcademicContext.getActiveAcademicYear() &&
                              window.AcademicContext.getActiveTerm();

    if (!hasAcademicContext) {
      return this.renderAcademicContextError();
    }

    // Show loading state if data is still loading
    if (TeacherManagementComponents.state.loading) {
      // Use centralized loading state from page router
      const container = document.getElementById('content-area');
      if (container && window.PageRouter) {
        window.PageRouter.showLoadingState(container, 'Loading teachers data...');
      }
      return '';
    }

    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Teacher Management',
          'View, edit, and manage teachers records and assignments',
        )}

        <!-- Teacher Management Actions -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
          <div class="flex items-center justify-end mb-6">
            <div class="flex items-center space-x-3">
              ${SRDesignSystem.forms.button('add-teacher', 'Register Teacher', 'primary', {
                icon: 'fas fa-plus',
                onclick: 'ManageTeachersComponent.addTeacher()'
              })}
              ${SRDesignSystem.forms.button('export', 'Export', 'secondary', {
                icon: 'fas fa-download',
                onclick: 'ManageTeachersComponent.exportTeachers()'
              })}
            </div>
          </div>

          <!-- Filters and Search -->
          <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap}">
            ${SRDesignSystem.forms.input('search', 'Search Teachers', '', {
              placeholder: 'Search by name or initials...',
              icon: 'fas fa-search'
            })}
            ${SRDesignSystem.forms.select('filter_teacher_type', 'Teacher Type', [
              { value: '', label: 'All Teachers' },
              { value: 'Class Teacher', label: 'Class Teacher' },
              { value: 'Subject Teacher', label: 'Subject Teacher' }
            ], '')}
            ${SRDesignSystem.forms.select('filter_employment_status', 'Status', [
              { value: '', label: 'All Status' },
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' },
              { value: 'terminated', label: 'Terminated' },
              { value: 'retired', label: 'Retired' }
            ], '')}
          </div>
        </div>

        <!-- Teachers Table -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-4 border-b border-primary-200">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-primary-900 flex items-center">
              ${SRDesignSystem.components.icon('fas fa-chalkboard-teacher', 'base', 'primary-600')}
              <span class="ml-3">School Teachers</span>
            </h3>
          </div>
          <div class="overflow-x-auto max-h-96 overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="px-6 py-3 text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Photo</th>
                  <th class="px-6 py-3 text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Name</th>
                  <th class="px-6 py-3 text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Gender</th>
                  <th class="px-6 py-3 text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Type</th>
                  <th class="px-6 py-3 text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Status</th>
                  <th class="px-6 py-3 text-right ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Actions</th>
                </tr>
              </thead>
              <tbody id="teachers-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Teachers will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  },

  // Render academic context error
  renderAcademicContextError() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Teacher Management',
          'Manage school teachers'
        )}

        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-600')}
            </div>
            <div class="ml-4">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-red-900">Academic Context Required</h3>
              <p class="text-red-700 mt-2">
                You cannot manage teachers without setting up the academic year and terms first.
              </p>
              <p class="text-red-700 mt-1">
                Please go to <strong>Academic Year → Setup</strong> to configure your academic year and terms before managing teachers.
              </p>
              <div class="mt-4 text-center">
                ${SRDesignSystem.forms.button('setup-academic-manage-teacher', 'Setup Academic Year & Terms', 'danger', {
                  icon: 'fas fa-calendar-plus',
                  onclick: 'ManageTeachersComponent.navigateToAcademicSetup()'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize manage teachers component
  async init() {
    try {
      // Check academic context first
      await window.AcademicContext.initialize();
      const activeYear = window.AcademicContext.getActiveAcademicYear();
      const activeTerm = window.AcademicContext.getActiveTerm();

      if (!activeYear || !activeTerm) {

        return;
      }

      // Load initial data first
      await TeacherManagementComponents.loadInitialData();


      // Re-render the component with loaded data
      this.reRenderComponent();

      // Initialize directly - DOM should be ready due to lifecycle manager
      this.populateTeachersTable();
      this.initializeEventListeners();

    } catch (error) {
      console.error('❌ Failed to initialize ManageTeachersComponent:', error);
      if (window.SRDesignSystem?.notifications) {
        window.SRDesignSystem.notifications.show('Failed to load teacher management', 'error');
      }
    }
  },

  // Re-render the component with updated data
  reRenderComponent() {
    const contentArea = document.getElementById('content-area');
    if (contentArea) {
      contentArea.innerHTML = this.render();
    }
  },

  // Reset component state
  resetComponentState() {
    // Reset any component-specific state if needed

  },

  // Cleanup component
  cleanup() {

    this.resetComponentState();

    // Hide any open modals
    const modals = ['edit-teacher-modal', 'delete-teacher-modal'];
    modals.forEach(modalId => {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.classList.add('hidden');
      }
    });


  },

  // Get active teachers count
  getActiveTeachersCount() {
    const teachersState = TeacherManagementComponents.state.teachers;
    let teachers = [];

    if (teachersState?.success && Array.isArray(teachersState.data)) {
      teachers = teachersState.data;
    } else if (Array.isArray(teachersState)) {
      teachers = teachersState;
    }

    return teachers.filter(t => t.employment_status === 'active').length;
  },

  // Populate teachers table
  populateTeachersTable() {
    const tbody = document.getElementById('teachers-table-body');
    if (!tbody) return;

    const teachers = this.getFilteredTeachers();


    if (teachers.length === 0) {
      // Get current filter values to provide contextual messaging
      const typeFilter = document.getElementById('filter_teacher_type')?.selectedOptions[0]?.text || '';
      const statusFilter = document.getElementById('filter_employment_status')?.selectedOptions[0]?.text || '';
      const searchTerm = document.getElementById('search')?.value || '';

      let message = 'No teachers found';
      let suggestion = 'Try adjusting your search criteria or add new teachers.';

      // Provide specific messaging based on applied filters
      if (searchTerm) {
        message = `No teachers found matching "${searchTerm}"`;
        suggestion = `Try a different search term or check the spelling.`;
      } else if (typeFilter && typeFilter !== 'All Types' && statusFilter && statusFilter !== 'All Status') {
        message = `No ${statusFilter.toLowerCase()} ${typeFilter.toLowerCase()} teachers found`;
        suggestion = `Try selecting different filters or check if teachers exist with these criteria.`;
      } else if (typeFilter && typeFilter !== 'All Types') {
        message = `No ${typeFilter.toLowerCase()} teachers found`;
        suggestion = `Try selecting a different teacher type or check if teachers are registered with this type.`;
      } else if (statusFilter && statusFilter !== 'All Status') {
        message = `No ${statusFilter.toLowerCase()} teachers found`;
        suggestion = `Try selecting a different employment status or check if teachers exist with this status.`;
      }

      tbody.innerHTML = `
        <tr>
          <td colspan="5" class="px-6 py-12 text-center text-gray-500">
            <div class="flex flex-col items-center">
              <div class="text-gray-400 mb-4">
                ${SRDesignSystem.components.icon('fas fa-chalkboard-teacher', '4xl', 'gray-300')}
              </div>
              <p class="${SRDesignSystem.responsive.text.lg} font-medium">${message}</p>
              <p class="${SRDesignSystem.responsive.text.sm}">${suggestion}</p>
            </div>
          </td>
        </tr>
      `;
      return;
    }

    tbody.innerHTML = teachers.map(teacher => {
      const actions = [
        {
          label: 'View Details',
          icon: 'fas fa-eye',
          onclick: `ManageTeachersComponent.viewTeacher(${teacher.id})`,
          color: 'blue'
        }
      ];

      return `
        <tr class="hover:bg-gray-50">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center justify-center">
              <div class="flex-shrink-0 h-10 w-10">
                <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-200"
                     src="${ManageTeachersComponent.getTeacherPhotoUrl(teacher.passport_photo)}"
                     alt="${teacher.first_name} ${teacher.last_name}"
                     onerror="this.src='${window.SR.serverUrl}/assets/images/default-avatar.png'; this.onerror=null;">
              </div>
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900">
              ${teacher.first_name} ${teacher.middle_name || ''} ${teacher.last_name}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            ${teacher.gender}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            ${teacher.teacher_type}
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusBadgeClass(teacher.employment_status)}">
              ${this.formatEmploymentStatus(teacher.employment_status)}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-right">
            ${SRDesignSystem.tables.generateKebabMenu(`teacher-${teacher.id}`, actions, teacher)}
          </td>
        </tr>
      `;
    }).join('');
  },

  // Generate dropdown action menu for teacher actions
  generateTeacherActionDropdown(teacherId, actions) {
    const dropdownId = `dropdown-teacher-${teacherId}`;

    return `
      <div class="relative inline-block text-left">
        <button type="button"
                onclick="ManageTeachersComponent.toggleDropdown('${dropdownId}')"
                class="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors duration-150"
                title="More actions">
          <i class="fas fa-ellipsis-v text-sm"></i>
        </button>

        <div id="${dropdownId}"
             class="hidden fixed z-[9999] w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1"
             style="transform: translate(-100%, -100%);">
          ${actions.map(action => `
            <button onclick="ManageTeachersComponent.closeDropdown('${dropdownId}'); ${action.onclick}"
                    class="flex items-center w-full px-4 py-3 text-sm text-left hover:bg-gray-50 transition-colors duration-150 ${action.color || 'text-gray-700'}">
              <i class="${action.icon} w-4 mr-3"></i>
              ${action.label}
            </button>
          `).join('')}
        </div>
      </div>
    `;
  },

  // Toggle dropdown visibility
  toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (!dropdown) return;

    // Close all other dropdowns first
    this.closeAllDropdowns();

    // Toggle current dropdown
    dropdown.classList.toggle('hidden');

    // Position dropdown next to the button if dropdown is now open
    if (!dropdown.classList.contains('hidden')) {
      const button = dropdown.previousElementSibling;
      if (button) {
        const buttonRect = button.getBoundingClientRect();

        // Position dropdown to the left of the kebab icon, aligned to its top
        dropdown.style.position = 'fixed';
        dropdown.style.right = `${window.innerWidth - buttonRect.left + 8}px`; // 8px gap to the left of button
        dropdown.style.top = `${buttonRect.top}px`; // Same vertical position as button
        dropdown.style.zIndex = '1000';
      }

      setTimeout(() => {
        document.addEventListener('click', this.handleOutsideClick);
      }, 10);
    }
  },

  // Close specific dropdown
  closeDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (dropdown) {
      dropdown.classList.add('hidden');
    }
    document.removeEventListener('click', this.handleOutsideClick);
  },

  // Close all dropdowns
  closeAllDropdowns() {
    const dropdowns = document.querySelectorAll('[id^="dropdown-teacher-"]');
    dropdowns.forEach(dropdown => {
      dropdown.classList.add('hidden');
    });
    document.removeEventListener('click', this.handleOutsideClick);
  },

  // Handle clicks outside dropdown
  handleOutsideClick(event) {
    const isDropdownButton = event.target.closest('[onclick*="toggleDropdown"]');
    const isDropdownContent = event.target.closest('[id^="dropdown-teacher-"]');

    if (!isDropdownButton && !isDropdownContent) {
      ManageTeachersComponent.closeAllDropdowns();
    }
  },

  // Get filtered teachers based on search and filters
  getFilteredTeachers() {
    // Handle API response structure: { success: true, data: [...] }
    let teachers = [];
    const teachersState = TeacherManagementComponents.state.teachers;

    if (teachersState?.success && Array.isArray(teachersState.data)) {
      teachers = teachersState.data;
    } else if (Array.isArray(teachersState)) {
      teachers = teachersState;
    } else {
      teachers = [];
    }



    // Apply search filter
    const searchTerm = document.getElementById('search')?.value?.toLowerCase() || '';
    if (searchTerm) {
      teachers = teachers.filter(teacher =>
        teacher.first_name?.toLowerCase().includes(searchTerm) ||
        teacher.last_name?.toLowerCase().includes(searchTerm) ||
        (teacher.initials && teacher.initials.toLowerCase().includes(searchTerm))
      );
    }

    // Apply teacher type filter
    const typeFilter = document.getElementById('filter_teacher_type')?.value || '';
    if (typeFilter) {
      teachers = teachers.filter(teacher => teacher.teacher_type === typeFilter);
    }

    // Apply employment status filter
    const statusFilter = document.getElementById('filter_employment_status')?.value || '';
    if (statusFilter) {
      teachers = teachers.filter(teacher => teacher.employment_status === statusFilter);
    }


    return teachers;
  },

  // Get status badge CSS class
  getStatusBadgeClass(status) {
    const classes = {
      'active': 'bg-green-100 text-green-800',
      'inactive': 'bg-yellow-100 text-yellow-800',
      'terminated': 'bg-red-100 text-red-800',
      'retired': 'bg-purple-100 text-purple-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
  },

  // Helper method to format employment status in sentence case
  formatEmploymentStatus(status) {
    if (!status) return '';
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  },

  // Helper method to get proper teacher photo URL with fallback
  getTeacherPhotoUrl(passportPhoto) {
    const serverUrl = window.SRConfig.getServerUrl();

    // Handle null, undefined, empty string, or string "null"
    if (passportPhoto === null ||
        passportPhoto === undefined ||
        passportPhoto === '' ||
        passportPhoto === 'null' ||
        passportPhoto === 'undefined' ||
        (typeof passportPhoto === 'string' && passportPhoto.trim() === '')) {
      return `${serverUrl}/assets/images/default-avatar.png`;
    }

    // Convert to string and trim
    const photoPath = String(passportPhoto).trim();

    // If photo path starts with 'C:' or contains backslashes, it's an invalid path
    if (photoPath.startsWith('C:') || photoPath.includes('\\') || photoPath.startsWith('file://')) {
      return `${serverUrl}/assets/images/default-avatar.png`;
    }

    // If photo path doesn't start with '/', add it
    if (!photoPath.startsWith('/')) {
      return `${serverUrl}/${photoPath}`;
    }

    return `${serverUrl}${photoPath}`;
  },

  // Get teaching subjects display
  getTeachingSubjects(assignedSubjects) {
    if (!assignedSubjects) return '-';

    const assignments = assignedSubjects.split('|').filter(Boolean);
    if (assignments.length === 0) return '-';

    const subjectNames = assignments.map(assignment => {
      const [, , name] = assignment.split(':');
      return name;
    }).filter(Boolean);

    if (subjectNames.length <= 2) {
      return subjectNames.join(', ');
    }
    return `${subjectNames.slice(0, 2).join(', ')} +${subjectNames.length - 2}`;
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Search input with debounce for better performance
    const searchInput = document.getElementById('search');
    if (searchInput) {
      const debouncedSearch = window.ComponentLifecycleManager.debounce(() => {
        this.populateTeachersTable();
      }, 300);

      searchInput.addEventListener('input', debouncedSearch);
    }

    // Filter dropdowns
    ['filter_teacher_type', 'filter_employment_status'].forEach(filterId => {
      const filterElement = document.getElementById(filterId);
      if (filterElement) {
        filterElement.addEventListener('change', () => {
          this.populateTeachersTable();
        });
      }
    });
  },

  // Add new teacher
  addTeacher() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('register-teacher');
    }
  },

  // View teacher details
  viewTeacher(teacherId) {
    const teachers = TeacherManagementComponents.state.teachers;
    let teacher = null;

    if (teachers?.success && Array.isArray(teachers.data)) {
      teacher = teachers.data.find(t => t.id === teacherId);
    } else if (Array.isArray(teachers)) {
      teacher = teachers.find(t => t.id === teacherId);
    }

    if (!teacher) {
      if (window.SRDesignSystem?.notifications) {
        window.SRDesignSystem.notifications.show('Teacher not found', 'error');
      }
      return;
    }

    this.showTeacherDetailsModal(teacher);
  },

  // Show teacher details modal
  showTeacherDetailsModal(teacher) {
    const modalHtml = `
      <div id="teacher-details-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">Teacher Details</h3>
              ${SRDesignSystem.forms.button('close-teacher-details', '', 'ghost', {
                icon: 'fas fa-times',
                onclick: 'this.closest(\'#teacher-details-modal\').remove()',
                class: 'text-gray-400 hover:text-gray-600 p-2'
              })}
            </div>

            <!-- Teacher Information -->
            <div class="py-6">
              <!-- Personal Information -->
              <div class="bg-white rounded-lg p-6">
                <h4 class="text-base font-semibold text-gray-900 mb-6">Teacher Information</h4>
                <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                  <!-- Photo -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Photo</label>
                    <div class="flex items-center">
                      <img class="h-16 w-16 rounded-full object-cover border-2 border-gray-200"
                           src="${ManageTeachersComponent.getTeacherPhotoUrl(teacher.passport_photo)}"
                           alt="${teacher.first_name} ${teacher.last_name}"
                           onerror="this.src='${window.SR.serverUrl}/assets/images/default-avatar.png'; this.onerror=null;">
                    </div>
                  </div>
                  <!-- Full Name -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <p class="text-sm text-gray-900">${teacher.first_name} ${teacher.middle_name || ''} ${teacher.last_name}</p>
                  </div>
                  <!-- Gender -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                    <p class="text-sm text-gray-900">${teacher.gender}</p>
                  </div>
                  <!-- Teacher Type -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Teacher Type</label>
                    <p class="text-sm text-gray-900">${teacher.teacher_type}</p>
                  </div>
                  <!-- Employment Status -->
                  <div class="col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Employment Status</label>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${ManageTeachersComponent.getStatusBadgeClass(teacher.employment_status)}">
                      ${ManageTeachersComponent.formatEmploymentStatus(teacher.employment_status)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-between pt-4 border-t">
              <div class="flex space-x-3">
                ${SRDesignSystem.forms.button('edit-teacher-details', 'Edit Details', 'blue', {
                  icon: 'fas fa-edit',
                  onclick: `ManageTeachersComponent.showEditTeacherModal(${JSON.stringify(teacher).replace(/"/g, '&quot;')}); this.closest('#teacher-details-modal').remove();`
                })}
                ${SRDesignSystem.forms.button('view-teacher-assignments', 'View Assignments', 'green', {
                  icon: 'fas fa-tasks',
                  onclick: `ManageTeachersComponent.viewAssignments(${teacher.id}); this.closest('#teacher-details-modal').remove();`
                })}
              </div>
              <div>
                ${SRDesignSystem.forms.button('delete-teacher', 'Delete Teacher', 'red', {
                  icon: 'fas fa-trash',
                  onclick: `ManageTeachersComponent.confirmDeleteTeacher(${teacher.id}, '${teacher.first_name} ${teacher.last_name}'); this.closest('#teacher-details-modal').remove();`
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
  },

  // Edit teacher
  editTeacher(teacherId) {
    const teachers = TeacherManagementComponents.state.teachers;
    let teacher = null;

    if (teachers?.success && Array.isArray(teachers.data)) {
      teacher = teachers.data.find(t => t.id === teacherId);
    } else if (Array.isArray(teachers)) {
      teacher = teachers.find(t => t.id === teacherId);
    }

    if (!teacher) {
      if (window.SRDesignSystem?.notifications) {
        window.SRDesignSystem.notifications.show('Teacher not found', 'error');
      }
      return;
    }

    this.showEditTeacherModal(teacher);
  },

  // Show edit teacher modal
  showEditTeacherModal(teacher) {
    // Store the original teacher data for preserving fields not shown in the form
    window.editingTeacherData = teacher;

    const modalHtml = `
      <div id="edit-teacher-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">Edit Teacher Information</h3>
              ${SRDesignSystem.forms.button('close-edit-teacher', '', 'ghost', {
                icon: 'fas fa-times',
                onclick: 'delete window.editingTeacherData; this.closest(\'#edit-teacher-modal\').remove()',
                class: 'text-gray-400 hover:text-gray-600 p-2'
              })}
            </div>

            <!-- Edit Form -->
            <form id="edit-teacher-form" class="py-4 space-y-6">
              <input type="hidden" id="edit-teacher-id" value="${teacher.id}">

              <!-- Passport Photo Section -->
              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Passport Photo</h4>
                <div class="flex items-center space-x-4">
                  <div class="flex-shrink-0">
                    <img id="edit-teacher-photo-preview"
                         class="h-20 w-20 rounded-full object-cover border-2 border-gray-200"
                         src="${ManageTeachersComponent.getTeacherPhotoUrl(teacher.passport_photo)}"
                         alt="Teacher Photo"
                         onerror="this.src='${window.SR.serverUrl}/assets/images/default-avatar.png'; this.onerror=null;">
                  </div>
                  <div class="flex-1">
                    <input type="file" id="edit-passport-photo" accept="image/*"
                           onchange="ManageTeachersComponent.previewEditPhoto(this)"
                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                    <p class="mt-1 text-xs text-gray-500">Upload a new passport photo (JPG, PNG - Max 2MB)</p>
                  </div>
                </div>
              </div>

              <!-- Row 1: Gender, Teacher Type -->
              <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Gender</label>
                  <select id="edit-gender" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200" required>
                    <option value="Male" ${teacher.gender === 'Male' ? 'selected' : ''}>Male</option>
                    <option value="Female" ${teacher.gender === 'Female' ? 'selected' : ''}>Female</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Teacher Type</label>
                  <select id="edit-teacher-type" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200" required>
                    <option value="Class Teacher" ${teacher.teacher_type === 'Class Teacher' ? 'selected' : ''}>Class Teacher</option>
                    <option value="Subject Teacher" ${teacher.teacher_type === 'Subject Teacher' ? 'selected' : ''}>Subject Teacher</option>
                  </select>
                </div>
              </div>

              <!-- Row 2: Employment Status -->
              <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Employment Status</label>
                  <select id="edit-employment-status" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200" required>
                    <option value="active" ${teacher.employment_status === 'active' ? 'selected' : ''}>Active</option>
                    <option value="inactive" ${teacher.employment_status === 'inactive' ? 'selected' : ''}>Inactive</option>
                    <option value="terminated" ${teacher.employment_status === 'terminated' ? 'selected' : ''}>Terminated</option>
                    <option value="retired" ${teacher.employment_status === 'retired' ? 'selected' : ''}>Retired</option>
                  </select>
                </div>
                <div>
                  <!-- Empty column for layout balance -->
                </div>
              </div>

              <!-- Row 2: First Name, Middle Name, Last Name -->
              <div class="grid grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">First Name</label>
                  <input type="text" id="edit-first-name" value="${teacher.first_name}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200" required>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Middle Name</label>
                  <input type="text" id="edit-middle-name" value="${teacher.middle_name || ''}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Last Name</label>
                  <input type="text" id="edit-last-name" value="${teacher.last_name}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200" required>
                </div>
              </div>

              <!-- Row 3: Initials, Joining Date -->
              <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Initials</label>
                  <input type="text" id="edit-initials" value="${teacher.initials || ''}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                         placeholder="e.g., M.N.">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Joining Date</label>
                  <input type="date" id="edit-joining-date" value="${teacher.joining_date ? new Date(teacher.joining_date).toISOString().split('T')[0] : ''}"
                         class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
                </div>
              </div>
            </form>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3 pt-4 border-t">
              ${SRDesignSystem.forms.button('save-teacher-changes', 'Save Changes', 'blue', {
                icon: 'fas fa-save',
                onclick: 'ManageTeachersComponent.saveTeacherChanges()'
              })}
              ${SRDesignSystem.forms.button('cancel-edit-teacher', 'Cancel', 'secondary', {
                onclick: 'delete window.editingTeacherData; this.closest(\'#edit-teacher-modal\').remove()'
              })}
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Setup validation for edit form
    this.setupEditFormValidation();
  },

  // Setup validation for edit form
  setupEditFormValidation() {
    // Setup name field formatting (uppercase, letters only)
    const nameFields = ['edit-first-name', 'edit-middle-name', 'edit-last-name'];

    nameFields.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (field) {
        // Prevent non-letter characters and spaces from being typed
        field.addEventListener('keydown', (e) => {
          // Allow navigation and control keys
          const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];

          // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z
          if ((e.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(e.key.toLowerCase())) ||
              allowedKeys.includes(e.key)) {
            return;
          }

          // Prevent spaces and only allow letters (a-z, A-Z)
          if (e.key === ' ' || !/^[a-zA-Z]$/.test(e.key)) {
            e.preventDefault();
          }
        });

        // Convert to uppercase and remove any non-letter characters including spaces
        field.addEventListener('input', (e) => {
          let value = e.target.value.toUpperCase().replace(/[^A-Z]/g, '');
          e.target.value = value;
        });

        // Handle paste events - only allow letters, no spaces
        field.addEventListener('paste', (e) => {
          e.preventDefault();
          let paste = '';
          if (e.clipboardData) {
            paste = e.clipboardData.getData('text');
          }
          paste = paste.toUpperCase().replace(/[^A-Z]/g, '');
          e.target.value = paste;
        });
      }
    });

    // Setup initials field validation (letters and dots only)
    const initialsField = document.getElementById('edit-initials');
    if (initialsField) {
      initialsField.addEventListener('input', (e) => {
        let value = e.target.value.toUpperCase().replace(/[^A-Z.]/g, '');
        e.target.value = value;
      });
    }
  },

  // Preview photo in edit modal
  previewEditPhoto(input) {
    if (input.files && input.files[0]) {
      const file = input.files[0];

      // Validate file size (2MB max)
      if (file.size > 2 * 1024 * 1024) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('File size must be less than 2MB', 'error');
        }
        input.value = '';
        return;
      }

      // Validate file type (only JPG and PNG)
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select a JPG or PNG image file', 'error');
        }
        input.value = '';
        return;
      }

      const reader = new FileReader();
      reader.onload = function(e) {
        const preview = document.getElementById('edit-teacher-photo-preview');
        if (preview) {
          preview.src = e.target.result;
        }
      };
      reader.readAsDataURL(file);
    }
  },

  // Save teacher changes
  async saveTeacherChanges() {
    try {
      const originalTeacher = window.editingTeacherData;
      if (!originalTeacher) {
        throw new Error('No teacher data found for editing');
      }

      // Get form data
      const formData = {
        first_name: document.getElementById('edit-first-name')?.value?.trim(),
        middle_name: document.getElementById('edit-middle-name')?.value?.trim() || null,
        last_name: document.getElementById('edit-last-name')?.value?.trim(),
        initials: document.getElementById('edit-initials')?.value?.trim() || null,
        gender: document.getElementById('edit-gender')?.value,
        teacher_type: document.getElementById('edit-teacher-type')?.value,
        employment_status: document.getElementById('edit-employment-status')?.value,
        joining_date: document.getElementById('edit-joining-date')?.value || null
      };

      // Validate required fields
      if (!formData.first_name || !formData.last_name || !formData.gender || !formData.teacher_type || !formData.employment_status) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please fill in all required fields', 'error');
        }
        return;
      }

      // Handle photo upload if a new photo was selected
      const photoInput = document.getElementById('edit-passport-photo');
      let uploadedPhotoPath = null;

      if (photoInput && photoInput.files && photoInput.files[0]) {
        try {
          // Check if ImageUploadUtil is available
          if (!window.ImageUploadUtil) {
            throw new Error('ImageUploadUtil is not available. Please ensure the image upload utility is loaded.');
          }

          // Upload the photo first
          const uploadResult = await window.ImageUploadUtil.uploadImage(
            photoInput.files[0],
            'teacher-photo'
          );

          if (uploadResult.success) {
            uploadedPhotoPath = uploadResult.filePath;
          } else {
            throw new Error('Failed to upload passport photo');
          }
        } catch (uploadError) {
          console.error('Photo upload error:', uploadError);
          if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
            window.SRDesignSystem.notifications.show(
              'Failed to upload passport photo: ' + uploadError.message,
              'error'
            );
          }
          return; // Stop execution if photo upload fails
        }
      }

      // Merge with original data to preserve fields not in the form
      const updatedData = {
        ...originalTeacher,
        ...formData
      };

      // Include uploaded photo path if available
      if (uploadedPhotoPath) {
        updatedData.passport_photo = uploadedPhotoPath;
      }

      // Update teacher via API
      const result = await window.TeachersAPI.update(originalTeacher.id, updatedData);

      if (result.success) {
        // Show success notification
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Teacher information updated successfully', 'success');
        }

        // Close modal and cleanup
        document.getElementById('edit-teacher-modal')?.remove();
        delete window.editingTeacherData;

        // Reload data from server and refresh the teachers table
        await TeacherManagementComponents.loadInitialData();
        this.populateTeachersTable();
      } else {
        throw new Error(result.message || 'Failed to update teacher');
      }
    } catch (error) {
      console.error('Save teacher changes error:', error);
      if (window.SRDesignSystem?.notifications) {
        window.SRDesignSystem.notifications.show(error.message || 'Failed to update teacher information', 'error');
      }
    }
  },

  // View teacher assignments
  async viewAssignments(teacherId) {
    try {
      const serverUrl = window.SRConfig.getServerUrl();

      // Fetch teacher assignments
      const response = await fetch(`${serverUrl}/api/teachers/${teacherId}/assignments`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('smartreport_token')}`
        }
      });

      const result = await response.json();

      if (result.success) {
        this.showAssignmentsModal(teacherId, result.data);
      } else {
        throw new Error(result.message || 'Failed to load assignments');
      }
    } catch (error) {
      console.error('View assignments error:', error);
      SRDesignSystem.notifications.show(error.message || 'Failed to load assignments', 'error');
    }
  },

  // Show assignments modal
  showAssignmentsModal(teacherId, assignments) {
    const { class_assignments, subject_assignments } = assignments;

    const modalContent = `
      <div class="space-y-6">
        <!-- Class Assignments -->
        <div>
          <h4 class="text-lg font-semibold text-gray-900 mb-3">Class Assignments</h4>
          ${class_assignments.length > 0 ? `
            <div class="space-y-2">
              ${class_assignments.map(assignment => `
                <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <div>
                    <span class="font-medium">${assignment.class_name}</span>
                    ${assignment.stream_name ? `<span class="text-sm text-gray-600"> (${assignment.stream_name})</span>` : ''}
                  </div>
                  <button onclick="ManageTeachersComponent.removeClassAssignment(${teacherId}, ${assignment.id})"
                          class="text-red-600 hover:text-red-800 text-sm">
                    Remove
                  </button>
                </div>
              `).join('')}
            </div>
          ` : `
            <p class="text-gray-500 text-sm">No class assignments</p>
          `}
        </div>

        <!-- Subject Assignments -->
        <div>
          <h4 class="text-lg font-semibold text-gray-900 mb-3">Subject Assignments</h4>
          ${subject_assignments.length > 0 ? `
            <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gapSm}">
              ${subject_assignments.map(assignment => `
                <div class="p-3 bg-green-50 rounded-lg">
                  <div class="font-medium">${assignment.subject_name}</div>
                  <div class="text-sm text-gray-600">${assignment.subject_level.replace('_', '-').toUpperCase()}</div>
                </div>
              `).join('')}
            </div>
          ` : `
            <p class="text-gray-500 text-sm">No subject assignments</p>
          `}
        </div>
      </div>
    `;

    // Show modal using SR Design System
    window.SRDesignSystem.modal.show('Teacher Assignments', modalContent);
  },

  // Remove class assignment
  async removeClassAssignment(teacherId, assignmentId) {
    if (!confirm('Are you sure you want to remove this class assignment?')) {
      return;
    }

    try {
      const serverUrl = window.SRConfig.getServerUrl();

      const response = await fetch(`${serverUrl}/api/teachers/${teacherId}/class-assignment/${assignmentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('smartreport_token')}`
        }
      });

      const result = await response.json();

      if (result.success) {
        SRDesignSystem.notifications.show('Class assignment removed successfully!', 'success');

        // Refresh assignments view
        this.viewAssignments(teacherId);
      } else {
        throw new Error(result.message || 'Failed to remove class assignment');
      }
    } catch (error) {
      console.error('Remove class assignment error:', error);
      SRDesignSystem.notifications.show(error.message || 'Failed to remove class assignment', 'error');
    }
  },

  // Assign subjects to teacher
  assignSubjects(teacherId) {

    // TODO: Implement subject assignment modal
    alert('Subject assignment functionality will be implemented soon');
  },

  // Confirm delete teacher with modal
  confirmDeleteTeacher(teacherId, teacherName) {
    const confirmationHtml = `
      <div id="delete-teacher-confirmation" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', 'xl', 'red-600')}
            </div>
            <h3 class="${SRDesignSystem.responsive.text.lg} font-medium text-gray-900 mt-4">Delete Teacher</h3>
            <div class="mt-2 px-7 py-3">
              <p class="${SRDesignSystem.responsive.text.sm} text-gray-500">
                Are you sure you want to delete <strong>${teacherName}</strong>?
                This action cannot be undone and will remove all associated data.
              </p>
            </div>
            <div class="flex justify-center space-x-3 px-4 py-3">
              ${SRDesignSystem.forms.button('confirm-delete-teacher', 'Delete', 'red', {
                icon: 'fas fa-trash',
                onclick: `ManageTeachersComponent.deleteTeacher(${teacherId}); this.closest('#delete-teacher-confirmation').remove();`
              })}
              ${SRDesignSystem.forms.button('cancel-delete-teacher', 'Cancel', 'secondary', {
                onclick: 'this.closest(\'#delete-teacher-confirmation\').remove()'
              })}
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', confirmationHtml);
  },

  // Delete teacher
  async deleteTeacher(teacherId) {
    if (!confirm('Are you sure you want to delete this teacher? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`${window.SRConfig.getApiUrl(`/teachers/${teacherId}`)}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('smartreport_token')}`
        }
      });

      const result = await response.json();

      if (result.success) {
        SRDesignSystem.notifications.show('Teacher deleted successfully', 'success');
        await TeacherManagementComponents.loadInitialData();
        this.populateTeachersTable();
      } else {
        SRDesignSystem.notifications.show(result.message || 'Failed to delete teacher', 'error');
      }
    } catch (error) {
      console.error('Delete error:', error);
      SRDesignSystem.notifications.show('Failed to delete teacher', 'error');
    }
  },

  // Export teachers
  exportTeachers() {
    // TODO: Implement export functionality
  },

  // Navigate to academic year setup
  navigateToAcademicSetup() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('academic-year-setup');
    }
  }
};

// Export components to global scope
window.RegisterTeacherComponent = RegisterTeacherComponent;
window.ManageTeachersComponent = ManageTeachersComponent;
window.TeacherManagementComponents = TeacherManagementComponents;

// Make components and functions available globally
window.RegisterTeacherComponent = RegisterTeacherComponent;
window.handleTeacherTypeChange = function(teacherType) {
  RegisterTeacherComponent.handleTeacherTypeChange(teacherType);
};
