// SmartReport - O-Level Student Registration Component
// Handles registration of O-Level students (S.1 to S.4)

// Uses global API services: window.StudentsAPI, window.ClassesAPI, etc.
// Uses global config: window.SRConfig
// Uses environment configuration: ../config/environment.js

// Register O-Level Student Component
const RegisterOLevelStudentComponent = {
  // Form data storage for retention on errors
  formData: {},

  // Render register O-Level student form
  render() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'O-Level Student Registration',
          'Complete all required fields to register a new student'
        )}
        <!-- Registration Form -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <form id="register-o-level-student-form" class="${SRDesignSystem.responsive.spacing.padding} space-y-8">
            <!-- Hidden fields for auto-populated values -->
            <input type="hidden" name="student_level" value="o_level">
            <input type="hidden" id="status" name="status" value="active">
            <input type="hidden" id="enrollment_date" name="enrollment_date" value="">
            <input type="hidden" id="current_academic_year_id" name="current_academic_year_id" value="">
            <input type="hidden" id="current_term_id" name="current_term_id" value="">

            <!-- Personal Information Section -->
            <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900 mb-6">Personal Information</h3>

              <!-- Passport Photo and Admission Number - First row (3 columns: 2+1) -->
              <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap} mb-4">
                <div class="col-span-2 form-field">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Passport Photo
                  </label>
                  <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center space-x-4">
                      <div class="flex-shrink-0">
                        <img id="passport-photo-preview"
                             class="h-20 w-20 rounded-full object-cover border-2 border-gray-200"
                             src="${window.SR.serverUrl}/assets/images/default-avatar.png"
                             alt="Student Photo"
                             onerror="this.src='${window.SR.serverUrl}/assets/images/default-avatar.png'; this.onerror=null;">
                      </div>
                      <div class="flex-1">
                        <input type="file" id="passport_photo" name="passport_photo" accept="image/*"
                               onchange="RegisterOLevelStudentComponent.previewPhoto(this)"
                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                        <p class="mt-1 text-xs text-gray-500">Upload student passport photo (JPG, PNG - Max 2MB)</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-span-1">
                  ${SRDesignSystem.forms.input('admission_number', 'Admission Number', '', {
                    required: true,
                    placeholder: 'e.g., 2024/001',
                    helpText: 'Unique student identifier'
                  })}
                </div>
              </div>

              <!-- Names - Second row (3 columns) -->
              <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap} mb-4">
                ${SRDesignSystem.forms.input('first_name', 'First Name', '', {
                  required: true,
                  placeholder: 'Enter first name'
                })}
                ${SRDesignSystem.forms.input('middle_name', 'Middle Name', '', {
                  placeholder: 'Enter middle name (optional)'
                })}
                ${SRDesignSystem.forms.input('last_name', 'Last Name', '', {
                  required: true,
                  placeholder: 'Enter last name'
                })}
              </div>

              <!-- Date of Birth and Gender - Third row (2 columns) -->
              <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                ${SRDesignSystem.forms.input('date_of_birth', 'Date of Birth', '', {
                  type: 'date',
                })}
                ${SRDesignSystem.forms.select('gender', 'Gender', [
                  { value: '', label: 'Select Gender' },
                  { value: 'Male', label: 'Male' },
                  { value: 'Female', label: 'Female' }
                ], '', { required: true })}
              </div>
            </div>

            <!-- Class & Stream Selection Section -->
            <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900 mb-6">Class Selection</h3>

              <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">

                ${SRDesignSystem.forms.select('current_class_id', 'Current Class', [
                  { value: '', label: 'Select Class' }
                ], '', {
                  required: true,
                  helpText: 'Assign student to an O-Level class'
                })}

                ${SRDesignSystem.forms.select('stream_id', 'Class Stream', [
                  { value: '', label: 'Select Class First', disabled: true }
                ], '', {
                  helpText: 'Select the stream for this class (if available)',
                  disabled: true,
                  required: false
                })}

              </div>
            </div>



            <!-- O-Level Subject Selection Section -->
            <div id="o-level-subjects-section" class="hidden bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900 mb-6">Subject Selection</h3>
              <div id="o-level-subjects-container" class="min-h-[100px]">
                <!-- O-Level subjects will be populated dynamically -->
              </div>
            </div>



            <!-- Form Actions -->
            <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
              <div class="flex items-center justify-end">
                <div class="flex justify-end">
                  ${SRDesignSystem.forms.button('register', 'Register Student', 'primary', {
                    type: 'submit',
                    loading: false,
                    icon: 'fas fa-user-plus'
                  })}
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    `;
  },

  // Initialize event listeners for O-Level registration
  initializeEventListeners() {
    try {
      // Form submission
      const form = document.getElementById('register-o-level-student-form');
      if (form) {
        form.addEventListener('submit', this.handleSubmit.bind(this));
      }

      // Handle class selection change
      const classSelect = document.getElementById('current_class_id');
      if (classSelect) {
        classSelect.addEventListener('change', this.handleClassChange.bind(this));
      }

      // Setup name field formatting
      this.setupNameFieldFormatting();

      // Setup admission number formatting
      this.setupAdmissionNumberFormatting();
    } catch (error) {
      console.error('❌ Error in O-Level initializeEventListeners:', error);
      throw error;
    }
  },

  // Setup name field formatting (uppercase, letters only)
  setupNameFieldFormatting() {
    const nameFields = ['first_name', 'middle_name', 'last_name'];

    nameFields.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (field) {
        // Prevent non-letter characters from being typed
        field.addEventListener('keydown', (e) => {
          // Allow navigation and control keys
          const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];

          // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z
          if ((e.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(e.key.toLowerCase())) ||
              allowedKeys.includes(e.key)) {
            return;
          }

          // Prevent spaces and only allow letters (a-z, A-Z)
          if (e.key === ' ' || !/^[a-zA-Z]$/.test(e.key)) {
            e.preventDefault();
          }
        });

        // Convert to uppercase and remove any non-letter characters including spaces
        field.addEventListener('input', (e) => {
          let value = e.target.value.toUpperCase().replace(/[^A-Z]/g, '');
          e.target.value = value;
        });

        // Handle paste events - only allow letters, no spaces
        field.addEventListener('paste', (e) => {
          e.preventDefault();
          let paste = '';
          if (e.clipboardData) {
            paste = e.clipboardData.getData('text');
          }
          paste = paste.toUpperCase().replace(/[^A-Z]/g, '');
          e.target.value = paste;
        });
      }
    });
  },

  // Setup admission number formatting (no spaces allowed)
  setupAdmissionNumberFormatting() {
    const admissionField = document.getElementById('admission_number');
    if (admissionField) {
      // Prevent spaces from being typed
      admissionField.addEventListener('keydown', (e) => {
        // Block space key
        if (e.key === ' ' || e.key === 'Spacebar') {
          e.preventDefault();
          return;
        }

        // Allow navigation and control keys
        const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];

        // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z
        if ((e.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(e.key.toLowerCase())) ||
            allowedKeys.includes(e.key)) {
          return;
        }
      });

      // Remove spaces on input (in case of paste)
      admissionField.addEventListener('input', (e) => {
        e.target.value = e.target.value.replace(/\s/g, '');
      });

      // Handle paste events to remove spaces
      admissionField.addEventListener('paste', (e) => {
        e.preventDefault();
        let paste = '';
        if (e.clipboardData) {
          paste = e.clipboardData.getData('text');
        }
        paste = paste.replace(/\s/g, ''); // Remove all spaces
        e.target.value = paste;
      });
    }
  },

  // Preview passport photo
  previewPhoto(input) {
    if (input.files && input.files[0]) {
      const file = input.files[0];

      // Validate file size (2MB max)
      if (file.size > 2 * 1024 * 1024) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('File size must be less than 2MB', 'error');
        } else {
          alert('File size must be less than 2MB');
        }
        input.value = '';
        return;
      }

      // Validate file type (only JPG and PNG)
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        if (window.SRDesignSystem?.notifications) {
          window.SRDesignSystem.notifications.show('Please select a JPG or PNG image file', 'error');
        } else {
          alert('Please select a JPG or PNG image file');
        }
        input.value = '';
        return;
      }

      const reader = new FileReader();
      reader.onload = function(e) {
        const preview = document.getElementById('passport-photo-preview');
        if (preview) {
          preview.src = e.target.result;
        }
      };
      reader.readAsDataURL(file);
    }
  },





  // Determine education level from class data
  determineEducationLevel(selectedClass, selectedOption) {
    if (!selectedClass) return null;

    // Check option dataset first (most reliable)
    const optionEducationLevel = selectedOption?.dataset?.educationLevel;
    if (optionEducationLevel) {
      console.log('✅ Education level from option dataset:', optionEducationLevel);
      return optionEducationLevel;
    }

    // Fallback to class data analysis
    const educationLevelCode = selectedClass.education_level_code;
    const classLevelCode = selectedClass.class_level_code;
    const className = selectedClass.name || '';

    console.log('🔍 Analyzing class data:', {
      educationLevelCode,
      classLevelCode,
      className
    });

    // Check for O-Level (S.1 to S.4)
    if (educationLevelCode === 'o_level' ||
        ['s1', 's2', 's3', 's4'].includes(classLevelCode) ||
        className.match(/S\.[1-4]/) ||
        className.match(/S[1-4]/) ||
        className.toLowerCase().includes('senior 1') ||
        className.toLowerCase().includes('senior 2') ||
        className.toLowerCase().includes('senior 3') ||
        className.toLowerCase().includes('senior 4')) {
      console.log('✅ Detected O-Level class');
      return 'o_level';
    }



    console.log('⚠️ Could not determine education level');
    return null;
  },

  // Hide subject section
  hideSubjectSection() {
    const oLevelSection = document.getElementById('o-level-subjects-section');
    if (oLevelSection) {
      oLevelSection.classList.add('hidden');
    }
    this.clearSubjectContainer();
  },

  // Show O-Level subject selection
  showOLevelSubjects() {
    console.log('📚 Showing O-Level subject selection...');

    // Show the O-Level subjects section
    const oLevelSection = document.getElementById('o-level-subjects-section');

    if (oLevelSection) {
      oLevelSection.classList.remove('hidden');
      console.log('✅ O-Level subject section shown');
    } else {
      console.error('❌ O-Level subject section not found in DOM');
    }

    // Load the subjects for the selected class
    this.loadOLevelSubjects();
  },

  // Clear subject container
  clearSubjectContainer() {
    const container = document.getElementById('o-level-subjects-container');
    if (container) container.innerHTML = '';
  },

  // Load O-Level subjects for the selected class
  async loadOLevelSubjects() {
    try {
      console.log('📚 Loading O-Level subjects...');

      // Get the selected class details to determine class_level_id
      const classSelect = document.getElementById('current_class_id');
      const selectedOption = classSelect?.options[classSelect.selectedIndex];
      const classLevelId = selectedOption?.dataset.classLevelId;

      if (!classLevelId) {
        console.warn('⚠️ No class_level_id found, loading all O-Level subjects');
        const result = await window.SubjectsAPI.oLevel.getAll();

        if (result.success) {
          const subjects = result.data || result;
          this.renderOLevelSubjects(Array.isArray(subjects) ? subjects : [], null);
        } else {
          this.showSubjectLoadError('o-level-subjects-container', 'O-Level subjects');
        }
        return;
      }

      // Load O-Level subjects filtered by class level
      const result = await window.SubjectsAPI.oLevel.getAll({ class_level_id: classLevelId });

      if (result.success) {
        const subjects = result.data || result;
        console.log('✅ Loaded', subjects.length, 'O-Level subjects for class level', classLevelId);

        // Get class level for rendering logic
        const classLevel = this.getClassLevelFromCode(selectedOption?.dataset.classLevelCode);
        this.renderOLevelSubjects(Array.isArray(subjects) ? subjects : [], classLevel);
      } else {
        console.error('❌ Failed to load O-Level subjects:', result.message);
        this.showSubjectLoadError('o-level-subjects-container', 'O-Level subjects');
      }
    } catch (error) {
      console.error('❌ Exception in loadOLevelSubjects:', error);
      this.showSubjectLoadError('o-level-subjects-container', 'O-Level subjects');
    }
  },

  // Get class level from class ID
  getClassLevel(classId) {
    if (!classId) return null;

    const classes = StudentManagementComponents.state.classes.data || StudentManagementComponents.state.classes;
    const selectedClass = Array.isArray(classes) ? classes.find(cls => cls.id == classId) : null;

    if (!selectedClass) return null;

    return this.getClassLevelFromCode(selectedClass.class_level_code);
  },

  // Get class level grouping from class level code
  getClassLevelFromCode(classLevelCode) {
    if (!classLevelCode) return null;

    if (['s1', 's2'].includes(classLevelCode)) {
      return 's1_s2';
    } else if (['s3', 's4'].includes(classLevelCode)) {
      return 's3_s4';
    } else if (['s5', 's6'].includes(classLevelCode)) {
      return 's5_s6';
    }

    return null;
  },

  // Show error message when subjects fail to load
  showSubjectLoadError(containerId, subjectType) {
    const container = document.getElementById(containerId);
    if (container) {
      container.innerHTML = `
        <div class="text-center py-12 text-red-500">
          <div class="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-500')}
          </div>
          <p class="${SRDesignSystem.responsive.text.lg} font-medium text-red-600">Failed to load ${subjectType}</p>
          <p class="${SRDesignSystem.responsive.text.sm} text-red-500 mt-1">Please try refreshing the page or contact support.</p>
        </div>
      `;
    }
  },


  // Get O-Level selection rules text
  getOLevelSelectionRulesText(classLevel) {
    if (classLevel === 's1_s2') {
      return 'S.1-S.2: 10 compulsory subjects + 2 elective subjects (max 1 from each group: Religious Education, Language, Practical) = 12 total subjects';
    } else if (classLevel === 's3_s4') {
      return 'S.3-S.4: 7 compulsory subjects + 1-2 elective subjects (max 1 from each group: Religious Education, Language, Practical) = 8-9 total subjects';
    }
    return 'Select subjects according to your class level requirements';
  },

  // Render individual subject card
  renderSubjectCard(subject, isCompulsory = false, inputType = 'checkbox', groupName = 'selected_subjects[]', subjectGroup = null) {
    const isRequired = isCompulsory || subject.is_compulsory || subject.subject_type === 'compulsory';
    const inputName = inputType === 'radio' ? groupName : 'selected_subjects[]';
    const groupAttribute = subjectGroup ? `data-subject-group="${subjectGroup}"` : '';

    return `
      <div class="flex items-center space-x-3 ${SRDesignSystem.responsive.spacing.paddingSm} bg-white border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
        <input type="${inputType}"
               id="subject_${subject.id}"
               name="${inputName}"
               value="${subject.id}"
               ${isRequired ? 'checked disabled data-compulsory="true"' : ''}
               ${groupAttribute}
               class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 ${inputType === 'radio' ? 'rounded-full' : 'rounded'}">
        <label for="subject_${subject.id}" class="flex-1 cursor-pointer">
          <div class="font-medium text-gray-900 ${SRDesignSystem.responsive.text.sm}">${subject.name}</div>
          <div class="text-xs text-gray-500">${subject.short_name || subject.code || ''}</div>
        </label>
      </div>
    `;
  },

  // Add event listeners for subject selection validation
  addSubjectSelectionListeners(classLevel) {
    // Add listeners for elective subject checkboxes only (exclude compulsory subjects)
    const electiveCheckboxes = document.querySelectorAll('input[name="selected_subjects[]"]:not([data-compulsory="true"])');
    electiveCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        this.validateElectiveSelection(classLevel);
      });
    });

  },

  // Validate elective subject selection
  validateElectiveSelection(classLevel) {
    if (classLevel === 's1_s2') {
      // S1-S2: Maximum 1 elective
      const selectedElectives = document.querySelectorAll('input[name="selected_subjects[]"]:checked:not([data-compulsory="true"])');

      if (selectedElectives.length >= 1) {
        // Disable remaining elective checkboxes (but never touch compulsory ones)
        const electiveCheckboxes = document.querySelectorAll('input[name="selected_subjects[]"]:not([data-compulsory="true"])');
        electiveCheckboxes.forEach(checkbox => {
          if (!checkbox.checked) {
            checkbox.disabled = true;
            checkbox.parentElement.classList.add('opacity-50');
          }
        });
      } else {
        // Re-enable all elective checkboxes (but never touch compulsory ones)
        const electiveCheckboxes = document.querySelectorAll('input[name="selected_subjects[]"]:not([data-compulsory="true"])');
        electiveCheckboxes.forEach(checkbox => {
          checkbox.disabled = false;
          checkbox.parentElement.classList.remove('opacity-50');
        });
      }
    } else if (classLevel === 's3_s4') {
      // S3-S4: Maximum 2 electives
      // Only count non-compulsory subjects
      const selectedElectives = document.querySelectorAll('input[name="selected_subjects[]"]:checked:not([data-compulsory="true"])');

      if (selectedElectives.length >= 2) {
        // Disable remaining elective checkboxes (but never touch compulsory ones)
        const unselectedElectives = document.querySelectorAll('input[name="selected_subjects[]"]:not(:checked):not([data-compulsory="true"])');
        unselectedElectives.forEach(checkbox => {
          checkbox.disabled = true;
          checkbox.parentElement.classList.add('opacity-50');
        });
      } else {
        // Re-enable all elective checkboxes (but never touch compulsory ones)
        const electiveCheckboxes = document.querySelectorAll('input[name="selected_subjects[]"]:not([data-compulsory="true"])');
        electiveCheckboxes.forEach(checkbox => {
          checkbox.disabled = false;
          checkbox.parentElement.classList.remove('opacity-50');
        });
      }
    }
  },

  // Validate subject selection based on class level
  validateSubjectSelection(classLevel) {
    if (classLevel === 's1_s2') {
      // S1-S2: Exactly 2 electives (compulsory are auto-selected)
      const selectedElectives = Array.from(document.querySelectorAll('input[name="selected_subjects[]"]:checked:not([data-compulsory="true"])'))
        .map(input => parseInt(input.value));

      // Count elective subjects 
      if (selectedElectives.length < 2) {
        return {
          isValid: false,
          message: 'Please select exactly 2 elective subjects for S.1-S.2'
        };
      }
      if (selectedElectives.length > 2) {
        return {
          isValid: false,
          message: 'Please select only 2 elective subjects for S.1-S.2'
        };
      }
    } else if (classLevel === 's3_s4') {
      // S3-S4: 1-2 electives
      const selectedElectives = Array.from(document.querySelectorAll('input[name="selected_subjects[]"]:checked:not([disabled])'))
        .map(input => parseInt(input.value));

      if (selectedElectives.length < 1) {
        return {
          isValid: false,
          message: 'Please select at least 1 elective subject for S.3-S.4'
        };
      }
      if (selectedElectives.length > 2) {
        return {
          isValid: false,
          message: 'Please select maximum 2 elective subjects for S.3-S.4'
        };
      }
    }

    return { isValid: true };
  },

  // Initialize register student component
  async init() {
    console.log('🚀 O-Level RegisterOLevelStudentComponent.init() called');

    // Wait for academic context to be loaded
    await window.AcademicContext.initialize();

    // Check if data is already loaded, if not load it
    const hasData = StudentManagementComponents.state.classes &&
                   StudentManagementComponents.state.academicYears &&
                   StudentManagementComponents.state.terms;

    if (!hasData) {
      await StudentManagementComponents.loadInitialData();
    }

    // Initialize directly - DOM should be ready due to lifecycle manager
    try {
      this.populateAcademicContextFields();
      this.initializeEventListeners();
      this.populateDropdowns();
      this.restoreFormData(); // Restore form data if it exists
    } catch (error) {
      console.error('❌ Error initializing O-Level component:', error);
    }
  },

  // Reset component state
  resetComponentState() {
    this.formData = {};
    console.log('🔄 RegisterOLevelStudentComponent state reset');
  },

  // Cleanup component
  cleanup() {
    console.log('🧹 Cleaning up RegisterOLevelStudentComponent...');
    this.resetComponentState();

    // Reset form if it exists
    const form = document.getElementById('register-o-level-student-form');
    if (form) {
      form.reset();
    }

    console.log('✅ RegisterOLevelStudentComponent cleanup completed');
  },

  // Save current form data for retention on errors
  saveFormData() {
    const form = document.getElementById('register-o-level-student-form');
    if (form) {
      const formData = new FormData(form);
      this.formData = Object.fromEntries(formData.entries());

      // Also save checkbox states for subjects
      const checkboxes = form.querySelectorAll('input[type="checkbox"]');
      checkboxes.forEach(checkbox => {
        this.formData[checkbox.name + '_checked'] = checkbox.checked;
      });

      // Save radio button states
      const radios = form.querySelectorAll('input[type="radio"]:checked');
      radios.forEach(radio => {
        this.formData[radio.name] = radio.value;
      });

      console.log('💾 O-Level form data saved for retention:', this.formData);
    }
  },

  // Restore form data after error
  restoreFormData() {
    if (Object.keys(this.formData).length === 0) return;

    console.log('🔄 Restoring O-Level form data:', this.formData);

    const form = document.getElementById('register-o-level-student-form');
    if (!form) return;

    // Restore text inputs, selects, and textareas
    Object.keys(this.formData).forEach(key => {
      if (key.endsWith('_checked')) return; // Skip checkbox state keys

      const element = form.querySelector(`[name="${key}"]`);
      if (element && !element.disabled) {
        if (element.type === 'checkbox') {
          element.checked = this.formData[key + '_checked'] || false;
        } else if (element.type === 'radio') {
          if (element.value === this.formData[key]) {
            element.checked = true;
          }
        } else {
          element.value = this.formData[key] || '';
        }
      }
    });

    // Trigger change events to update dependent fields
    const classSelect = form.querySelector('#current_class_id');
    if (classSelect && classSelect.value) {
      classSelect.dispatchEvent(new Event('change'));
    }
  },

  // Clear saved form data (call after successful submission)
  clearFormData() {
    this.formData = {};
    console.log('🗑️ O-Level form data cleared');
  },

  // Populate academic context fields with active academic year and term
  populateAcademicContextFields() {
    const activeYear = window.AcademicContext.getActiveAcademicYear();
    const activeTerm = window.AcademicContext.getActiveTerm();

    // Populate hidden academic year field
    const academicYearHidden = document.getElementById('current_academic_year_id');
    if (academicYearHidden && activeYear) {
      academicYearHidden.value = activeYear.id;
    }

    // Populate hidden term field
    const termHidden = document.getElementById('current_term_id');
    if (termHidden && activeTerm) {
      termHidden.value = activeTerm.id;
    }


  },



  // Populate dropdown fields
  populateDropdowns() {
    try {
      // Populate hidden fields with current data
      this.populateHiddenFields();
      // Populate visible dropdowns
      this.populateClassDropdown();
      // Initialize stream dropdown in disabled state
      this.clearStreamDropdown();
    } catch (error) {
      console.error('❌ Error in O-Level populateDropdowns:', error);
    }
  },

  // Populate hidden fields with current academic data
  populateHiddenFields() {
    const academicYears = StudentManagementComponents.state.academicYears.data || StudentManagementComponents.state.academicYears;
    const terms = StudentManagementComponents.state.terms.data || StudentManagementComponents.state.terms;

    // Set current academic year (hidden field only)
    if (Array.isArray(academicYears)) {
      const activeYear = academicYears.find(year => year.is_active);
      if (activeYear) {
        const academicYearIdField = document.getElementById('current_academic_year_id');
        if (academicYearIdField) academicYearIdField.value = activeYear.id;
      }
    }

    // Set current term (hidden field only)
    if (Array.isArray(terms)) {
      const activeTerm = terms.find(term => term.is_active);
      if (activeTerm) {
        const termIdField = document.getElementById('current_term_id');
        if (termIdField) termIdField.value = activeTerm.id;
      }
    }

    // Set enrollment date to current date
    const enrollmentDateField = document.getElementById('enrollment_date');
    if (enrollmentDateField) {
      const currentDate = new Date().toISOString().split('T')[0];
      enrollmentDateField.value = currentDate;
    }
  },

  // Populate class dropdown
  populateClassDropdown() {
    // Populate O-Level classes only
    const classSelect = document.getElementById('current_class_id');

    if (classSelect) {
      classSelect.innerHTML = '<option value="">Select Class</option>';

      const classes = StudentManagementComponents.state.classes.data || StudentManagementComponents.state.classes;

      if (Array.isArray(classes)) {
        console.log('📚 O-Level total classes available:', classes.length);
        console.log('📚 O-Level sample class data:', classes[0]);

        // Filter for O-Level classes only (S.1 to S.4)
        const oLevelClasses = classes.filter(cls => {
          const isOLevel = cls.education_level_code === 'o_level' ||
                          cls.class_level_code?.startsWith('s') && ['s1', 's2', 's3', 's4'].includes(cls.class_level_code) ||
                          cls.name?.match(/^S\.[1-4]$/);
          console.log(`📚 O-Level checking class ${cls.name}: isOLevel=${isOLevel}, education_level_code=${cls.education_level_code}, class_level_code=${cls.class_level_code}`);
          return isOLevel;
        });

        console.log('📚 O-Level filtered classes:', oLevelClasses.length);
        console.log('📚 O-Level filtered classes data:', oLevelClasses);

        if (oLevelClasses.length > 0) {
          // Add O-Level classes (simple class names without streams)
          oLevelClasses.forEach(cls => {
            const option = document.createElement('option');
            option.value = cls.id;
            option.textContent = cls.name; // Just the class name (S.1, S.2, etc.)
            option.dataset.educationLevel = 'o_level';
            option.dataset.classLevelId = cls.class_level_id;
            option.dataset.classLevelCode = cls.class_level_code;
            classSelect.appendChild(option);
          });

          if (oLevelClasses.length === 0) {
            console.warn('⚠️ No O-Level classes found');
          }
        }
      } else {
        console.warn('⚠️ Classes data is not available');
      }
    } else {
      console.warn('⚠️ Class select element not found - DOM may not be ready');
    }
  },

  // Handle class selection change for O-Level students
  handleClassChange(event) {
    const classId = event.target.value;
    const selectedOption = event.target.selectedOptions[0];
    const classes = StudentManagementComponents.state.classes.data || StudentManagementComponents.state.classes;
    const selectedClass = Array.isArray(classes) ? classes.find(cls => cls.id == classId) : null;

    // Hide subject section first
    this.hideSubjectSection();

    // Clear stream dropdown first
    this.clearStreamDropdown();

    if (selectedClass && classId) {
      // Determine education level to ensure this is an O-Level class
      const educationLevel = this.determineEducationLevel(selectedClass, selectedOption);

      if (educationLevel === 'o_level') {
        // Load streams for the selected class
        this.loadStreamsForClass(classId);
        this.showOLevelSubjects();
      } else {
        console.warn('⚠️ Non O-Level class selected in O-Level form:', educationLevel);
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(
            'Please select an O-Level class (S.1 - S.4) for O-Level student registration',
            'warning'
          );
        }
      }
    } else {
      // No class selected, clear streams
      this.clearStreamDropdown();
    }
  },

  // Load streams for a specific class (permanent assignments)
  async loadStreamsForClass(classId) {
    try {
      console.log('🔄 Loading streams for class:', classId);

      const result = await window.StreamsAPI.getForClass(classId);

      if (result.success) {
        const { streams, streams_required, class_info } = result.data;
        console.log('✅ Streams loaded for class:', class_info.name, 'Streams:', streams.length, 'Required:', streams_required);
        this.populateStreamDropdown(streams, streams_required);
      } else {
        console.error('Failed to load streams:', result.message);
        this.clearStreamDropdown();
      }
    } catch (error) {
      console.error('Error loading streams:', error);
      this.clearStreamDropdown();
    }
  },

  // Clear stream dropdown
  clearStreamDropdown() {
    const streamSelect = document.getElementById('stream_id');
    if (streamSelect) {
      // Clear all options first
      streamSelect.innerHTML = '';

      // Create and add the disabled option
      const option = document.createElement('option');
      option.value = '';
      option.textContent = 'Select Class First';
      option.disabled = true;
      option.selected = true;
      streamSelect.appendChild(option);

      // Disable the dropdown
      streamSelect.disabled = true;
      streamSelect.required = false;

      // Update label to remove required indicator
      const streamField = streamSelect.closest('.form-field');
      if (streamField) {
        const label = streamField.querySelector('label');
        if (label) {
          label.innerHTML = 'Class Stream';
        }
      }
    }
  },

  // Clear subjects display
  clearSubjects() {
    const subjectsContainer = document.getElementById('o-level-subjects-container');
    if (subjectsContainer) {
      subjectsContainer.innerHTML = `<p class="${SRDesignSystem.responsive.text.base} text-gray-500">Select a class to view subjects</p>`;
    }
  },

  // Populate stream dropdown (updated version)
  populateStreamDropdown(streams = [], streamsRequired = false) {
    const streamSelect = document.getElementById('stream_id');
    if (!streamSelect) {
      return; // Silently return if element not found
    }

    // Clear and activate the dropdown
    streamSelect.innerHTML = '<option value="">Select Stream</option>';
    streamSelect.disabled = false;

    if (streams.length === 0) {
      // No streams available
      streamSelect.innerHTML = '<option value="">No streams available</option>';
      streamSelect.disabled = true;
      streamSelect.required = false;
      return;
    }

    // Add stream options
    streams.forEach(stream => {
      const option = document.createElement('option');
      option.value = stream.id || ''; // null for "No Stream (Default)"
      option.textContent = stream.name;
      if (stream.is_default) {
        option.dataset.isDefault = 'true';
        // Pre-select the default option only if streams are not required
        if (!streamsRequired) {
          option.selected = true;
        }
      }
      streamSelect.appendChild(option);
    });

    // Set required state - required when streams are available
    streamSelect.required = true;

    // Update the label to show required state
    const streamField = streamSelect.closest('.form-field');
    if (streamField) {
      const label = streamField.querySelector('label');
      if (label) {
        label.innerHTML = 'Class Stream <span class="text-red-500">*</span>';
      }
    }

    console.log('✅ Stream dropdown activated and populated:', {
      options: streams.length,
      required: streamsRequired,
      disabled: false,
      streams: streams.map(s => ({ id: s.id, name: s.name, is_default: s.is_default }))
    });
  },




  // Hide all subject selection sections
  hideAllSubjectSections() {
    const oLevelSection = document.getElementById('o-level-subjects-section');
    if (oLevelSection) oLevelSection.classList.add('hidden');

    this.clearSubjectContainers();
  },










  // Clear all subject containers
  clearSubjectContainers() {
    const oLevelContainer = document.getElementById('o-level-subjects-container');
    if (oLevelContainer) oLevelContainer.innerHTML = '';
  },





  // Get class level from class ID
  getClassLevel(classId) {
    if (!classId) return null;

    const classes = StudentManagementComponents.state.classes.data || StudentManagementComponents.state.classes;
    const selectedClass = Array.isArray(classes) ? classes.find(cls => cls.id == classId) : null;

    if (!selectedClass) return null;

    // Determine class level
    if (selectedClass.class_level_code === 's1' || selectedClass.class_level_code === 's2' ||
        selectedClass.name?.includes('S.1') || selectedClass.name?.includes('S.2')) {
      return 's1_s2';
    } else if (selectedClass.class_level_code === 's3' || selectedClass.class_level_code === 's4' ||
               selectedClass.name?.includes('S.3') || selectedClass.name?.includes('S.4')) {
      return 's3_s4';
    } else if (selectedClass.class_level_code === 's5' || selectedClass.class_level_code === 's6' ||
               selectedClass.name?.includes('S.5') || selectedClass.name?.includes('S.6')) {
      return 's5_s6';
    }

    return null;
  },



  // Show error message when subjects fail to load
  showSubjectLoadError(containerId, subjectType) {
    const container = document.getElementById(containerId);
    if (container) {
      container.innerHTML = `
        <div class="text-center py-12 text-red-500">
          <div class="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-exclamation-triangle text-2xl text-red-500"></i>
          </div>
          <p class="text-lg font-medium text-red-600">Failed to load ${subjectType}</p>
          <p class="text-sm text-red-500 mt-1">Please try refreshing the page or contact support.</p>
        </div>
      `;
    }
  },

  // Render O-Level subjects
  renderOLevelSubjects(subjects, classLevel) {
    console.log('🎨 Rendering', subjects?.length, 'O-Level subjects for class level:', classLevel);

    const container = document.getElementById('o-level-subjects-container');
    if (!container) {
      console.error('❌ O-Level subjects container not found in DOM');
      return;
    }

    if (!Array.isArray(subjects) || subjects.length === 0) {
      container.innerHTML = `
        <div class="text-center py-12 text-gray-500">
          <div class="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            ${SRDesignSystem.components.icon('fas fa-book', '2xl', 'gray-400')}
          </div>
          <p class="${SRDesignSystem.responsive.text.lg} font-medium text-gray-600">No O-Level subjects available</p>
          <p class="${SRDesignSystem.responsive.text.sm} text-gray-500 mt-1">Please contact the administrator.</p>
        </div>
      `;
      return;
    }

    // Define compulsory subjects based on class level
    const s1s2CompulsorySubjects = ['ENG', 'ENT', 'MATH', 'BIO', 'CHEM', 'PHY', 'GEOG', 'HPE', 'KIS', 'PE'];
    const s3s4CompulsorySubjects = ['MATH', 'ENG', 'CHEM', 'BIO', 'PHY', 'GEOG', 'HPE'];

    // Group subjects based on class level and selection rules
    let compulsorySubjects, electiveSubjects;

    if (classLevel === 's1_s2') {
      // S1-S2: 10 compulsory + 2 electives = 12 total
      compulsorySubjects = subjects.filter(s => s1s2CompulsorySubjects.includes(s.short_name));

      electiveSubjects = subjects.filter(s =>
        s.subject_type === 'Language' ||
        s.subject_type === 'Practical (pre-vocational)' ||
        s.subject_type === 'Religious Education'
      ).filter(s => !s1s2CompulsorySubjects.includes(s.short_name));
    } else if (classLevel === 's3_s4') {
      // S3-S4: 7 compulsory + max 2 electives = 8-9 total
      compulsorySubjects = subjects.filter(s => s3s4CompulsorySubjects.includes(s.short_name));

      electiveSubjects = subjects.filter(s =>
        !s3s4CompulsorySubjects.includes(s.short_name) // All non-compulsory subjects are electives
      );
    } else {
      // Fallback: show all subjects grouped by type
      compulsorySubjects = subjects.filter(s => s.subject_type === 'Compulsory');
      electiveSubjects = subjects.filter(s =>
        s.subject_type === 'Language' ||
        s.subject_type === 'Practical (pre-vocational)' ||
        s.subject_type === 'Religious Education'
      );
    }

    // Generate selection rules text
    const selectionRulesText = this.getOLevelSelectionRulesText(classLevel);

    container.innerHTML = `
      <div class="space-y-6">
        <!-- Selection Rules -->
        <div class="bg-white rounded-lg shadow-soft border border-gray-200 p-4">
          <h4 class="font-semibold text-gray-900 mb-2">Subject Selection Rules</h4>
          <p class="${SRDesignSystem.responsive.text.sm} text-gray-600">${selectionRulesText}</p>
        </div>

        ${compulsorySubjects.length > 0 ? `
          <div class="bg-white rounded-lg shadow-soft border border-gray-200 p-4">
            <h4 class="font-semibold text-gray-900 mb-3">Compulsory Subjects (${compulsorySubjects.length} ${classLevel === 's1_s2' ? 'of 10' : 'of 7'})</h4>
            <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gapSm}">
              ${compulsorySubjects.map(subject => this.renderSubjectCard(subject, true)).join('')}
            </div>
          </div>
        ` : ''}



        ${electiveSubjects.length > 0 ? `
          <div class="bg-white rounded-lg shadow-soft border border-gray-200 p-4">
            <h4 class="font-semibold text-gray-900 mb-3">Elective Subjects ${classLevel === 's1_s2' ? '(Choose 2)' : '(Choose 1-2)'}</h4>
            ${this.renderElectivesByCategory(electiveSubjects)}
          </div>
        ` : ''}
      </div>
    `;

    // Add event listeners for subject selection validation
    this.addSubjectSelectionListeners(classLevel);
  },

  // Render elective subjects grouped by category
  renderElectivesByCategory(electiveSubjects) {
    // Group subjects by their type
    const religiousSubjects = electiveSubjects.filter(s => s.subject_type === 'Religious Education');
    const languageSubjects = electiveSubjects.filter(s => s.subject_type === 'Language');
    const practicalSubjects = electiveSubjects.filter(s => s.subject_type === 'Practical (pre-vocational)');
    const otherSubjects = electiveSubjects.filter(s =>
      !['Religious Education', 'Language', 'Practical (pre-vocational)'].includes(s.subject_type)
    );

    return `
      <div class="space-y-4">
        ${religiousSubjects.length > 0 ? `
          <div class="bg-white rounded-lg border border-gray-200 p-3">
            <h5 class="font-medium text-gray-900 mb-2 ${SRDesignSystem.responsive.text.sm}">Religious Education Subjects</h5>
            <div class="${SRDesignSystem.responsive.grid.cols3} gap-2">
              ${religiousSubjects.map(subject => this.renderSubjectCard(subject, false, 'checkbox', 'selected_subjects[]', 'Religious Education')).join('')}
            </div>
          </div>
        ` : ''}

        ${languageSubjects.length > 0 ? `
          <div class="bg-white rounded-lg border border-gray-200 p-3">
            <h5 class="font-medium text-gray-900 mb-2 ${SRDesignSystem.responsive.text.sm}">Language Subjects</h5>
            <div class="${SRDesignSystem.responsive.grid.cols3} gap-2">
              ${languageSubjects.map(subject => this.renderSubjectCard(subject, false, 'checkbox', 'selected_subjects[]', 'Language')).join('')}
            </div>
          </div>
        ` : ''}

        ${practicalSubjects.length > 0 ? `
          <div class="bg-white rounded-lg border border-gray-200 p-3">
            <h5 class="font-medium text-gray-900 mb-2 ${SRDesignSystem.responsive.text.sm}">Practical (Pre-vocational) Subjects</h5>
            <div class="${SRDesignSystem.responsive.grid.cols3} gap-2">
              ${practicalSubjects.map(subject => this.renderSubjectCard(subject, false, 'checkbox', 'selected_subjects[]', 'Practical (pre-vocational)')).join('')}
            </div>
          </div>
        ` : ''}

        ${otherSubjects.length > 0 ? `
          <div class="bg-white rounded-lg border border-gray-200 p-3">
            <h5 class="font-medium text-gray-900 mb-2 ${SRDesignSystem.responsive.text.sm}">Other Subjects</h5>
            <div class="${SRDesignSystem.responsive.grid.cols3} gap-2">
              ${otherSubjects.map(subject => this.renderSubjectCard(subject, false, 'checkbox')).join('')}
            </div>
          </div>
        ` : ''}
      </div>
    `;
  },

  // Get O-Level selection rules text
  getOLevelSelectionRulesText(classLevel) {
    if (classLevel === 's1_s2') {
      return 'S.1-S.2: 10 compulsory subjects + 2 elective subjects (max 1 from each group: Religious Education, Language, Practical) = 12 total subjects';
    } else if (classLevel === 's3_s4') {
      return 'S.3-S.4: 7 compulsory subjects + 1-2 elective subjects (max 1 from each group: Religious Education, Language, Practical) = 8-9 total subjects';
    }
    return 'Select subjects according to your class level requirements';
  },

  // Add event listeners for subject selection validation
  addSubjectSelectionListeners(classLevel) {
    // Add listeners for elective subject checkboxes only (exclude compulsory subjects)
    const electiveCheckboxes = document.querySelectorAll('input[name="selected_subjects[]"]:not([data-compulsory="true"])');

    electiveCheckboxes.forEach(checkbox => {
      // Remove existing listeners to avoid duplicates
      checkbox.removeEventListener('change', this.handleSubjectChange);

      // Add new listener
      checkbox.addEventListener('change', (event) => {
        this.enforceGroupSelectionLimits(event.target);
        this.validateElectiveSelection(classLevel);
      });
    });
  },

  // Enforce one subject per group rule
  enforceGroupSelectionLimits(changedCheckbox) {
    const subjectGroup = changedCheckbox.getAttribute('data-subject-group');

    if (subjectGroup) {
      const groupCheckboxes = document.querySelectorAll(`input[data-subject-group="${subjectGroup}"]`);

      if (changedCheckbox.checked) {
        // If a checkbox in a group is checked, uncheck and disable all other checkboxes in the same group
        groupCheckboxes.forEach(checkbox => {
          if (checkbox !== changedCheckbox) {
            checkbox.checked = false;
            checkbox.disabled = true;
            checkbox.parentElement.classList.add('opacity-50', 'cursor-not-allowed');
          }
        });
      } else {
        // If a checkbox in a group is unchecked, re-enable all other checkboxes in the same group
        groupCheckboxes.forEach(checkbox => {
          if (checkbox !== changedCheckbox) {
            checkbox.disabled = false;
            checkbox.parentElement.classList.remove('opacity-50', 'cursor-not-allowed');
          }
        });
      }
    }
  },

  // Validate and enforce elective selection limits
  validateElectiveSelection(classLevel) {
    if (classLevel === 's1_s2') {
      // S1-S2: Exactly 2 electives
      // Only count non-compulsory, non-disabled subjects
      const selectedElectives = document.querySelectorAll('input[name="selected_subjects[]"]:checked:not([data-compulsory="true"])');

      if (selectedElectives.length >= 2) {
        // Disable other elective checkboxes that are not already disabled by group rules
        const unselectedElectives = document.querySelectorAll('input[name="selected_subjects[]"]:not(:checked):not([data-compulsory="true"]):not([disabled])');
        unselectedElectives.forEach(checkbox => {
          checkbox.disabled = true;
          checkbox.parentElement.classList.add('opacity-50', 'cursor-not-allowed');
          checkbox.setAttribute('data-disabled-by-limit', 'true');
        });
      } else {
        // Re-enable elective checkboxes that were disabled by limit (but not by group rules)
        const limitDisabledCheckboxes = document.querySelectorAll('input[name="selected_subjects[]"][data-disabled-by-limit="true"]');
        limitDisabledCheckboxes.forEach(checkbox => {
          checkbox.disabled = false;
          checkbox.parentElement.classList.remove('opacity-50', 'cursor-not-allowed');
          checkbox.removeAttribute('data-disabled-by-limit');
        });
      }
    } else if (classLevel === 's3_s4') {
      // S3-S4: Maximum 2 electives
      // Only count non-compulsory subjects
      const selectedElectives = document.querySelectorAll('input[name="selected_subjects[]"]:checked:not([data-compulsory="true"])');

      if (selectedElectives.length >= 2) {
        // Disable remaining elective checkboxes that are not already disabled by group rules
        const unselectedElectives = document.querySelectorAll('input[name="selected_subjects[]"]:not(:checked):not([data-compulsory="true"]):not([disabled])');
        unselectedElectives.forEach(checkbox => {
          checkbox.disabled = true;
          checkbox.parentElement.classList.add('opacity-50', 'cursor-not-allowed');
          checkbox.setAttribute('data-disabled-by-limit', 'true');
        });
      } else {
        // Re-enable elective checkboxes that were disabled by limit (but not by group rules)
        const limitDisabledCheckboxes = document.querySelectorAll('input[name="selected_subjects[]"][data-disabled-by-limit="true"]');
        limitDisabledCheckboxes.forEach(checkbox => {
          checkbox.disabled = false;
          checkbox.parentElement.classList.remove('opacity-50', 'cursor-not-allowed');
          checkbox.removeAttribute('data-disabled-by-limit');
        });
      }
    }
  },




  // Validate subject selection based on class level rules
  validateSubjectSelection(classLevel) {
    // First check group restrictions
    const groupValidation = this.validateGroupSelections();
    if (!groupValidation.isValid) {
      return groupValidation;
    }

    if (classLevel === 's1_s2') {
      // S1-S2: Exactly 2 elective subjects
      const selectedElectives = Array.from(document.querySelectorAll('input[name="selected_subjects[]"]:checked:not([disabled])'))
        .map(input => parseInt(input.value));

      if (selectedElectives.length < 2) {
        return {
          isValid: false,
          message: 'Please select exactly 2 elective subjects for S.1-S.2'
        };
      }
      if (selectedElectives.length > 2) {
        return {
          isValid: false,
          message: 'Please select only 2 elective subjects for S.1-S.2'
        };
      }
    } else if (classLevel === 's3_s4') {
      // S3-S4: 1-2 electives
      const selectedElectives = Array.from(document.querySelectorAll('input[name="selected_subjects[]"]:checked:not([disabled])'))
        .map(input => parseInt(input.value));

      if (selectedElectives.length < 1) {
        return {
          isValid: false,
          message: 'Please select at least 1 elective subject for S.3-S.4'
        };
      }
      if (selectedElectives.length > 2) {
        return {
          isValid: false,
          message: 'Please select maximum 2 elective subjects for S.3-S.4'
        };
      }

    }

    return { isValid: true };
  },

  // Validate that only one subject is selected per group
  validateGroupSelections() {
    const groups = ['Religious Education', 'Language', 'Practical (pre-vocational)'];

    for (const group of groups) {
      const groupCheckboxes = document.querySelectorAll(`input[data-subject-group="${group}"]:checked`);
      if (groupCheckboxes.length > 1) {
        return {
          isValid: false,
          message: `You can only select one subject from the ${group} group. Please uncheck extra selections.`
        };
      }
    }

    return { isValid: true };
  },



  // Handle form submission
  async handleSubmit(event) {
    event.preventDefault();

    // Save form data for retention in case of errors
    this.saveFormData();

    const formData = new FormData(event.target);
    let data = Object.fromEntries(formData.entries());

    // Handle passport photo upload if provided
    const passportPhotoFile = formData.get('passport_photo');

    if (passportPhotoFile && passportPhotoFile.size > 0) {
      try {
        // Check if ImageUploadUtil is available
        if (!window.ImageUploadUtil) {
          throw new Error('ImageUploadUtil is not available. Please ensure the image upload utility is loaded.');
        }

        SRDesignSystem.forms.setButtonLoading('register', true);

        // Upload the photo first
        const uploadResult = await window.ImageUploadUtil.uploadImage(
          passportPhotoFile,
          'o-level-student-photo'
        );

        if (uploadResult.success) {
          data.passport_photo = uploadResult.filePath;
        } else {
          throw new Error('Failed to upload passport photo');
        }
      } catch (uploadError) {
        console.error('Photo upload error:', uploadError);
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(
            'Failed to upload passport photo: ' + uploadError.message,
            'error'
          );
        }
        SRDesignSystem.forms.setButtonLoading('register', false);
        return;
      }
    } else {
      // No photo provided, set to null
      data.passport_photo = null;
    }

    // Format name fields (uppercase, letters only)
    if (data.first_name) data.first_name = data.first_name.toUpperCase().replace(/[^A-Z]/g, '');
    if (data.middle_name) data.middle_name = data.middle_name.toUpperCase().replace(/[^A-Z]/g, '');
    if (data.last_name) data.last_name = data.last_name.toUpperCase().replace(/[^A-Z]/g, '');



    // Validate required fields
    if (!data.admission_number || !data.first_name || !data.last_name || !data.gender) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Please fill in all required personal information fields', 'error');
      }
      return;
    }

    if (!data.current_academic_year_id || !data.current_term_id || !data.current_class_id) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Please select academic year, term, and class - these are required for student registration', 'error');
      }
      return;
    }

    // Validate stream selection if required
    const streamSelect = document.getElementById('stream_id');
    if (streamSelect && streamSelect.required && !data.stream_id) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Please select a class stream as it is required for this class', 'error');
      }
      return;
    }

    // Validate subject selection based on class level
    const classLevel = this.getClassLevel(data.current_class_id);
    const subjectValidation = this.validateSubjectSelection(classLevel);
    if (!subjectValidation.isValid) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show(subjectValidation.message, 'error');
      }
      return;
    }

    // Collect selected subjects
    const selectedSubjects = [];

    // Add compulsory subjects (already checked and disabled)
    const compulsorySubjects = document.querySelectorAll('input[name="selected_subjects[]"][data-compulsory="true"]:checked');
    compulsorySubjects.forEach(input => {
      selectedSubjects.push(parseInt(input.value));
    });

    // Add selected elective subjects
    const electiveSubjects = document.querySelectorAll('input[name="selected_subjects[]"]:checked:not([data-compulsory="true"])');
    electiveSubjects.forEach(input => {
      selectedSubjects.push(parseInt(input.value));
    });

    // All elective subjects are handled uniformly

    // Add selected subjects to form data
    data.selected_subjects = selectedSubjects;



    console.log('📝 Submitting student registration:', {
      student: {
        admission_number: data.admission_number,
        name: `${data.first_name} ${data.last_name}`,
        gender: data.gender
      },
      enrollment: data.enrollment_data,
      subjects: selectedSubjects.length
    });



    try {
      SRDesignSystem.forms.setButtonLoading('register', true);

      // Use O-Level specific API endpoint
      const result = await window.StudentsAPI.registerOLevel(data);

      if (result.success) {
        // Show success notification
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(
            `Student registered successfully!`,
            'success'
          );
        }

        // Clear saved form data on successful submission
        this.clearFormData();

        // Reset form
        event.target.reset();
        this.resetFormSections();

        // Redirect to manage students after a short delay
        setTimeout(() => {
          if (window.PageRouter) {
            window.PageRouter.loadPage('manage-students');
          }
        }, 2000);
      } else {
        // Show error notification
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(
            result.message || 'Failed to register O-Level student',
            'error'
          );
        }
      }
    } catch (error) {

      // Extract the specific error message from the server response
      let errorMessage = 'Failed to register O-Level student. Please check your connection and try again.';
      if (error.message) {
        errorMessage = error.message;
      }

      // Show error notification with specific server message
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show(errorMessage, 'error');
      }
    } finally {
      SRDesignSystem.forms.setButtonLoading('register', false);
    }
  },

  // Reset form sections to initial state
  resetFormSections() {
    // Hide all subject sections
    this.hideAllSubjectSections();

    // Reset class selection
    const classSelect = document.getElementById('current_class_id');
    if (classSelect) classSelect.value = '';
  }
};


// Make component globally available
window.RegisterOLevelStudentComponent = RegisterOLevelStudentComponent;