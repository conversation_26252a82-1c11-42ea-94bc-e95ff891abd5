const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// =============================================
// CONTINUOUS ASSESSMENTS ROUTES
// =============================================

// Get O-Level continuous assessments with filters
router.get('/continuous/o-level', async (req, res) => {
  try {
    const { student_id, subject_id, term_id, academic_year_id } = req.query;

    let query = `
      SELECT
        ca.*,
        s.id as student_id,
        s.first_name, s.last_name,
        CONCAT(s.first_name, ' ', s.last_name) as student_name,
        s.gender,
        sub.name as subject_name, sub.short_name as subject_short_name,
        ay.name as academic_year_name,
        term.name as term_name
      FROM o_level_subject_continuous_assessments_scores ca
      JOIN o_level_students s ON ca.student_id = s.id
      JOIN o_level_subjects sub ON ca.subject_id = sub.id
      JOIN academic_years ay ON ca.academic_year_id = ay.id
      JOIN terms term ON ca.term_id = term.id
      WHERE 1=1
    `;

    let params = [];

    if (student_id) {
      query += ' AND ca.student_id = ?';
      params.push(student_id);
    }

    if (subject_id) {
      query += ' AND ca.subject_id = ?';
      params.push(subject_id);
    }

    if (term_id) {
      query += ' AND ca.term_id = ?';
      params.push(term_id);
    }

    if (academic_year_id) {
      query += ' AND ca.academic_year_id = ?';
      params.push(academic_year_id);
    }

    query += ' ORDER BY ca.created_at DESC, s.first_name, s.last_name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get O-Level continuous assessments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve O-Level continuous assessments'
    });
  }
});

// Get A-Level Principal continuous assessments with filters
router.get('/continuous/a-level/principal', async (req, res) => {
  try {
    const { student_id, subject_id, subject_paper_id, term_id, academic_year_id } = req.query;

    let query = `
      SELECT
        ca.*,
        s.admission_number, s.first_name, s.last_name,
        sub.name as subject_name, sub.short_name as subject_short_name,
        sp.paper_number, sp.paper_name,
        ay.name as academic_year_name,
        term.name as term_name
      FROM a_level_paper_continuous_assessments_scores ca
      JOIN a_level_students s ON ca.student_id = s.id
      JOIN a_level_subject_papers sp ON ca.subject_paper_id = sp.id
      JOIN a_level_subjects sub ON sp.subject_id = sub.id
      JOIN academic_years ay ON ca.academic_year_id = ay.id
      JOIN terms term ON ca.term_id = term.id
      WHERE sub.subject_type = 'Principal'
    `;

    let params = [];

    if (student_id) {
      query += ' AND ca.student_id = ?';
      params.push(student_id);
    }

    if (subject_id) {
      query += ' AND sp.subject_id = ?';
      params.push(subject_id);
    }

    if (subject_paper_id) {
      query += ' AND ca.subject_paper_id = ?';
      params.push(subject_paper_id);
    }

    if (term_id) {
      query += ' AND ca.term_id = ?';
      params.push(term_id);
    }

    if (academic_year_id) {
      query += ' AND ca.academic_year_id = ?';
      params.push(academic_year_id);
    }

    query += ' ORDER BY ca.created_at DESC, s.admission_number';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get A-Level Principal continuous assessments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve A-Level Principal continuous assessments'
    });
  }
});

// Get A-Level Subsidiary continuous assessments with filters
router.get('/continuous/a-level/subsidiary', async (req, res) => {
  try {
    const { student_id, subject_id, subject_paper_id, term_id, academic_year_id } = req.query;

    let query = `
      SELECT
        ca.*,
        s.admission_number, s.first_name, s.last_name,
        sub.name as subject_name, sub.short_name as subject_short_name,
        sp.paper_number, sp.paper_name,
        ay.name as academic_year_name,
        term.name as term_name
      FROM a_level_paper_continuous_assessments_scores ca
      JOIN a_level_students s ON ca.student_id = s.id
      JOIN a_level_subject_papers sp ON ca.subject_paper_id = sp.id
      JOIN a_level_subjects sub ON sp.subject_id = sub.id
      JOIN academic_years ay ON ca.academic_year_id = ay.id
      JOIN terms term ON ca.term_id = term.id
      WHERE sub.subject_type = 'Subsidiary'
    `;

    let params = [];

    if (student_id) {
      query += ' AND ca.student_id = ?';
      params.push(student_id);
    }

    if (subject_id) {
      query += ' AND sp.subject_id = ?';
      params.push(subject_id);
    }

    if (subject_paper_id) {
      query += ' AND ca.subject_paper_id = ?';
      params.push(subject_paper_id);
    }

    if (term_id) {
      query += ' AND ca.term_id = ?';
      params.push(term_id);
    }

    if (academic_year_id) {
      query += ' AND ca.academic_year_id = ?';
      params.push(academic_year_id);
    }

    query += ' ORDER BY ca.created_at DESC, s.admission_number';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get A-Level Subsidiary continuous assessments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve A-Level Subsidiary continuous assessments'
    });
  }
});

// Get O-Level continuous assessment by ID
router.get('/continuous/o-level/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT
        ca.*,
        s.admission_number, s.first_name, s.last_name,
        sub.name as subject_name, sub.short_name as subject_short_name,
        ay.name as academic_year_name,
        term.name as term_name
      FROM o_level_subject_continuous_assessments_scores ca
      JOIN o_level_students s ON ca.student_id = s.id
      JOIN o_level_subjects sub ON ca.subject_id = sub.id
      JOIN academic_years ay ON ca.academic_year_id = ay.id
      JOIN terms term ON ca.term_id = term.id
      WHERE ca.id = ?
    `;

    const result = await executeQuery(query, [id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'O-Level continuous assessment not found'
      });
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get O-Level continuous assessment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve O-Level continuous assessment'
    });
  }
});

// Get A-Level Principal continuous assessment by ID
router.get('/continuous/a-level/principal/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT
        ca.*,
        s.admission_number, s.first_name, s.last_name,
        sub.name as subject_name, sub.short_name as subject_short_name,
        sp.paper_number, sp.paper_name,
        ay.name as academic_year_name,
        term.name as term_name
      FROM a_level_paper_continuous_assessments_scores ca
      JOIN a_level_students s ON ca.student_id = s.id
      JOIN a_level_subject_papers sp ON ca.subject_paper_id = sp.id
      JOIN a_level_subjects sub ON sp.subject_id = sub.id
      JOIN academic_years ay ON ca.academic_year_id = ay.id
      JOIN terms term ON ca.term_id = term.id
      WHERE ca.id = ? AND sub.subject_type = 'Principal'
    `;

    const result = await executeQuery(query, [id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'A-Level Principal continuous assessment not found'
      });
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get A-Level Principal continuous assessment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve A-Level Principal continuous assessment'
    });
  }
});

// Get A-Level Subsidiary continuous assessment by ID
router.get('/continuous/a-level/subsidiary/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT
        ca.*,
        s.admission_number, s.first_name, s.last_name,
        sub.name as subject_name, sub.short_name as subject_short_name,
        sp.paper_number, sp.paper_name,
        ay.name as academic_year_name,
        term.name as term_name
      FROM a_level_paper_continuous_assessments_scores ca
      JOIN a_level_students s ON ca.student_id = s.id
      JOIN a_level_subject_papers sp ON ca.subject_paper_id = sp.id
      JOIN a_level_subjects sub ON sp.subject_id = sub.id
      JOIN academic_years ay ON ca.academic_year_id = ay.id
      JOIN terms term ON ca.term_id = term.id
      WHERE ca.id = ? AND sub.subject_type = 'Subsidiary'
    `;

    const result = await executeQuery(query, [id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'A-Level Subsidiary continuous assessment not found'
      });
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get A-Level Subsidiary continuous assessment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve A-Level Subsidiary continuous assessment'
    });
  }
});

// Create O-Level continuous assessment
router.post('/continuous/o-level', async (req, res) => {
  try {
    const {
      student_id, subject_id, academic_year_id, term_id, ca_number,
      competency_score
    } = req.body;

    // Validate required fields
    if (!student_id || !subject_id || !academic_year_id || !term_id || !ca_number ||
        competency_score === undefined) {
      return res.status(400).json({
        success: false,
        message: 'All required fields must be provided'
      });
    }

    // Validate competency score (0.0 for absent, 0.9-3.0 scale with one decimal place)
    const roundedScore = Math.round(competency_score * 10) / 10;
    if (roundedScore !== 0.0 && (roundedScore < 0.9 || roundedScore > 3.0)) {
      return res.status(400).json({
        success: false,
        message: 'Competency score must be 0.0 (absent) or between 0.9 and 3.0'
      });
    }

    // Check decimal precision
    if (competency_score.toString().includes('.') && competency_score.toString().split('.')[1].length > 1) {
      return res.status(400).json({
        success: false,
        message: 'Competency score must have at most one decimal place'
      });
    }

    const insertQuery = `
      INSERT INTO o_level_subject_continuous_assessments_scores (
        student_id, subject_id, academic_year_id, term_id, ca_number,
        competency_score
      ) VALUES (?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery(insertQuery, [
      student_id, subject_id, academic_year_id, term_id, ca_number,
      competency_score
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get the created assessment with related data
    const newAssessmentQuery = `
      SELECT
        ca.*,
        s.admission_number, s.first_name, s.last_name,
        sub.name as subject_name,
        ay.name as academic_year_name,
        term.name as term_name
      FROM o_level_subject_continuous_assessments_scores ca
      JOIN o_level_students s ON ca.student_id = s.id
      JOIN o_level_subjects sub ON ca.subject_id = sub.id
      JOIN academic_years ay ON ca.academic_year_id = ay.id
      JOIN terms term ON ca.term_id = term.id
      WHERE ca.id = ?
    `;

    const newAssessmentResult = await executeQuery(newAssessmentQuery, [result.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'O-Level continuous assessment created successfully',
      data: newAssessmentResult.data[0]
    });

  } catch (error) {
    console.error('Create O-Level continuous assessment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create O-Level continuous assessment'
    });
  }
});

// Create A-Level continuous assessment (works for both Principal and Subsidiary)
router.post('/continuous/a-level', async (req, res) => {
  try {
    const {
      student_id, subject_paper_id, academic_year_id, term_id, ca_number,
      competency_score
    } = req.body;

    // Validate required fields
    if (!student_id || !subject_paper_id || !academic_year_id || !term_id || !ca_number ||
        competency_score === undefined) {
      return res.status(400).json({
        success: false,
        message: 'All required fields must be provided: student_id, subject_paper_id, academic_year_id, term_id, ca_number, competency_score'
      });
    }

    // Validate competency score (0-3 scale)
    if (competency_score < 0 || competency_score > 3) {
      return res.status(400).json({
        success: false,
        message: 'Competency score must be between 0 and 3'
      });
    }

    const insertQuery = `
      INSERT INTO a_level_paper_continuous_assessments_scores (
        student_id, subject_paper_id, academic_year_id, term_id, ca_number,
        competency_score
      ) VALUES (?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery(insertQuery, [
      student_id, subject_paper_id, academic_year_id, term_id, ca_number,
      competency_score
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get the created assessment with related data
    const newAssessmentQuery = `
      SELECT
        ca.*,
        s.admission_number, s.first_name, s.last_name,
        sub.name as subject_name, sub.short_name as subject_short_name,
        sp.paper_number, sp.paper_name,
        ay.name as academic_year_name,
        term.name as term_name
      FROM a_level_paper_continuous_assessments_scores ca
      JOIN a_level_students s ON ca.student_id = s.id
      JOIN a_level_subject_papers sp ON ca.subject_paper_id = sp.id
      JOIN a_level_subjects sub ON sp.subject_id = sub.id
      JOIN academic_years ay ON ca.academic_year_id = ay.id
      JOIN terms term ON ca.term_id = term.id
      WHERE ca.id = ?
    `;

    const newAssessmentResult = await executeQuery(newAssessmentQuery, [result.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'A-Level continuous assessment created successfully',
      data: newAssessmentResult.data[0]
    });

  } catch (error) {
    console.error('Create A-Level continuous assessment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create A-Level continuous assessment'
    });
  }
});

// Update and delete operations for A-Level assessments
router.put('/continuous/a-level/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { competency_score } = req.body;

    const updateQuery = `
      UPDATE a_level_paper_continuous_assessments_scores
      SET competency_score = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [competency_score, id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'A-Level continuous assessment not found'
      });
    }

    res.json({
      success: true,
      message: 'A-Level continuous assessment updated successfully'
    });

  } catch (error) {
    console.error('Update A-Level continuous assessment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update A-Level continuous assessment'
    });
  }
});

router.delete('/continuous/a-level/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const deleteQuery = 'DELETE FROM a_level_paper_continuous_assessments_scores WHERE id = ?';
    const result = await executeQuery(deleteQuery, [id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'A-Level continuous assessment not found'
      });
    }

    res.json({
      success: true,
      message: 'A-Level continuous assessment deleted successfully'
    });

  } catch (error) {
    console.error('Delete A-Level continuous assessment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete A-Level continuous assessment'
    });
  }
});

// =============================================
// EXAMINATIONS ROUTES
// =============================================

// Get O-Level examinations with filters
router.get('/examinations/o-level', async (req, res) => {
  try {
    const { class_id, subject_id, term_id, academic_year_id } = req.query;

    let query = `
      SELECT
        e.*,
        c.name as class_name,
        sub.name as subject_name, sub.short_name as subject_short_name,
        ay.name as academic_year_name,
        term.name as term_name,
        t.first_name as teacher_first_name, t.last_name as teacher_last_name
      FROM o_level_term_examinations e
      JOIN classes c ON e.class_id = c.id
      JOIN o_level_subjects sub ON e.subject_id = sub.id
      JOIN academic_years ay ON e.academic_year_id = ay.id
      JOIN terms term ON e.term_id = term.id
      LEFT JOIN teachers t ON e.teacher_id = t.id
      WHERE 1=1
    `;

    let params = [];

    if (class_id) {
      query += ' AND e.class_id = ?';
      params.push(class_id);
    }

    if (subject_id) {
      query += ' AND e.subject_id = ?';
      params.push(subject_id);
    }

    if (term_id) {
      query += ' AND e.term_id = ?';
      params.push(term_id);
    }

    if (academic_year_id) {
      query += ' AND e.academic_year_id = ?';
      params.push(academic_year_id);
    }

    query += ' ORDER BY e.created_at DESC, c.name, sub.name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get O-Level examinations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve O-Level examinations'
    });
  }
});

// Get A-Level examinations with filters
router.get('/examinations/a-level', async (req, res) => {
  try {
    const { class_id, subject_id, subject_paper_id, term_id, academic_year_id } = req.query;

    let query = `
      SELECT
        e.*,
        c.name as class_name,
        sub.name as subject_name, sub.short_name as subject_short_name,
        sp.paper_number, sp.paper_name,
        ay.name as academic_year_name,
        term.name as term_name,
        et.name as exam_type_name
      FROM a_level_paper_examinations e
      JOIN classes c ON e.class_id = c.id
      JOIN a_level_subject_papers sp ON e.subject_paper_id = sp.id
      JOIN a_level_subjects sub ON sp.subject_id = sub.id
      JOIN academic_years ay ON e.academic_year_id = ay.id
      JOIN terms term ON e.term_id = term.id
      JOIN exam_types et ON e.exam_type_id = et.id
      WHERE 1=1
    `;

    let params = [];

    if (class_id) {
      query += ' AND e.class_id = ?';
      params.push(class_id);
    }

    if (subject_id) {
      query += ' AND sp.subject_id = ?';
      params.push(subject_id);
    }

    if (subject_paper_id) {
      query += ' AND e.subject_paper_id = ?';
      params.push(subject_paper_id);
    }

    if (term_id) {
      query += ' AND e.term_id = ?';
      params.push(term_id);
    }

    if (academic_year_id) {
      query += ' AND e.academic_year_id = ?';
      params.push(academic_year_id);
    }

    query += ' ORDER BY e.created_at DESC, c.name, sub.name, sp.paper_number';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get A-Level examinations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve A-Level examinations'
    });
  }
});

// =============================================
// ASSESSMENT STATISTICS
// =============================================

// Get assessment statistics
router.get('/stats/overview', async (req, res) => {
  try {
    const { academic_year_id, term_id } = req.query;

    let whereClause = 'WHERE 1=1';
    let params = [];

    if (academic_year_id) {
      whereClause += ' AND academic_year_id = ?';
      params.push(academic_year_id);
    }

    if (term_id) {
      whereClause += ' AND term_id = ?';
      params.push(term_id);
    }

    // Get O-Level statistics
    const oLevelStatsQuery = `
      SELECT
        COUNT(*) as total_assessments,
        COUNT(CASE WHEN competency_score >= 2.5 THEN 1 END) as excellent_performance,
        COUNT(CASE WHEN competency_score >= 2.0 AND competency_score < 2.5 THEN 1 END) as good_performance,
        COUNT(CASE WHEN competency_score >= 1.5 AND competency_score < 2.0 THEN 1 END) as satisfactory_performance,
        COUNT(CASE WHEN competency_score < 1.5 THEN 1 END) as needs_improvement,
        AVG(competency_score) as average_score,
        COUNT(DISTINCT student_id) as students_assessed,
        COUNT(DISTINCT subject_id) as subjects_assessed,
        'O-Level' as level_type
      FROM o_level_subject_continuous_assessments_scores
      ${whereClause}
    `;

    // Get A-Level statistics
    const aLevelStatsQuery = `
      SELECT
        COUNT(*) as total_assessments,
        COUNT(CASE WHEN competency_score >= 2.5 THEN 1 END) as excellent_performance,
        COUNT(CASE WHEN competency_score >= 2.0 AND competency_score < 2.5 THEN 1 END) as good_performance,
        COUNT(CASE WHEN competency_score >= 1.5 AND competency_score < 2.0 THEN 1 END) as satisfactory_performance,
        COUNT(CASE WHEN competency_score < 1.5 THEN 1 END) as needs_improvement,
        AVG(competency_score) as average_score,
        COUNT(DISTINCT student_id) as students_assessed,
        COUNT(DISTINCT subject_paper_id) as subjects_assessed,
        'A-Level' as education_level_name
      FROM a_level_paper_continuous_assessments_scores
      ${whereClause}
    `;

    // Execute both queries
    const [oLevelResult, aLevelResult] = await Promise.all([
      executeQuery(oLevelStatsQuery, params),
      executeQuery(aLevelStatsQuery, params)
    ]);

    // Combine results
    const combinedStats = {
      o_level: oLevelResult.success ? oLevelResult.data[0] : null,
      a_level: aLevelResult.success ? aLevelResult.data[0] : null,
      total_assessments: (oLevelResult.success ? oLevelResult.data[0].total_assessments : 0) +
                        (aLevelResult.success ? aLevelResult.data[0].total_assessments : 0),
      total_students_assessed: new Set([
        ...(oLevelResult.success ? [oLevelResult.data[0].students_assessed] : []),
        ...(aLevelResult.success ? [aLevelResult.data[0].students_assessed] : [])
      ]).size
    };

    res.json({
      success: true,
      data: combinedStats
    });

  } catch (error) {
    console.error('Get assessment stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve assessment statistics'
    });
  }
});

module.exports = router;
