# SmartReport - Effortless Assessment Management

## 🎯 Overview

SmartReport is a powerful desktop application built to streamline academic performance tracking and reporting for Uganda Secondary Schools. Designed with simplicity and efficiency in mind, SmartReport supports both O-Level and A-Level education systems and is tailored to meet NCDC standards.

## ✨ Features

- **Student Management**: Complete student records and enrollment tracking
- **Academic Performance**: Grade management for O-Level and A-Level systems
- **Report Generation**: Automated report cards and academic summaries
- **User Authentication**: Secure login system with role-based access
- **Data Import/Export**: CSV support for bulk data operations
- **PDF Generation**: Professional report and document generation
- **Database Management**: MySQL/MariaDB integration with automated setup

## 🛠️ Technology Stack

- **Frontend**: Electron with HTML/CSS/JavaScript
- **Backend**: Node.js with Express.js
- **Database**: MySQL/MariaDB
- **Authentication**: JWT tokens with bcrypt encryption
- **File Processing**: CSV parsing and PDF generation
- **Development**: Nodemon for hot reloading

## 📋 Prerequisites

Before installing SmartReport, ensure you have:

- **Node.js** (v16 or higher)
- **MySQL** or **MariaDB** server
- **Git** (for cloning the repository)

## 🚀 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd smartreport
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Database Setup**
   - Ensure MySQL/MariaDB is running
   - Create a database for SmartReport
   - Note your database credentials

4. **Environment Configuration**
   ```bash
   npm run setup
   ```
   This will create a `.env` file. Update it with your database credentials:
   ```env
   DB_HOST=localhost
   DB_USER=your_username
   DB_PASSWORD=your_password
   DB_NAME=smartreport_db
   DB_PORT=3306
   ```

5. **Initialize Database**
   ```bash
   npm run init-db
   ```

## 🎮 Usage

### Development Mode
```bash
# Start the application in development mode
npm run dev

# Or start the server separately
npm run server:dev
```

### Production Mode
```bash
# Start the application
npm start

# Or start the server separately
npm run server
```

### Database Management
```bash
# Initialize database (first time setup)
npm run init-db

# Reset database (WARNING: This will delete all data)
npm run reset-db
```

## 📁 Project Structure

```
smartreport/
├── src/
│   ├── main.js              # Electron main process
│   ├── renderer/            # Frontend files (HTML/CSS/JS)
│   ├── server/              # Express.js backend
│   ├── database/            # Database configuration and initialization
│   ├── assets/              # Static assets (images, icons)
│   ├── backups/             # Database backup files
│   └── temp/                # Temporary files
├── package.json             # Project dependencies and scripts
├── setup.js                 # Initial setup script
└── README.md               # This file
```

## 🔧 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run setup` | Initial system setup and configuration |
| `npm start` | Start the application in production mode |
| `npm run dev` | Start the application in development mode |
| `npm run server` | Start only the backend server |
| `npm run server:dev` | Start the backend server with hot reload |
| `npm run init-db` | Initialize the database with required tables |
| `npm run reset-db` | Reset the database (⚠️ Deletes all data) |
| `npm run build` | Build the application for distribution |

## 🎓 Uganda Education System Support

SmartReport is specifically designed for the Uganda education system:

- **O-Level (Ordinary Level)**: 4-year secondary education program
- **A-Level (Advanced Level)**: 2-year pre-university program
- **UNEB Integration**: Compatible with Uganda National Examinations Board requirements
- **Subject Management**: Supports Uganda curriculum subjects and grading systems

## 🔐 Security Features

- **Password Encryption**: bcrypt hashing for secure password storage
- **JWT Authentication**: Token-based authentication system
- **Role-based Access**: Different permission levels for administrators and teachers
- **Data Validation**: Input sanitization and validation

## 📊 Data Management

- **CSV Import/Export**: Bulk student data operations
- **PDF Reports**: Professional report generation
- **Database Backups**: Automated backup functionality
- **Data Integrity**: Foreign key constraints and data validation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Authors

**SmartReport Development Team**

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation in the `/docs` folder (if available)

## 🔄 Version History

- **v1.0.0** - Initial release with core functionality

---

**Note**: This application is specifically designed for Uganda Secondary Schools and follows the Uganda National Curriculum and UNEB requirements.