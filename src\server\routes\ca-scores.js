const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Get students for CA score entry 
router.get('/students', async (req, res) => {
  try {
    const { class_id, subject_id, academic_year_id, term_id, ca_number, subject_paper_id } = req.query;

    // Validate required parameters
    if (!class_id || !subject_id || !academic_year_id || !term_id || !ca_number) {
      return res.status(400).json({
        success: false,
        message: 'Class ID, subject ID, academic year ID, term ID, and CA number are required'
      });
    }

    // First, determine if this is O-Level or A-Level based on class
    const classLevelQuery = `
      SELECT el.name as level_name
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      WHERE c.id = ?
    `;

    const classLevelResult = await executeQuery(classLevelQuery, [class_id]);

    if (!classLevelResult.success || classLevelResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class not found'
      });
    }

    const isOLevel = classLevelResult.data[0].level_name === 'O-Level';
    let studentsQuery, caScoresQuery;
    let studentsResult, caScoresResult;

    if (isOLevel) {
      // O-Level students query
      studentsQuery = `
        SELECT
          s.id, s.admission_number, s.first_name, s.last_name,
          s.gender, s.status, s.enrollment_date,
          c.name as class_name, cl.name as class_level_name,
          st.name as stream_name,
          ay.name as academic_year_name, t.name as term_name
        FROM o_level_students s
        JOIN classes c ON s.current_class_id = c.id
        JOIN class_levels cl ON c.class_level_id = cl.id
        LEFT JOIN streams st ON s.stream_id = st.id
        JOIN academic_years ay ON s.current_academic_year_id = ay.id
        JOIN terms t ON s.current_term_id = t.id
        WHERE s.current_class_id = ? AND s.current_academic_year_id = ? AND s.current_term_id = ?
          AND s.status = 'active'
        ORDER BY s.admission_number
      `;

      // Check for existing CA scores
      caScoresQuery = `
        SELECT ca.student_id, ca.competency_score
        FROM o_level_subject_continuous_assessments_scores ca
        WHERE ca.subject_id = ? AND ca.academic_year_id = ? AND ca.term_id = ? AND ca.ca_number = ?
      `;

      [studentsResult, caScoresResult] = await Promise.all([
        executeQuery(studentsQuery, [class_id, academic_year_id, term_id]),
        executeQuery(caScoresQuery, [subject_id, academic_year_id, term_id, ca_number])
      ]);
    } else {
      // A-Level students query
      if (!subject_paper_id) {
        return res.status(400).json({
          success: false,
          message: 'Subject paper ID is required for A-Level CA scores'
        });
      }

      studentsQuery = `
        SELECT
          s.id, s.admission_number, s.first_name, s.last_name,
          s.gender, s.status, s.registration_date as enrollment_date,
          c.name as class_name, cl.name as class_level_name,
          st.name as stream_name,
          ay.name as academic_year_name, t.name as term_name
        FROM a_level_students s
        JOIN classes c ON s.current_class_id = c.id
        JOIN class_levels cl ON c.class_level_id = cl.id
        LEFT JOIN streams st ON s.stream_id = st.id
        JOIN academic_years ay ON s.current_academic_year_id = ay.id
        JOIN terms t ON s.current_term_id = t.id
        WHERE s.current_class_id = ? AND s.current_academic_year_id = ? AND s.current_term_id = ?
          AND s.status = 'active'
        ORDER BY s.admission_number
      `;

      // Check for existing CA scores for the specific paper
      caScoresQuery = `
        SELECT ca.student_id, ca.competency_score
        FROM a_level_paper_continuous_assessments_scores ca
        WHERE ca.subject_paper_id = ? AND ca.academic_year_id = ? AND ca.term_id = ? AND ca.ca_number = ?
      `;

      [studentsResult, caScoresResult] = await Promise.all([
        executeQuery(studentsQuery, [class_id, academic_year_id, term_id]),
        executeQuery(caScoresQuery, [subject_paper_id, academic_year_id, term_id, ca_number])
      ]);
    }

    if (!studentsResult.success) {
      throw new Error(studentsResult.error);
    }

    if (!caScoresResult.success) {
      throw new Error(caScoresResult.error);
    }

    // Create a map of existing CA scores for quick lookup
    const existingScores = {};
    caScoresResult.data.forEach(score => {
      existingScores[score.student_id] = score.competency_score;
    });

    // Merge students with their existing CA scores
    const studentsWithData = studentsResult.data.map(student => ({
      ...student,
      ca_score: existingScores[student.id] || null,
      has_existing_score: existingScores[student.id] !== undefined
    }));

    res.json({
      success: true,
      data: studentsWithData,
      message: `${studentsWithData.length} students retrieved for CA score entry`
    });

  } catch (error) {
    console.error('Get students for CA scores error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve students for CA score entry',
      error: error.message
    });
  }
});

// Create/Update CA scores
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { scores } = req.body;

    if (!scores || !Array.isArray(scores) || scores.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Scores array is required and must not be empty'
      });
    }

    // Validate that all scores have the same academic context
    const firstScore = scores[0];
    const { academic_year_id, term_id, ca_number } = firstScore;

    if (!academic_year_id || !term_id || !ca_number) {
      return res.status(400).json({
        success: false,
        message: 'Academic year ID, term ID, and CA number are required for all scores'
      });
    }

    // Determine if this is O-Level or A-Level based on the presence of subject_paper_id
    const isOLevel = !firstScore.subject_paper_id;
    const errors = [];
    const successfulInserts = [];

    // Process each score
    for (const score of scores) {
      try {
        const {
          student_id, subject_id, subject_paper_id, competency_score
        } = score;

        // Validate competency_score (must be between 0.0 and 3.0, or 0 for absent)
        if (competency_score !== null && (competency_score < 0 || competency_score > 3)) {
          errors.push(`Invalid competency score for student ${student_id}: must be between 0.0 and 3.0`);
          continue;
        }

        // Use the appropriate table based on level
        let tableName, checkQuery, insertQuery, updateQuery;

        if (isOLevel) {
          tableName = 'o_level_subject_continuous_assessments_scores';
          // Check using the exact unique constraint: uk_student_subject_term_ca (student_id, subject_id, term_id, ca_number)
          checkQuery = `
            SELECT id FROM ${tableName}
            WHERE student_id = ? AND subject_id = ? AND term_id = ? AND ca_number = ?
          `;
          insertQuery = `
            INSERT INTO ${tableName} (student_id, subject_id, academic_year_id, term_id, ca_number, competency_score)
            VALUES (?, ?, ?, ?, ?, ?)
          `;
          updateQuery = `
            UPDATE ${tableName} SET competency_score = ?, updated_at = NOW()
            WHERE student_id = ? AND subject_id = ? AND term_id = ? AND ca_number = ?
          `;
        } else {
          tableName = 'a_level_paper_continuous_assessments_scores';
          // Check using the exact unique constraint: uk_student_subject_paper_term_ca (student_id, subject_paper_id, term_id, ca_number)
          checkQuery = `
            SELECT id FROM ${tableName}
            WHERE student_id = ? AND subject_paper_id = ? AND term_id = ? AND ca_number = ?
          `;
          insertQuery = `
            INSERT INTO ${tableName} (student_id, subject_paper_id, academic_year_id, term_id, ca_number, competency_score)
            VALUES (?, ?, ?, ?, ?, ?)
          `;
          updateQuery = `
            UPDATE ${tableName} SET competency_score = ?, updated_at = NOW()
            WHERE student_id = ? AND subject_paper_id = ? AND term_id = ? AND ca_number = ?
          `;
        }

        // Check if record exists using the unique constraint fields
        const checkParams = isOLevel
          ? [student_id, subject_id, term_id, ca_number]
          : [student_id, subject_paper_id, term_id, ca_number];

        const existingResult = await executeQuery(checkQuery, checkParams);

        if (!existingResult.success) {
          errors.push(`Database error for student ${student_id}: ${existingResult.error}`);
          continue;
        }

        let result;
        if (existingResult.data.length > 0) {
          // Update existing record using the unique constraint fields
          const updateParams = isOLevel
            ? [competency_score, student_id, subject_id, term_id, ca_number]
            : [competency_score, student_id, subject_paper_id, term_id, ca_number];

          result = await executeQuery(updateQuery, updateParams);
        } else {
          // Insert new record (still need academic_year_id for the insert)
          const insertParams = isOLevel
            ? [student_id, subject_id, academic_year_id, term_id, ca_number, competency_score]
            : [student_id, subject_paper_id, academic_year_id, term_id, ca_number, competency_score];

          result = await executeQuery(insertQuery, insertParams);
        }

        if (result.success) {
          successfulInserts.push(student_id);
        } else {
          errors.push(`Failed to save score for student ${student_id}: ${result.error}`);
        }

      } catch (scoreError) {
        errors.push(`Error processing student ${score.student_id}: ${scoreError.message}`);
      }
    }

    // Return response
    const response = {
      success: errors.length === 0,
      message: errors.length === 0 
        ? `${successfulInserts.length} CA scores saved successfully`
        : `${successfulInserts.length} scores saved, ${errors.length} errors occurred`,
      successful_saves: successfulInserts.length,
      errors: errors
    };

    res.status(errors.length === 0 ? 200 : 207).json(response);

  } catch (error) {
    console.error('Save CA scores error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save CA scores',
      error: error.message
    });
  }
});

module.exports = router;
