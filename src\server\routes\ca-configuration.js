const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Get CA configurations
router.get('/', async (req, res) => {
  try {
    const { academic_year_id, term_id, subject_id, class_id } = req.query;

    // Query both O-Level and A-Level configurations and combine results
    let query = `
      SELECT
        occ.id,
        occ.class_id,
        occ.subject_id,
        NULL as subject_paper_id,
        'o_level' as education_level,
        occ.academic_year_id,
        occ.term_id,
        occ.total_cas,
        occ.created_at,
        occ.updated_at,
        c.name as class_name,
        ay.name as academic_year_name,
        t.name as term_name,
        os.name as subject_name,
        os.short_name as subject_short_name,
        os.subject_type as subject_category,
        'o_level' as subject_level,
        NULL as paper_number,
        NULL as paper_name
      FROM o_level_ca_configuration occ
      JOIN classes c ON occ.class_id = c.id
      JOIN academic_years ay ON occ.academic_year_id = ay.id
      JOIN terms t ON occ.term_id = t.id
      JOIN o_level_subjects os ON occ.subject_id = os.id
      WHERE 1=1
    `;

    // Add O-Level specific filters
    const params = [];
    if (academic_year_id) {
      query += ' AND occ.academic_year_id = ?';
      params.push(academic_year_id);
    }
    if (term_id) {
      query += ' AND occ.term_id = ?';
      params.push(term_id);
    }
    if (subject_id) {
      query += ' AND occ.subject_id = ?';
      params.push(subject_id);
    }
    if (class_id) {
      query += ' AND occ.class_id = ?';
      params.push(class_id);
    }

    // Add UNION for A-Level configurations
    query += `
      UNION ALL
      SELECT
        acc.id,
        acc.class_id,
        acc.subject_id,
        acc.subject_paper_id,
        'a_level' as education_level,
        acc.academic_year_id,
        acc.term_id,
        acc.total_cas,
        acc.created_at,
        acc.updated_at,
        c.name as class_name,
        ay.name as academic_year_name,
        t.name as term_name,
        als.name as subject_name,
        als.short_name as subject_short_name,
        als.subject_type as subject_category,
        'a_level' as subject_level,
        asp.paper_number,
        asp.paper_name
      FROM a_level_ca_configuration acc
      JOIN classes c ON acc.class_id = c.id
      JOIN academic_years ay ON acc.academic_year_id = ay.id
      JOIN terms t ON acc.term_id = t.id
      JOIN a_level_subjects als ON acc.subject_id = als.id
      JOIN a_level_subject_papers asp ON acc.subject_paper_id = asp.id
      WHERE 1=1
    `;

    // Add A-Level specific filters (duplicate the params for the UNION)
    if (academic_year_id) {
      query += ' AND acc.academic_year_id = ?';
      params.push(academic_year_id);
    }
    if (term_id) {
      query += ' AND acc.term_id = ?';
      params.push(term_id);
    }
    if (subject_id) {
      query += ' AND acc.subject_id = ?';
      params.push(subject_id);
    }
    if (class_id) {
      query += ' AND acc.class_id = ?';
      params.push(class_id);
    }

    query += ' ORDER BY class_name, subject_level, subject_name, paper_number';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get CA configurations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch CA configurations'
    });
  }
});

// Create or update CA configuration
router.post('/', authenticateToken, async (req, res) => {
  try {
    const {
      class_id, subject_id, subject_paper_id, education_level, academic_year_id, term_id, total_cas
    } = req.body;

    // Validate required fields
    if (!class_id || !subject_id || !academic_year_id || !term_id || !total_cas) {
      return res.status(400).json({
        success: false,
        message: 'All required fields must be provided (class_id, subject_id, academic_year_id, term_id, total_cas)'
      });
    }

    // For A-Level, subject_paper_id is required
    if (education_level === 'a_level' && !subject_paper_id) {
      return res.status(400).json({
        success: false,
        message: 'subject_paper_id is required for A-Level configurations'
      });
    }

    // Validate total_cas range
    if (total_cas < 1 || total_cas > 6) {
      return res.status(400).json({
        success: false,
        message: 'Total CAs must be between 1 and 6'
      });
    }

    // Validate that subject_id exists in either o_level_subjects or a_level_subjects
    const subjectValidationQuery = `
      SELECT 'o_level' as level FROM o_level_subjects WHERE id = ?
      UNION
      SELECT 'a_level' as level FROM a_level_subjects WHERE id = ?
    `;

    const subjectValidation = await executeQuery(subjectValidationQuery, [subject_id, subject_id]);

    if (!subjectValidation.success || subjectValidation.data.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid subject_id provided'
      });
    }

    // Check if configuration already exists for this class-subject combination
    let checkQuery, checkParams;

    if (education_level === 'o_level') {
      checkQuery = `
        SELECT id FROM o_level_ca_configuration
        WHERE class_id = ? AND subject_id = ? AND academic_year_id = ? AND term_id = ?
      `;
      checkParams = [class_id, subject_id, academic_year_id, term_id];
    } else {
      checkQuery = `
        SELECT id FROM a_level_ca_configuration
        WHERE class_id = ? AND subject_paper_id = ? AND academic_year_id = ? AND term_id = ?
      `;
      checkParams = [class_id, subject_paper_id, academic_year_id, term_id];
    }

    const checkResult = await executeQuery(checkQuery, checkParams);

    if (!checkResult.success) {
      throw new Error(checkResult.error);
    }

    let result;
    if (checkResult.data.length > 0) {
      // Update existing configuration
      let updateQuery;

      if (education_level === 'o_level') {
        updateQuery = `
          UPDATE o_level_ca_configuration SET
            total_cas = ?, updated_at = NOW()
          WHERE id = ?
        `;
      } else {
        updateQuery = `
          UPDATE a_level_ca_configuration SET
            total_cas = ?, updated_at = NOW()
          WHERE id = ?
        `;
      }

      result = await executeQuery(updateQuery, [
        total_cas, checkResult.data[0].id
      ]);
    } else {
      // Create new configuration
      let insertQuery, insertParams;

      if (education_level === 'o_level') {
        insertQuery = `
          INSERT INTO o_level_ca_configuration (
            class_id, subject_id, academic_year_id, term_id, total_cas
          ) VALUES (?, ?, ?, ?, ?)
        `;
        insertParams = [class_id, subject_id, academic_year_id, term_id, total_cas];
      } else {
        insertQuery = `
          INSERT INTO a_level_ca_configuration (
            class_id, subject_id, subject_paper_id, academic_year_id, term_id, total_cas
          ) VALUES (?, ?, ?, ?, ?, ?)
        `;
        insertParams = [class_id, subject_id, subject_paper_id, academic_year_id, term_id, total_cas];
      }

      result = await executeQuery(insertQuery, insertParams);
    }
    
    if (!result.success) {
      throw new Error(result.error);
    }
    
    res.json({
      success: true,
      message: 'CA configuration saved successfully'
    });
    
  } catch (error) {
    console.error('Save CA configuration error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save CA configuration'
    });
  }
});

// Update CA configuration
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { total_cas, education_level } = req.body;

    // Validate required fields
    if (!total_cas || !education_level) {
      return res.status(400).json({
        success: false,
        message: 'Total CAs and education level are required'
      });
    }

    // Validate total_cas range
    if (total_cas < 1 || total_cas > 6) {
      return res.status(400).json({
        success: false,
        message: 'Total CAs must be between 1 and 6'
      });
    }

    // Check if configuration exists in the appropriate table
    let checkQuery;
    if (education_level === 'o_level') {
      checkQuery = 'SELECT id FROM o_level_ca_configuration WHERE id = ?';
    } else {
      checkQuery = 'SELECT id FROM a_level_ca_configuration WHERE id = ?';
    }

    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'CA configuration not found'
      });
    }

    // Update configuration in the appropriate table
    let updateQuery;
    if (education_level === 'o_level') {
      updateQuery = `
        UPDATE o_level_ca_configuration SET
          total_cas = ?, updated_at = NOW()
        WHERE id = ?
      `;
    } else {
      updateQuery = `
        UPDATE a_level_ca_configuration SET
          total_cas = ?, updated_at = NOW()
        WHERE id = ?
      `;
    }

    const result = await executeQuery(updateQuery, [total_cas, id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      message: 'CA configuration updated successfully'
    });

  } catch (error) {
    console.error('Update CA configuration error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update CA configuration'
    });
  }
});

// Delete CA configuration
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Try to find and delete from O-Level table first
    const oLevelCheckQuery = 'SELECT id FROM o_level_ca_configuration WHERE id = ?';
    const oLevelCheckResult = await executeQuery(oLevelCheckQuery, [id]);

    let result;
    if (oLevelCheckResult.success && oLevelCheckResult.data.length > 0) {
      // Delete from O-Level table
      const deleteQuery = 'DELETE FROM o_level_ca_configuration WHERE id = ?';
      result = await executeQuery(deleteQuery, [id]);
    } else {
      // Try A-Level table
      const aLevelCheckQuery = 'SELECT id FROM a_level_ca_configuration WHERE id = ?';
      const aLevelCheckResult = await executeQuery(aLevelCheckQuery, [id]);

      if (aLevelCheckResult.success && aLevelCheckResult.data.length > 0) {
        // Delete from A-Level table
        const deleteQuery = 'DELETE FROM a_level_ca_configuration WHERE id = ?';
        result = await executeQuery(deleteQuery, [id]);
      } else {
        return res.status(404).json({
          success: false,
          message: 'CA configuration not found'
        });
      }
    }
    
    if (!result.success) {
      throw new Error(result.error);
    }
    
    res.json({
      success: true,
      message: 'CA configuration deleted successfully'
    });
    
  } catch (error) {
    console.error('Delete CA configuration error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete CA configuration'
    });
  }
});

module.exports = router;
