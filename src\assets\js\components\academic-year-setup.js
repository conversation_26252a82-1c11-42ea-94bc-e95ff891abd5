/**
 * Academic Year Setup Component
 * Dedicated component for initial academic year and terms setup
 * Used when admin first logs in and needs to configure the system
 */

class AcademicYearSetupComponent {
  constructor() {
    console.log('🔧 AcademicYearSetupComponent constructor called');
    this.currentStep = 1;
    this.totalSteps = 3;
    this.academicYearData = {};
    this.termsData = [];
    this.isSubmitting = false;
    this.availableYears = [];
  }

  // Render the main component using SR design system
  render() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Academic Year Setup',
          'Configure your school\'s academic year and terms to get started with the system'
        )}

        <!-- Progress Steps -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="flex items-center justify-center">
            <div class="flex items-center space-x-8">
              <div class="flex items-center ${this.currentStep >= 1 ? 'text-primary-600' : 'text-gray-400'}">
                <div class="flex items-center justify-center w-10 h-10 rounded-full border-2 ${this.currentStep >= 1 ? 'border-primary-600 bg-primary-50' : 'border-gray-300'} mr-3">
                  ${this.currentStep > 1 ? '<i class="fas fa-check text-primary-600"></i>' : '<span class="font-semibold">1</span>'}
                </div>
                <span class="font-medium">Academic Year</span>
              </div>
              <div class="w-16 h-0.5 ${this.currentStep >= 2 ? 'bg-primary-600' : 'bg-gray-300'}"></div>
              <div class="flex items-center ${this.currentStep >= 2 ? 'text-primary-600' : 'text-gray-400'}">
                <div class="flex items-center justify-center w-10 h-10 rounded-full border-2 ${this.currentStep >= 2 ? 'border-primary-600 bg-primary-50' : 'border-gray-300'} mr-3">
                  ${this.currentStep > 2 ? '<i class="fas fa-check text-primary-600"></i>' : '<span class="font-semibold">2</span>'}
                </div>
                <span class="font-medium">Terms Setup</span>
              </div>
              <div class="w-16 h-0.5 ${this.currentStep >= 3 ? 'bg-primary-600' : 'bg-gray-300'}"></div>
              <div class="flex items-center ${this.currentStep >= 3 ? 'text-primary-600' : 'text-gray-400'}">
                <div class="flex items-center justify-center w-10 h-10 rounded-full border-2 ${this.currentStep >= 3 ? 'border-primary-600 bg-primary-50' : 'border-gray-300'} mr-3">
                  ${this.currentStep > 3 ? '<i class="fas fa-check text-primary-600"></i>' : '<span class="font-semibold">3</span>'}
                </div>
                <span class="font-medium">Review & Save</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Main Content -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          ${this.renderCurrentStep()}
        </div>

        <!-- Navigation Actions -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="flex items-center justify-between">
            <div>
              ${SRDesignSystem.forms.button('prev-step-btn', 'Previous', 'secondary', {
                icon: 'fas fa-arrow-left',
                disabled: this.currentStep === 1,
                onclick: 'window.academicYearSetup.prevStep()'
              })}
            </div>
            <div>
              ${this.currentStep < this.totalSteps ?
                SRDesignSystem.forms.button('next-step-btn', 'Next', 'primary', {
                  icon: 'fas fa-arrow-right',
                  onclick: 'window.academicYearSetup.nextStep()'
                }) :
                SRDesignSystem.forms.button('save-setup-btn', this.isSubmitting ? 'Saving...' : 'Save Configuration', 'success', {
                  icon: this.isSubmitting ? 'fas fa-spinner fa-spin' : 'fas fa-save',
                  disabled: this.isSubmitting,
                  onclick: 'window.academicYearSetup.saveSetup()'
                })
              }
            </div>
          </div>
        </div>
      </div>
    `;
  }

  // Render current step content
  renderCurrentStep() {
    switch (this.currentStep) {
      case 1:
        return this.renderAcademicYearStep();
      case 2:
        return this.renderTermsStep();
      case 3:
        return this.renderReviewStep();
      default:
        return this.renderAcademicYearStep();
    }
  }

  // Step 1: Academic Year Configuration
  renderAcademicYearStep() {
    // Ensure years are loaded
    if (!this.availableYears || this.availableYears.length === 0) {
      this.loadAvailableYears();
    }

    return `
      <div class="space-y-6">
        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Academic Year Information</h3>
          <p class="text-gray-600">
            Set up your school's academic year. This will be the active academic year for the system.
          </p>
        </div>

        <form id="academic-year-setup-form" class="space-y-6">
          <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap}">
            ${SRDesignSystem.forms.select('name', 'Academic Year', [
              { value: '', label: 'Select Academic Year' },
              ...this.getYearOptions()
            ], this.academicYearData.name || '', {
              required: true,
              helpText: 'Select the academic year'
            })}

            ${SRDesignSystem.forms.input('start_date', 'Start Date', this.academicYearData.start_date || '', {
              type: 'date',
              required: true
            })}

            ${SRDesignSystem.forms.input('end_date', 'End Date', this.academicYearData.end_date || '', {
              type: 'date',
              required: true
            })}
          </div>

          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-start">
              <i class="fas fa-info-circle text-blue-600 mt-0.5 mr-3"></i>
              <div class="text-sm text-blue-800">
                <strong>Note:</strong> This academic year will be automatically set as active when created.
              </div>
            </div>
          </div>
        </form>
      </div>
    `;
  }

  // Step 2: Terms Configuration
  renderTermsStep() {
    return `
      <div class="space-y-6">
        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Terms Configuration</h3>
          <p class="text-gray-600">
            Set up the terms for your academic year. Configure the start and end dates for each term.
          </p>
        </div>

        <form id="academic-year-setup-form" class="space-y-6">
          <!-- Hidden fields to preserve academic year data -->
          <input type="hidden" name="name" value="${this.academicYearData.name || ''}">
          <input type="hidden" name="start_date" value="${this.academicYearData.start_date || ''}">
          <input type="hidden" name="end_date" value="${this.academicYearData.end_date || ''}">

          <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap}">
            <!-- Term 1 -->
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
              <div class="mb-4">
                <h4 class="text-lg font-semibold text-gray-900">Term One</h4>
              </div>
              <div class="space-y-4">
                ${SRDesignSystem.forms.input('term1_start', 'Start Date', this.academicYearData.term1_start || '', {
                  type: 'date',
                  required: true
                })}
                ${SRDesignSystem.forms.input('term1_end', 'End Date', this.academicYearData.term1_end || '', {
                  type: 'date',
                  required: true
                })}
              </div>
            </div>

            <!-- Term 2 -->
            <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-6 border border-green-200">
              <div class="mb-4">
                <h4 class="text-lg font-semibold text-gray-900">Term Two</h4>
              </div>
              <div class="space-y-4">
                ${SRDesignSystem.forms.input('term2_start', 'Start Date', this.academicYearData.term2_start || '', {
                  type: 'date',
                  required: true
                })}
                ${SRDesignSystem.forms.input('term2_end', 'End Date', this.academicYearData.term2_end || '', {
                  type: 'date',
                  required: true
                })}
              </div>
            </div>

            <!-- Term 3 -->
            <div class="bg-gradient-to-br from-purple-50 to-violet-50 rounded-lg p-6 border border-purple-200">
              <div class="mb-4">
                <h4 class="text-lg font-semibold text-gray-900">Term Three</h4>
              </div>
              <div class="space-y-4">
                ${SRDesignSystem.forms.input('term3_start', 'Start Date', this.academicYearData.term3_start || '', {
                  type: 'date',
                  required: true
                })}
                ${SRDesignSystem.forms.input('term3_end', 'End Date', this.academicYearData.term3_end || '', {
                  type: 'date',
                  required: true
                })}
              </div>
            </div>
          </div>

          <!-- Active Term Selection -->
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-3">
                Which term is currently active? <span class="text-red-500">*</span>
              </label>
              <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap}">
                <div class="flex items-center p-4 border border-gray-300 rounded-lg hover:border-primary-500 transition-colors">
                  <input type="radio" name="active_term" value="1" id="active_term_1"
                         class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                         ${(this.academicYearData.active_term || '2') === '1' ? 'checked' : ''}>
                  <label for="active_term_1" class="ml-3 text-sm font-medium text-gray-700 cursor-pointer">Term 1</label>
                </div>
                <div class="flex items-center p-4 border border-gray-300 rounded-lg hover:border-primary-500 transition-colors">
                  <input type="radio" name="active_term" value="2" id="active_term_2"
                         class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                         ${(this.academicYearData.active_term || '2') === '2' ? 'checked' : ''}>
                  <label for="active_term_2" class="ml-3 text-sm font-medium text-gray-700 cursor-pointer">Term 2</label>
                </div>
                <div class="flex items-center p-4 border border-gray-300 rounded-lg hover:border-primary-500 transition-colors">
                  <input type="radio" name="active_term" value="3" id="active_term_3"
                         class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                         ${(this.academicYearData.active_term || '2') === '3' ? 'checked' : ''}>
                  <label for="active_term_3" class="ml-3 text-sm font-medium text-gray-700 cursor-pointer">Term 3</label>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-start">
              <i class="fas fa-info-circle text-blue-600 mt-0.5 mr-3"></i>
              <div class="text-sm text-blue-800">
                <strong>Note:</strong> The selected term will be set as the active term when the academic year is created.
              </div>
            </div>
          </div>
        </form>
      </div>
    `;
  }

  // Step 3: Review and Confirmation
  renderReviewStep() {
    // Data should already be saved from previous steps navigation
    // Don't call saveCurrentStepData() here as the form doesn't exist

    return `
      <div class="space-y-6">
        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Review Your Setup</h3>
          <p class="text-gray-600">
            Please review your academic year and terms configuration before saving.
          </p>
        </div>

        <!-- Academic Year Summary -->
        <div class="space-y-4">
          <div>
            <h4 class="text-md font-semibold text-gray-900 mb-3">Academic Year</h4>
          </div>
          <div class="bg-gray-50 rounded-lg p-6 border border-gray-200">
            <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap}">
              <div class="space-y-1">
                <span class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-500">Academic Year:</span>
                <span class="block ${SRDesignSystem.responsive.text.sm} font-semibold text-gray-900">${this.academicYearData.name || 'Not selected'}</span>
              </div>
              <div class="space-y-1">
                <span class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-500">Start Date:</span>
                <span class="block ${SRDesignSystem.responsive.text.sm} font-semibold text-gray-900">${this.formatDate(this.academicYearData.start_date)}</span>
              </div>
              <div class="space-y-1">
                <span class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-500">End Date:</span>
                <span class="block ${SRDesignSystem.responsive.text.sm} font-semibold text-gray-900">${this.formatDate(this.academicYearData.end_date)}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Terms Summary -->
        <div class="space-y-4">
          <div>
            <h4 class="text-md font-semibold text-gray-900 mb-3">Terms Configuration</h4>
          </div>
          <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap}">
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
              <div class="flex items-center justify-between mb-4">
                <h5 class="text-lg font-semibold text-gray-900">Term 1</h5>
                ${this.academicYearData.active_term === '1' ? SRDesignSystem.components.badge('Active', 'success') : ''}
              </div>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-500">Start:</span>
                  <span class="${SRDesignSystem.responsive.text.sm} font-semibold text-gray-900">${this.formatDate(this.academicYearData.term1_start)}</span>
                </div>
                <div class="flex justify-between">
                  <span class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-500">End:</span>
                  <span class="${SRDesignSystem.responsive.text.sm} font-semibold text-gray-900">${this.formatDate(this.academicYearData.term1_end)}</span>
                </div>
              </div>
            </div>

            <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-6 border border-green-200">
              <div class="flex items-center justify-between mb-4">
                <h5 class="text-lg font-semibold text-gray-900">Term 2</h5>
                ${this.academicYearData.active_term === '2' ? SRDesignSystem.components.badge('Active', 'success') : ''}
              </div>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-500">Start:</span>
                  <span class="${SRDesignSystem.responsive.text.sm} font-semibold text-gray-900">${this.formatDate(this.academicYearData.term2_start)}</span>
                </div>
                <div class="flex justify-between">
                  <span class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-500">End:</span>
                  <span class="${SRDesignSystem.responsive.text.sm} font-semibold text-gray-900">${this.formatDate(this.academicYearData.term2_end)}</span>
                </div>
              </div>
            </div>

            <div class="bg-gradient-to-br from-purple-50 to-violet-50 rounded-lg p-6 border border-purple-200">
              <div class="flex items-center justify-between mb-4">
                <h5 class="text-lg font-semibold text-gray-900">Term 3</h5>
                ${this.academicYearData.active_term === '3' ? SRDesignSystem.components.badge('Active', 'success') : ''}
              </div>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-500">Start:</span>
                  <span class="${SRDesignSystem.responsive.text.sm} font-semibold text-gray-900">${this.formatDate(this.academicYearData.term3_start)}</span>
                </div>
                <div class="flex justify-between">
                  <span class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-500">End:</span>
                  <span class="${SRDesignSystem.responsive.text.sm} font-semibold text-gray-900">${this.formatDate(this.academicYearData.term3_end)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <div class="flex items-start">
            <i class="fas fa-check-circle text-green-600 mt-0.5 mr-3"></i>
            <div class="text-sm text-green-800">
              <strong>Ready to Create:</strong> Your academic year and terms will be created and the selected term will be activated.
            </div>
          </div>
        </div>
      </div>
    `;
  }



  // Format date for display
  formatDate(dateString) {
    if (!dateString) return 'Not set';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch (error) {
      return 'Invalid date';
    }
  }

  // Initialize component
  init() {
    console.log('🔧 Initializing Academic Year Setup Component...');

    // Reset component state
    this.resetComponentState();

    // Load available years for dropdown
    this.loadAvailableYears();

    // Initialize event listeners
    this.initializeEventListeners();

    console.log('✅ Academic Year Setup Component initialized');
  }

  // Reset component state
  resetComponentState() {
    this.currentStep = 1;
    this.academicYearData = {
      name: '',
      startDate: '',
      endDate: '',
      terms: []
    };
    console.log('🔄 Academic Year Setup Component state reset');
  }

  // Cleanup component
  cleanup() {
    console.log('🧹 Cleaning up Academic Year Setup Component...');
    this.resetComponentState();

    // Reset form if it exists
    const form = document.getElementById('academic-year-setup-form');
    if (form) {
      form.reset();
    }

    console.log('✅ Academic Year Setup Component cleanup completed');
  }



  // Initialize event listeners
  initializeEventListeners() {
    // Store reference to this instance globally for button onclick handlers
    window.academicYearSetup = this;

    // Form validation on input
    document.addEventListener('input', (e) => {
      if (e.target.closest('#academic-year-setup-form')) {
        // Real-time validation can be added here if needed
      }
    });
  }

  // Move to next step
  nextStep() {
    if (this.currentStep < this.totalSteps) {
      // Validate current step before proceeding
      if (this.validateCurrentStep()) {
        this.saveCurrentStepData();
        this.currentStep++;
        console.log(`📍 Moved to step ${this.currentStep}, current data:`, this.academicYearData);
        this.refreshView();
      }
    }
  }

  // Move to previous step
  prevStep() {
    if (this.currentStep > 1) {
      this.saveCurrentStepData();
      this.currentStep--;
      console.log(`📍 Moved back to step ${this.currentStep}, current data:`, this.academicYearData);
      this.refreshView();
    }
  }

  // Validate current step
  validateCurrentStep() {
    switch (this.currentStep) {
      case 1:
        return this.validateAcademicYearForm();
      case 2:
        return this.validateTermsForm();
      case 3:
        return true; // Review step doesn't need validation
      default:
        return false;
    }
  }

  // Validate academic year form
  validateAcademicYearForm() {
    const form = document.getElementById('academic-year-setup-form');
    if (!form) return false;

    // Use HTML5 validation first
    if (!form.checkValidity()) {
      form.reportValidity();
      return false;
    }

    const formData = new FormData(form);
    const name = formData.get('name');
    const startDate = formData.get('start_date');
    const endDate = formData.get('end_date');

    if (!name || !startDate || !endDate) {
      this.showError('Please fill in all required fields for the academic year.');
      return false;
    }

    if (new Date(startDate) >= new Date(endDate)) {
      this.showError('Academic year start date must be before the end date.');
      return false;
    }

    return true;
  }

  // Validate terms form
  validateTermsForm() {
    const form = document.getElementById('academic-year-setup-form');
    if (!form) return false;

    const formData = new FormData(form);

    // Check all term dates are filled
    const termFields = [
      { start: 'term1_start', end: 'term1_end', name: 'Term 1' },
      { start: 'term2_start', end: 'term2_end', name: 'Term 2' },
      { start: 'term3_start', end: 'term3_end', name: 'Term 3' }
    ];

    for (const term of termFields) {
      const startDate = formData.get(term.start);
      const endDate = formData.get(term.end);

      if (!startDate || !endDate) {
        this.showError(`Please fill in all dates for ${term.name}.`);
        return false;
      }

      if (new Date(startDate) >= new Date(endDate)) {
        this.showError(`${term.name} start date must be before the end date.`);
        return false;
      }
    }

    // Check that an active term is selected
    const activeTerm = formData.get('active_term');
    if (!activeTerm) {
      this.showError('Please select which term is currently active.');
      return false;
    }

    return true;
  }

  // Save current step data
  saveCurrentStepData() {
    switch (this.currentStep) {
      case 1:
        this.saveAcademicYearData();
        break;
      case 2:
        this.saveTermsData();
        break;
    }
  }

  // Save academic year data
  saveAcademicYearData() {
    const form = document.getElementById('academic-year-setup-form');
    if (form) {
      const formData = new FormData(form);

      // Update academicYearData preserving existing data
      this.academicYearData = {
        ...this.academicYearData, // Preserve existing data
        name: formData.get('name'),
        start_date: formData.get('start_date'),
        end_date: formData.get('end_date'),
        // Only update terms data if it exists in the form (for step 1, these will be null)
        ...(formData.get('term1_start') && {
          term1_start: formData.get('term1_start'),
          term1_end: formData.get('term1_end'),
          term2_start: formData.get('term2_start'),
          term2_end: formData.get('term2_end'),
          term3_start: formData.get('term3_start'),
          term3_end: formData.get('term3_end'),
          active_term: formData.get('active_term') || '2'
        })
      };

      console.log('📊 Academic year data saved:', this.academicYearData);
    }
  }

  // Save terms data
  saveTermsData() {
    const form = document.getElementById('academic-year-setup-form');
    if (form) {
      const formData = new FormData(form);

      // Update the academicYearData with all form data including terms
      this.academicYearData = {
        ...this.academicYearData, // Preserve existing academic year data
        term1_start: formData.get('term1_start'),
        term1_end: formData.get('term1_end'),
        term2_start: formData.get('term2_start'),
        term2_end: formData.get('term2_end'),
        term3_start: formData.get('term3_start'),
        term3_end: formData.get('term3_end'),
        active_term: formData.get('active_term') || '2'
      };


    }
  }

  // Refresh entire view
  refreshView() {
    const contentArea = document.getElementById('content-area');
    if (contentArea) {
      contentArea.innerHTML = this.render();
    }
  }

  // Save complete setup
  async saveSetup() {
    if (this.isSubmitting) return;

    try {
      this.isSubmitting = true;
      this.refreshView(); // Update UI to show submitting state

      console.log('🚀 Starting academic year creation...');
      console.log('📊 Using saved data:', this.academicYearData);

      // Use the already saved data instead of trying to read from form
      // (form doesn't exist in review step)
      const activeTerm = this.academicYearData.active_term || '2';

      // Prepare data exactly like the modal
      const academicYearData = {
        name: this.academicYearData.name,
        start_date: this.academicYearData.start_date,
        end_date: this.academicYearData.end_date,
        is_active: true,
        terms: [
          {
            name: 'Term 1',
            number: 1,
            start_date: this.academicYearData.term1_start,
            end_date: this.academicYearData.term1_end,
            is_active: activeTerm === '1'
          },
          {
            name: 'Term 2',
            number: 2,
            start_date: this.academicYearData.term2_start,
            end_date: this.academicYearData.term2_end,
            is_active: activeTerm === '2'
          },
          {
            name: 'Term 3',
            number: 3,
            start_date: this.academicYearData.term3_start,
            end_date: this.academicYearData.term3_end,
            is_active: activeTerm === '3'
          }
        ]
      };

      console.log('📊 Academic year data prepared:', academicYearData);

      // Validate required fields
      if (!academicYearData.name || !academicYearData.start_date || !academicYearData.end_date) {
        throw new Error('Please fill in all academic year fields');
      }

      // Validate term dates
      const missingTermDates = academicYearData.terms.some(term => !term.start_date || !term.end_date);
      if (missingTermDates) {
        throw new Error('Please fill in all term start and end dates');
      }

      // Validate date order
      if (new Date(academicYearData.start_date) >= new Date(academicYearData.end_date)) {
        throw new Error('Academic year end date must be after start date');
      }

      // Validate term date order
      for (const term of academicYearData.terms) {
        if (new Date(term.start_date) >= new Date(term.end_date)) {
          throw new Error(`${term.name} end date must be after start date`);
        }
      }

      // Set flag to prevent modal from showing during setup
      window.isSettingUpAcademicYear = true;

      // Use the API service to create academic year
      const result = await window.AcademicYearsAPI.create(academicYearData);

      if (result && result.success) {
        this.showSuccess('Academic year and terms created successfully!');

        // Wait a moment for database to be consistent, then refresh
        setTimeout(async () => {
          console.log('🔄 Refreshing system after academic year creation...');

          // Refresh global academic context
          if (window.AcademicContext) {
            await window.AcademicContext.refresh();
            console.log('✅ Global academic context refreshed');
          }

          // Force refresh academic info and update all components
          if (window.refreshAcademicInfo) {
            await window.refreshAcademicInfo();
          }

          // Update layout with new academic info
          if (window.Layout && window.Layout.updateAcademicInfo) {
            await window.Layout.updateAcademicInfo();
          }

          // Reload dashboard with new academic context
          if (window.PageRouter) {
            window.PageRouter.loadPage('dashboard');
          }

          // Clear the setup flag
          window.isSettingUpAcademicYear = false;

        }, 100);

      } else {
        throw new Error(result?.message || 'Failed to create academic year');
      }

    } catch (error) {
      this.showError(error.message || 'Failed to save academic year setup');

      // Clear the setup flag
      window.isSettingUpAcademicYear = false;
    } finally {
      this.isSubmitting = false;
      this.refreshView();
    }
  }

  // Show success message
  showSuccess(message) {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, 'success');
    } else {
      alert('Success: ' + message);
    }
  }

  // Show error message
  showError(message) {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, 'error');
    } else {
      alert('Error: ' + message);
    }
  }

  // Generate available years (academic_years.name is VARCHAR with 4-digit year format)
  loadAvailableYears() {
    try {
      // Generate years programmatically since academic_years.name is VARCHAR, not ENUM
      const currentYear = new Date().getFullYear();
      const startYear = currentYear;     // Start with current year
      const endYear = currentYear + 4;   // Allow 4 years forward

      this.availableYears = [];
      for (let year = startYear; year <= endYear; year++) {
        this.availableYears.push(year.toString());
      }

    } catch (error) {
      console.warn('⚠️ Error generating years, using fallback:', error);

      // Fallback: generate a basic range
      const currentYear = new Date().getFullYear();
      this.availableYears = [];
      for (let year = currentYear; year <= currentYear + 5; year++) {
        this.availableYears.push(year.toString());
      }
      console.log('📅 Using fallback years:', this.availableYears);
    }
  }

  // Get year options for design system select
  getYearOptions() {
    if (!this.availableYears || this.availableYears.length === 0) {
      return [{ value: '', label: 'Loading years...' }];
    }

    return this.availableYears.map(year => ({
      value: year,
      label: year
    }));
  }

  // Render year options for the dropdown (legacy method)
  renderYearOptions() {
    if (!this.availableYears || this.availableYears.length === 0) {
      return '';
    }

    return this.availableYears.map(year => {
      const selected = this.academicYearData.name === year ? 'selected' : '';
      return `<option value="${year}" ${selected}>${year}</option>`;
    }).join('');
  }

  // Populate year dropdown with retry mechanism
  populateYearDropdownWithRetry(retryCount = 0, maxRetries = 10) {
    const yearSelect = document.getElementById('academic-year-name');

    if (yearSelect) {
      // Element found, populate it
      this.populateYearDropdown();
    } else if (retryCount < maxRetries) {
      // Element not found, retry after a short delay
      setTimeout(() => {
        this.populateYearDropdownWithRetry(retryCount + 1, maxRetries);
      }, 100);
    }
  }

  // Populate year dropdown
  populateYearDropdown() {
    console.log('🔄 Populating year dropdown...');
    const yearSelect = document.getElementById('academic-year-name');
    console.log('📋 Year select element:', yearSelect);
    console.log('📅 Available years:', this.availableYears);

    if (!yearSelect) {
      console.warn('⚠️ Year dropdown element not found');
      return;
    }

    // Ensure we have available years - generate them if missing
    if (!this.availableYears || this.availableYears.length === 0) {
      console.warn('⚠️ Available years not found, generating fallback years');
      const currentYear = new Date().getFullYear();
      this.availableYears = [];
      for (let year = currentYear; year <= currentYear + 4; year++) {
        this.availableYears.push(year.toString());
      }
      console.log('📅 Generated fallback years:', this.availableYears);
    }

    // Clear existing options except the first one
    while (yearSelect.children.length > 1) {
      yearSelect.removeChild(yearSelect.lastChild);
    }

    // Add year options
    this.availableYears.forEach(year => {
      const option = document.createElement('option');
      option.value = year;
      option.textContent = year;

      // Select current year by default
      if (year === new Date().getFullYear().toString()) {
        option.selected = true;
      }

      yearSelect.appendChild(option);
    });
  }
}

// Export for use in page router
window.AcademicYearSetupComponent = AcademicYearSetupComponent;

// Log that the component is loaded
console.log('✅ AcademicYearSetupComponent class loaded and available globally');
