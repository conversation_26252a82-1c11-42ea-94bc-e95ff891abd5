const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');
const { requireAcademicContext } = require('../middleware/validation');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get classes and streams overview
router.get('/', async (req, res) => {
  try {
    const { academic_year_id, level } = req.query;

    let whereClause = 'WHERE c.is_active = TRUE';
    let params = [];

    if (level) {
      whereClause += ' AND el.code = ?';
      params.push(level);
    }

    const query = `
      SELECT
        c.id, c.name as class_name, c.is_active,
        cl.id as class_level_id, cl.name as class_level_name, cl.sort_order,
        el.code as education_level_code, el.name as education_level_name,
        GROUP_CONCAT(DISTINCT s.id) as stream_ids,
        GROUP_CONCAT(DISTINCT s.name) as stream_names,
        GROUP_CONCAT(DISTINCT s.stream_type) as stream_types,
        COALESCE(o_enrollment.count, 0) + COALESCE(a_enrollment.count, 0) as actual_enrollment
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN stream_classes sc ON cl.id = sc.class_level_id
      LEFT JOIN streams s ON sc.stream_id = s.id
      LEFT JOIN (
        SELECT current_class_id, COUNT(*) as count
        FROM o_level_students
        WHERE status = 'active'
        GROUP BY current_class_id
      ) o_enrollment ON c.id = o_enrollment.current_class_id
      LEFT JOIN (
        SELECT current_class_id, COUNT(*) as count
        FROM a_level_students
        WHERE status = 'active'
        GROUP BY current_class_id
      ) a_enrollment ON c.id = a_enrollment.current_class_id

      ${whereClause}
      GROUP BY c.id, c.name, c.is_active,
               cl.id, cl.name, cl.sort_order,
               el.code, el.name,
               o_enrollment.count, a_enrollment.count
      ORDER BY cl.sort_order, c.name
    `;

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get classes and streams error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve classes and streams'
    });
  }
});

// Get class details with students and subjects
router.get('/:classId/details', async (req, res) => {
  try {
    const { classId } = req.params;
    const { academic_year_id, term_id } = req.query;

    // Get class basic information
    const classQuery = `
      SELECT
        c.*,
        cl.name as class_level_name, cl.sort_order, cl.streams_optional,
        el.code as education_level_code, el.name as education_level_name,
        s.name as stream_name, s.stream_type
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN stream_classes sc ON cl.id = sc.class_level_id
      LEFT JOIN streams s ON sc.stream_id = s.id
      WHERE c.id = ?
    `;
    
    const classResult = await executeQuery(classQuery, [classId]);
    
    if (!classResult.success || classResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class not found'
      });
    }

    // Get enrolled students from both O-Level and A-Level tables
    let studentWhereClause = 'WHERE current_class_id = ?';
    let studentParams = [classId];

    if (academic_year_id) {
      studentWhereClause += ' AND current_academic_year_id = ?';
      studentParams.push(academic_year_id);
    }

    if (term_id) {
      studentWhereClause += ' AND current_term_id = ?';
      studentParams.push(term_id);
    }

    // Query O-Level students
    const oLevelStudentsQuery = `
      SELECT
        'o_level' as student_type,
        id, admission_number, first_name, middle_name, last_name,
        gender, status, enrollment_date
      FROM o_level_students
      ${studentWhereClause}
      ORDER BY admission_number
    `;

    // Query A-Level students
    const aLevelStudentsQuery = `
      SELECT
        'a_level' as student_type,
        id, admission_number, first_name, middle_name, last_name,
        gender, status, registration_date as enrollment_date
      FROM a_level_students
      ${studentWhereClause}
      ORDER BY admission_number
    `;

    const [oLevelStudentsResult, aLevelStudentsResult] = await Promise.all([
      executeQuery(oLevelStudentsQuery, studentParams),
      executeQuery(aLevelStudentsQuery, studentParams)
    ]);

    // Combine students from both levels
    const allStudents = [
      ...(oLevelStudentsResult.success ? oLevelStudentsResult.data : []),
      ...(aLevelStudentsResult.success ? aLevelStudentsResult.data : [])
    ].sort((a, b) => a.admission_number.localeCompare(b.admission_number));

    // Get available subjects based on class level
    const classInfo = classResult.data[0];
    let subjectsQuery = '';
    let subjectParams = [];

    if (classInfo.education_level_code === 'o_level') {
      subjectsQuery = `
        SELECT
          id,
          name,
          short_name,
          'o_level' as level,
          subject_type,
          created_at as assigned_date,
          NULL as teacher_id,
          NULL as teacher_first_name,
          NULL as teacher_last_name
        FROM o_level_subjects
        WHERE is_active = 1
        ORDER BY name
      `;
    } else if (classInfo.education_level_code === 'a_level') {
      subjectsQuery = `
        SELECT
          id,
          name,
          short_name,
          'a_level' as level,
          subject_type,
          created_at as assigned_date,
          NULL as teacher_id,
          NULL as teacher_first_name,
          NULL as teacher_last_name
        FROM a_level_subjects
        WHERE is_active = 1
        ORDER BY name
      `;
    } else {
      // Return both O-Level and A-Level subjects
      subjectsQuery = `
        SELECT
          id,
          name,
          short_name,
          'o_level' as level,
          subject_type,
          created_at as assigned_date,
          NULL as teacher_id,
          NULL as teacher_first_name,
          NULL as teacher_last_name
        FROM o_level_subjects
        WHERE is_active = 1
        UNION ALL
        SELECT
          id,
          name,
          short_name,
          'a_level' as level,
          subject_type,
          created_at as assigned_date,
          NULL as teacher_id,
          NULL as teacher_first_name,
          NULL as teacher_last_name
        FROM a_level_subjects
        WHERE is_active = 1
        ORDER BY level, name
      `;
    }

    const subjectsResult = await executeQuery(subjectsQuery);

    const classDetails = {
      class_info: classResult.data[0],
      students: allStudents,
      subjects: subjectsResult.success ? subjectsResult.data : []
    };

    res.json({
      success: true,
      data: classDetails
    });

  } catch (error) {
    console.error('Get class details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve class details'
    });
  }
});

// Assign subjects to class
router.post('/:classId/assign-subjects', requireAcademicContext(), async (req, res) => {
  try {
    const { classId } = req.params;
    const { subject_ids, academic_year_id } = req.body;

    if (!subject_ids || !Array.isArray(subject_ids) || subject_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Subject IDs array is required'
      });
    }

    if (!academic_year_id) {
      return res.status(400).json({
        success: false,
        message: 'Academic year ID is required'
      });
    }

    // Check if class exists
    const classCheck = await executeQuery('SELECT id FROM classes WHERE id = ?', [classId]);
    if (!classCheck.success || classCheck.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class not found'
      });
    }

    // Note: Subject assignment functionality not implemented in current schema
    // This endpoint returns success for compatibility but doesn't perform actual assignments

    res.json({
      success: true,
      message: 'Subjects assigned to class successfully',
      data: {
        class_id: classId,
        subjects_assigned: subject_ids.length,
        academic_year_id
      }
    });

  } catch (error) {
    console.error('Assign subjects to class error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign subjects to class'
    });
  }
});

// Get class enrollment statistics
router.get('/enrollment-stats', async (req, res) => {
  try {
    const { academic_year_id, term_id } = req.query;
    
    let whereClause = 'WHERE c.is_active = TRUE';
    let params = [];
    
    if (academic_year_id) {
      whereClause += ' AND c.academic_year_id = ?';
      params.push(academic_year_id);
    }
    
    if (term_id) {
      whereClause += ' AND se.term_id = ?';
      params.push(term_id);
    }

    const statsQuery = `
      SELECT
        el.name as education_level_name,
        el.code as education_level_code,
        COUNT(DISTINCT c.id) as total_classes,
        COALESCE(student_counts.total_students, 0) as total_students,
        COALESCE(student_counts.average_class_size, 0) as average_class_size,
        COALESCE(student_counts.largest_class_size, 0) as largest_class_size,
        COALESCE(student_counts.smallest_class_size, 0) as smallest_class_size
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN (
        SELECT
          el.code as education_level_code,
          COUNT(*) as total_students,
          AVG(class_student_count.student_count) as average_class_size,
          MAX(class_student_count.student_count) as largest_class_size,
          MIN(class_student_count.student_count) as smallest_class_size
        FROM (
          SELECT current_class_id, COUNT(*) as student_count
          FROM o_level_students
          WHERE status = 'active' AND current_class_id IS NOT NULL
          GROUP BY current_class_id
          UNION ALL
          SELECT current_class_id, COUNT(*) as student_count
          FROM a_level_students
          WHERE status = 'active' AND current_class_id IS NOT NULL
          GROUP BY current_class_id
        ) class_student_count
        JOIN classes c ON class_student_count.current_class_id = c.id
        JOIN class_levels cl ON c.class_level_id = cl.id
        JOIN education_levels el ON cl.education_level_id = el.id
        GROUP BY el.code
      ) student_counts ON el.code = student_counts.education_level_code
      ${whereClause}
      GROUP BY el.id, el.name, el.code
      ORDER BY el.sort_order
    `;
    
    const enrollmentParams = term_id ? [term_id, ...params] : params;
    const result = await executeQuery(statsQuery, enrollmentParams);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get enrollment stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve enrollment statistics'
    });
  }
});

// Get stream distribution
router.get('/stream-distribution', async (req, res) => {
  try {
    const { academic_year_id, level_type } = req.query;
    
    let whereClause = 'WHERE c.is_active = TRUE';
    let params = [];
    
    if (academic_year_id) {
      whereClause += ' AND c.academic_year_id = ?';
      params.push(academic_year_id);
    }
    
    if (level_type) {
      whereClause += ' AND el.code = ?';
      params.push(level_type);
    }

    const distributionQuery = `
      SELECT
        s.name as stream_name,
        s.stream_type,
        el.code as education_level_code,
        el.name as education_level_name,
        COUNT(DISTINCT sc.class_level_id) as class_levels_count,
        COALESCE(student_counts.students_count, 0) as students_count
      FROM streams s
      JOIN stream_classes sc ON s.id = sc.stream_id
      JOIN class_levels cl ON sc.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN (
        SELECT
          s.stream_id,
          COUNT(*) as students_count
        FROM (
          SELECT stream_id
          FROM o_level_students
          WHERE status = 'active' AND stream_id IS NOT NULL
          UNION ALL
          SELECT stream_id
          FROM a_level_students
          WHERE status = 'active' AND stream_id IS NOT NULL
        ) student_streams
        GROUP BY stream_id
      ) student_counts ON s.id = student_counts.stream_id
      ${whereClause}
      GROUP BY s.id, s.name, s.stream_type, el.code, el.name
      ORDER BY el.sort_order, s.stream_type, s.name
    `;
    
    const result = await executeQuery(distributionQuery, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get stream distribution error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve stream distribution'
    });
  }
});

module.exports = router;
