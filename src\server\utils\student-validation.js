const { executeQuery } = require('../../database/connection');

/**
 * Student Data Validation Utilities
 * Provides comprehensive validation for student registration and management
 */
class StudentValidation {
  
  /**
   * Validate student basic information
   */
  static validateBasicInfo(data, studentType) {
    const errors = [];
    
    // Required fields validation
    if (!data.admission_number || data.admission_number.trim() === '') {
      errors.push('Admission number is required');
    }
    
    if (!data.first_name || data.first_name.trim() === '') {
      errors.push('First name is required');
    }
    
    if (!data.last_name || data.last_name.trim() === '') {
      errors.push('Last name is required');
    }
    
    if (!data.gender || !['male', 'female', 'Male', 'Female'].includes(data.gender)) {
      errors.push('Valid gender (Male/Female) is required');
    }
    
    if (!data.current_class_id) {
      errors.push('Class selection is required');
    }
    
    if (!data.current_academic_year_id) {
      errors.push('Academic year is required');
    }
    
    if (!data.current_term_id) {
      errors.push('Term is required');
    }
    
    // A-Level specific validations
    if (studentType === 'a_level' && !data.stream_id) {
      errors.push('Stream selection is required for A-Level students');
    }
    
    // Date validation
    if (data.date_of_birth) {
      const birthDate = new Date(data.date_of_birth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (isNaN(birthDate.getTime())) {
        errors.push('Invalid date of birth format');
      } else if (age < 10 || age > 25) {
        errors.push('Student age must be between 10 and 25 years');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
  
  /**
   * Validate admission number uniqueness
   */
  static async validateAdmissionNumberUniqueness(admissionNumber, excludeStudentId = null) {
    try {
      const queries = [
        'SELECT id FROM o_level_students WHERE admission_number = ?' + (excludeStudentId ? ' AND id != ?' : ''),
        'SELECT id FROM a_level_students WHERE admission_number = ?' + (excludeStudentId ? ' AND id != ?' : '')
      ];
      
      const params = excludeStudentId ? [admissionNumber, excludeStudentId] : [admissionNumber];
      
      const [oLevelResult, aLevelResult] = await Promise.all([
        executeQuery(queries[0], params),
        executeQuery(queries[1], params)
      ]);
      
      if (!oLevelResult.success || !aLevelResult.success) {
        throw new Error('Failed to check admission number uniqueness');
      }
      
      const exists = oLevelResult.data.length > 0 || aLevelResult.data.length > 0;
      
      return {
        isValid: !exists,
        errors: exists ? [`Admission number "${admissionNumber}" is already registered in the system. Please use a different admission number.`] : []
      };
      
    } catch (error) {
      throw new Error(`Admission number validation failed: ${error.message}`);
    }
  }
  
  /**
   * Validate class and stream assignment
   */
  static async validateClassAndStream(classId, streamId, studentType, academicYearId, termId) {
    try {
      const errors = [];
      
      // Validate class exists and is appropriate for student type
      const classQuery = `
        SELECT c.id, c.name, c.class_level_id, cl.code as class_level_code, 
               el.code as education_level_code, cl.streams_optional
        FROM classes c
        JOIN class_levels cl ON c.class_level_id = cl.id
        JOIN education_levels el ON cl.education_level_id = el.id
        WHERE c.id = ? AND c.is_active = TRUE
      `;
      
      const classResult = await executeQuery(classQuery, [classId]);
      
      if (!classResult.success || classResult.data.length === 0) {
        errors.push('Invalid class selection');
        return { isValid: false, errors };
      }
      
      const classInfo = classResult.data[0];
      
      // Validate education level matches student type
      const expectedEducationLevel = studentType === 'o_level' ? 'o_level' : 'a_level';
      if (classInfo.education_level_code !== expectedEducationLevel) {
        errors.push(`Class is not appropriate for ${studentType.replace('_', '-')} students`);
      }
      
      // Validate stream requirements
      if (studentType === 'o_level') {
        // For O-Level, check if streams are available for this class
        const streamsQuery = `
          SELECT s.id, s.name
          FROM streams s
          JOIN stream_classes sc ON s.id = sc.stream_id
          WHERE sc.class_level_id = ? AND s.stream_type = 'o_level'
        `;
        
        const streamsResult = await executeQuery(streamsQuery, [classInfo.class_level_id]);
        
        if (streamsResult.success && streamsResult.data.length > 0) {
          // Streams are available, so stream_id is required
          if (!streamId) {
            errors.push('Stream selection is required for this class');
          } else {
            // Validate selected stream is valid
            const validStream = streamsResult.data.find(s => s.id == streamId);
            if (!validStream) {
              errors.push('Invalid stream selection for this class');
            }
          }
        }
        // If no streams available, streamId can be null
      } else {
        // A-Level always requires stream
        if (!streamId) {
          errors.push('Stream selection is required for A-Level students');
        } else {
          // Validate A-Level stream
          const aLevelStreamQuery = `
            SELECT id, name FROM streams
            WHERE id = ? AND stream_type = 'a_level'
          `;

          const aLevelStreamResult = await executeQuery(aLevelStreamQuery, [streamId]);
          
          if (!aLevelStreamResult.success || aLevelStreamResult.data.length === 0) {
            errors.push('Invalid A-Level stream selection');
          }
        }
      }
      
      return {
        isValid: errors.length === 0,
        errors: errors,
        classInfo: classInfo
      };
      
    } catch (error) {
      throw new Error(`Class and stream validation failed: ${error.message}`);
    }
  }
  
  /**
   * Validate O-Level subject selection
   */
  static async validateOLevelSubjects(subjectIds, classLevelId) {
    try {
      const errors = [];
      
      if (!Array.isArray(subjectIds) || subjectIds.length === 0) {
        errors.push('At least one subject must be selected');
        return { isValid: false, errors };
      }
      
      // Validate subjects exist and are available for this class level
      const subjectQuery = `
        SELECT s.id, s.name, s.subject_type, sc.subject_status
        FROM o_level_subjects s
        JOIN o_level_subject_classes sc ON s.id = sc.subject_id
        WHERE s.id IN (${subjectIds.map(() => '?').join(',')}) 
          AND sc.class_level_id = ? AND s.is_active = TRUE
      `;
      
      const subjectResult = await executeQuery(subjectQuery, [...subjectIds, classLevelId]);
      
      if (!subjectResult.success) {
        throw new Error('Failed to validate subjects');
      }
      
      if (subjectResult.data.length !== subjectIds.length) {
        errors.push('Some selected subjects are not available for this class level');
      }
      
      // Validate subject type distribution
      const subjects = subjectResult.data;
      const compulsoryCount = subjects.filter(s => s.subject_type === 'compulsory').length;
      const electiveCount = subjects.filter(s => s.subject_type === 'elective').length;
      
      // Business rule validation based on class level
      // This would need to be customized based on your specific requirements
      if (subjects.length < 8) {
        errors.push('Minimum 8 subjects required for O-Level students');
      }
      
      if (subjects.length > 12) {
        errors.push('Maximum 12 subjects allowed for O-Level students');
      }
      
      return {
        isValid: errors.length === 0,
        errors: errors,
        subjects: subjects
      };
      
    } catch (error) {
      throw new Error(`O-Level subject validation failed: ${error.message}`);
    }
  }
  
  /**
   * Validate A-Level subject selection
   */
  static async validateALevelSubjects(principalSubjects, subsidiarySubjects, classLevelId) {
    try {
      const errors = [];

      console.log('🔍 validateALevelSubjects called with:', {
        principalSubjects,
        subsidiarySubjects,
        classLevelId,
        principalCount: principalSubjects?.length,
        subsidiaryCount: subsidiarySubjects?.length
      });

      // Validate principal subjects (exactly 3 required)
      if (!Array.isArray(principalSubjects) || principalSubjects.length !== 3) {
        const error = `Exactly 3 principal subjects are required for A-Level students (received ${principalSubjects?.length || 0})`;
        console.log('❌ Principal subjects validation failed:', error);
        errors.push(error);
      }

      // Validate subsidiary subjects (exactly 2 required: GP + one other)
      if (!Array.isArray(subsidiarySubjects) || subsidiarySubjects.length !== 2) {
        const error = `Exactly 2 subsidiary subjects are required for A-Level students (GP + one other) (received ${subsidiarySubjects?.length || 0})`;
        console.log('❌ Subsidiary subjects validation failed:', error);
        errors.push(error);
      }
      
      if (errors.length > 0) {
        return { isValid: false, errors };
      }
      
      const allSubjectIds = [...principalSubjects, ...subsidiarySubjects];
      
      // Validate subjects exist and are available
      // For A-Level, all subjects are available to all A-Level classes (S.5 and S.6)
      const subjectQuery = `
        SELECT s.id, s.name, s.subject_type, 'elective' as subject_status
        FROM a_level_subjects s
        WHERE s.id IN (${allSubjectIds.map(() => '?').join(',')})
          AND s.is_active = TRUE
      `;

      const subjectResult = await executeQuery(subjectQuery, allSubjectIds);
      
      if (!subjectResult.success) {
        throw new Error('Failed to validate A-Level subjects');
      }
      
      if (subjectResult.data.length !== allSubjectIds.length) {
        errors.push('Some selected subjects are not available for this class level');
      }
      
      // Validate subject types
      const subjects = subjectResult.data;
      const principalSubjectData = subjects.filter(s => principalSubjects.includes(s.id));
      const subsidiarySubjectData = subjects.filter(s => subsidiarySubjects.includes(s.id));
      
      const invalidPrincipal = principalSubjectData.filter(s => s.subject_type !== 'Principal');
      const invalidSubsidiary = subsidiarySubjectData.filter(s => s.subject_type !== 'Subsidiary');
      
      if (invalidPrincipal.length > 0) {
        errors.push('Some selected principal subjects are not valid principal subjects');
      }
      
      if (invalidSubsidiary.length > 0) {
        errors.push('Some selected subsidiary subjects are not valid subsidiary subjects');
      }
      
      return {
        isValid: errors.length === 0,
        errors: errors,
        subjects: subjects
      };
      
    } catch (error) {
      throw new Error(`A-Level subject validation failed: ${error.message}`);
    }
  }
  
  /**
   * Comprehensive student registration validation
   */
  static async validateStudentRegistration(data, studentType) {
    try {
      const allErrors = [];



      // Basic info validation
      const basicValidation = this.validateBasicInfo(data, studentType);
      if (!basicValidation.isValid) {
        allErrors.push(...basicValidation.errors);
      }
      
      // Admission number uniqueness
      const admissionValidation = await this.validateAdmissionNumberUniqueness(data.admission_number);
      if (!admissionValidation.isValid) {
        allErrors.push(...admissionValidation.errors);
      }
      
      // Class and stream validation
      const classValidation = await this.validateClassAndStream(
        data.current_class_id, 
        data.stream_id, 
        studentType, 
        data.current_academic_year_id, 
        data.current_term_id
      );
      if (!classValidation.isValid) {
        allErrors.push(...classValidation.errors);
      }
      
      // Subject validation
      if (studentType === 'o_level' && data.selected_subjects) {
        const subjectValidation = await this.validateOLevelSubjects(
          data.selected_subjects, 
          classValidation.classInfo?.class_level_id
        );
        if (!subjectValidation.isValid) {
          allErrors.push(...subjectValidation.errors);
        }
      } else if (studentType === 'a_level') {
        const principalSubjects = [
          data.principal_subject_1_id,
          data.principal_subject_2_id,
          data.principal_subject_3_id
        ].filter(id => id != null && id !== '').map(id => parseInt(id));

        const subsidiarySubjects = [
          data.subsidiary_subject_1_id,
          data.subsidiary_subject_2_id
        ].filter(id => id != null && id !== '').map(id => parseInt(id));


        
        const subjectValidation = await this.validateALevelSubjects(
          principalSubjects,
          subsidiarySubjects,
          classValidation.classInfo?.class_level_id
        );
        if (!subjectValidation.isValid) {
          allErrors.push(...subjectValidation.errors);
        }
      }
      
      return {
        isValid: allErrors.length === 0,
        errors: allErrors
      };
      
    } catch (error) {
      throw new Error(`Student registration validation failed: ${error.message}`);
    }
  }
}

module.exports = { StudentValidation };
