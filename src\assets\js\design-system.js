// SmartReport - Design System & Component Standards
// Industry-standard design patterns and reusable components

const SRDesignSystem = {
  // Color palette and theme configuration
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a'
    },
    secondary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9',
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e'
    },
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d'
    },
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f'
    },
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d'
    }
  },

  // Desktop-only breakpoints (for future extensibility)
  breakpoints: {
    sm: '1024px',   // Small desktop
    md: '1280px',   // Medium desktop
    lg: '1440px',   // Large desktop
    xl: '1920px',   // Extra large desktop
    '2xl': '2560px' // Ultra-wide desktop
  },

  // Desktop-only utilities (no mobile responsiveness)
  responsive: {
    // Grid system for desktop layouts
    grid: {
      container: 'w-full max-w-7xl mx-auto px-8',
      cols1: 'grid grid-cols-1',
      cols2: 'grid grid-cols-2',
      cols3: 'grid grid-cols-3',
      cols4: 'grid grid-cols-4',
      cols6: 'grid grid-cols-6',
      gap: 'gap-6',
      gapSm: 'gap-4',
      gapLg: 'gap-8'
    },

    // Desktop spacing utilities
    spacing: {
      padding: 'p-6',
      paddingSm: 'p-4',
      paddingX: 'px-6',
      paddingY: 'py-6',
      margin: 'm-6',
      marginX: 'mx-6',
      marginY: 'my-6'
    },

    // Desktop typography
    text: {
      xs: 'text-xs',
      sm: 'text-sm',
      base: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl',
      '2xl': 'text-2xl',
      '3xl': 'text-3xl'
    },

    // Desktop display utilities (no mobile/tablet variants)
    show: {
      always: 'block',
      never: 'hidden'
    },

    // Desktop table classes
    table: {
      container: 'overflow-x-auto',
      base: 'min-w-full divide-y divide-gray-200',
      cell: 'px-6 py-4',
      cellCompact: 'px-4 py-3',
      // Sticky header classes
      stickyHeader: 'sticky top-0 z-10 bg-gray-50',
      stickyHeaderCell: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky top-0 z-10 bg-gray-50'
    }
  },

  // Standard component layouts
  layouts: {
    // Page header with desktop-only structure
    pageHeader: (title, description, stats = []) => `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
        <div class="flex items-center justify-between">
          <div class="flex-1 min-w-0">
            <h1 class="text-3xl font-bold text-gray-900 mb-2 truncate">${title}</h1>
            <p class="text-base text-gray-600">${description}</p>
          </div>
          ${stats.length > 0 ? `
            <div class="flex items-center gap-3">
              ${stats.map(stat => `
                <div class="bg-${stat.color}-50 text-${stat.color}-700 px-4 py-2 rounded-lg text-sm">
                  <i class="${stat.icon} mr-2"></i>
                  <span class="font-medium">${stat.value} ${stat.label}</span>
                </div>
              `).join('')}
            </div>
          ` : ''}
        </div>
      </div>
    `,

    // Filters and actions bar
    filtersAndActions: (filters = [], actions = []) => `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <!-- Filters -->
          <div class="flex space-x-4">
            ${filters.map(filter => {
              if (filter.type === 'select') {
                return `
                  <div class="relative">
                    <select id="${filter.id}" class="appearance-none bg-gray-50 border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                      <option value="">${filter.placeholder}</option>
                      ${filter.options.map(option => `<option value="${option.value}">${option.label}</option>`).join('')}
                    </select>
                    <i class="fas fa-chevron-down absolute right-3 top-3 text-gray-400 text-sm pointer-events-none"></i>
                  </div>
                `;
              } else if (filter.type === 'search') {
                return `
                  <div class="relative">
                    <input type="text" id="${filter.id}" placeholder="${filter.placeholder}"
                           class="bg-gray-50 border border-gray-300 rounded-lg px-4 py-2 pl-10 ${SRDesignSystem.responsive.text.sm} focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                  </div>
                `;
              }
              return '';
            }).join('')}
          </div>

          <!-- Actions -->
          <div class="flex space-x-3">
            ${actions.map(action => `
              <button ${action.onclick ? `onclick="${action.onclick}"` : ''}
                      class="bg-${action.color}-600 hover:bg-${action.color}-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center">
                <i class="${action.icon} mr-2"></i>
                ${action.label}
              </button>
            `).join('')}
          </div>
        </div>
      </div>
    `,

    // Empty state component
    emptyState: (icon, title, description, action = null) => `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-8 text-center">
        <i class="${icon} text-gray-400 text-4xl mb-4"></i>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">${title}</h3>
        <p class="text-gray-600 mb-4">${description}</p>
        ${action ? `
          <button onclick="${action.onclick}" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200">
            <i class="${action.icon} mr-2"></i>
            ${action.label}
          </button>
        ` : ''}
      </div>
    `,

    // Loading state - centralized for consistent UI
    loadingState: (message = 'Loading...') => `
      <div class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="w-16 h-16 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p class="text-gray-600 font-medium">${message}</p>
        </div>
      </div>
    `,

    // Error state
    errorState: (message = 'An error occurred') => `
      <div class="bg-red-50 border border-red-200 rounded-xl p-8 text-center">
        <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
        <h3 class="text-xl font-bold text-red-800 mb-2">Error</h3>
        <p class="text-red-600 mb-4">${message}</p>
        <button onclick="location.reload()" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200">
          Retry
        </button>
      </div>
    `
  },

  // Form components
  forms: {
    // Standard form field
    field: (label, input, required = false, helpText = '', error = '') => `
      <div class="form-field">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          ${label} ${required ? '<span class="text-red-500">*</span>' : ''}
        </label>
        ${input}
        ${error ? `<p class="mt-1 text-sm text-red-600 flex items-center"><i class="fas fa-exclamation-circle mr-1"></i>${error}</p>` : ''}
        ${helpText ? `<p class="mt-1 text-sm text-gray-500">${helpText}</p>` : ''}
      </div>
    `,

    // Text input with validation
    textInput: (id, name, placeholder = '', required = false, value = '', pattern = '', maxLength = '', additionalAttributes = {}) => {
      const readonly = additionalAttributes.readonly ? 'readonly' : '';
      const disabled = additionalAttributes.disabled ? 'disabled' : '';
      const style = additionalAttributes.style ? `style="${additionalAttributes.style}"` : '';
      const customClass = additionalAttributes.readonly ? 'bg-gray-50 cursor-not-allowed' : '';

      return `
        <input type="text" id="${id}" name="${name}" ${required ? 'required' : ''} value="${value}"
               ${pattern ? `pattern="${pattern}"` : ''} ${maxLength ? `maxlength="${maxLength}"` : ''}
               ${readonly} ${disabled} ${style}
               class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200 ${customClass}"
               placeholder="${placeholder}">
      `;
    },

    // Number input
    numberInput: (id, name, placeholder = '', required = false, value = '', min = '', max = '', step = '') => `
      <input type="number" id="${id}" name="${name}" ${required ? 'required' : ''} value="${value}"
             ${min !== '' ? `min="${min}"` : ''} ${max !== '' ? `max="${max}"` : ''} ${step !== '' ? `step="${step}"` : ''}
             class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
             placeholder="${placeholder}">
    `,

    // Email input
    emailInput: (id, name, placeholder = '', required = false, value = '') => `
      <input type="email" id="${id}" name="${name}" ${required ? 'required' : ''} value="${value}"
             class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
             placeholder="${placeholder}">
    `,

    // Date input
    dateInput: (id, name, required = false, value = '', min = '', max = '') => `
      <input type="date" id="${id}" name="${name}" ${required ? 'required' : ''} value="${value}"
             ${min ? `min="${min}"` : ''} ${max ? `max="${max}"` : ''}
             class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
    `,

    // Password input
    passwordInput: (id, name, placeholder = '', required = false, value = '') => `
      <input type="password" id="${id}" name="${name}" ${required ? 'required' : ''} value="${value}"
             class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
             placeholder="${placeholder}">
    `,

    // Desktop select dropdown
    select: (id, name, options = [], required = false, value = '', placeholder = 'Select an option', attributes = {}) => {
      const onchange = attributes.onchange || '';
      return `
        <select id="${id}" name="${name}" ${required ? 'required' : ''} ${onchange ? `onchange="${onchange}"` : ''}
                class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
          ${placeholder ? `<option value="">${placeholder}</option>` : ''}
          ${options.map(option => `
            <option value="${option.value}" ${value === option.value ? 'selected' : ''}>${option.label}</option>
          `).join('')}
        </select>
      `;
    },

    // Multi-select with checkboxes
    multiSelect: (id, name, options = [], values = [], label = '') => `
      <div class="space-y-2">
        ${label ? `<label class="block text-sm font-medium text-gray-700 mb-2">${label}</label>` : ''}
        <div class="max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-3 space-y-2">
          ${options.map(option => `
            <label class="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 p-1 rounded">
              <input type="checkbox" name="${name}" value="${option.value}"
                     ${values.includes(option.value) ? 'checked' : ''}
                     class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
              <span class="text-sm text-gray-700">${option.label}</span>
            </label>
          `).join('')}
        </div>
      </div>
    `,

    // Textarea
    textarea: (id, name, placeholder = '', required = false, value = '', rows = 4) => `
      <textarea id="${id}" name="${name}" ${required ? 'required' : ''} rows="${rows}"
                class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200 resize-vertical"
                placeholder="${placeholder}">${value}</textarea>
    `,

    // File input
    fileInput: (id, name, accept = '', required = false, multiple = false) => `
      <div class="relative">
        <input type="file" id="${id}" name="${name}" ${required ? 'required' : ''}
               ${accept ? `accept="${accept}"` : ''} ${multiple ? 'multiple' : ''}
               class="hidden" onchange="SRDesignSystem.forms.updateFileLabel('${id}')">
        <label for="${id}" class="w-full flex items-center justify-center px-3 py-2 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-primary-400 hover:bg-primary-50 transition-colors duration-200">
          <div class="text-center">
            <i class="fas fa-cloud-upload-alt text-2xl text-gray-400 mb-2"></i>
            <p class="text-sm text-gray-600">Click to upload or drag and drop</p>
            <p class="text-xs text-gray-500 mt-1" id="${id}-label">No file selected</p>
          </div>
        </label>
      </div>
    `,

    // Radio button group
    radioGroup: (name, options = [], value = '', label = '') => `
      <div class="space-y-2">
        ${label ? `<label class="block text-sm font-medium text-gray-700 mb-2">${label}</label>` : ''}
        <div class="space-y-2">
          ${options.map(option => `
            <label class="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 p-2 rounded">
              <input type="radio" name="${name}" value="${option.value}"
                     ${value === option.value ? 'checked' : ''}
                     class="border-gray-300 text-primary-600 focus:ring-primary-500">
              <span class="text-sm text-gray-700">${option.label}</span>
            </label>
          `).join('')}
        </div>
      </div>
    `,

    // Update file label helper
    updateFileLabel: (inputId) => {
      const input = document.getElementById(inputId);
      const label = document.getElementById(`${inputId}-label`);
      if (input && label) {
        if (input.files.length > 0) {
          if (input.files.length === 1) {
            label.textContent = input.files[0].name;
          } else {
            label.textContent = `${input.files.length} files selected`;
          }
        } else {
          label.textContent = 'No file selected';
        }
      }
    },

    // Database-specific form components for SmartReport

    // Academic year selector with dynamic year generation
    academicYearSelect: (id, name, required = false, value = '') => {
      // Generate years dynamically (same approach as AcademicYearSetupComponent)
      const currentYear = new Date().getFullYear();
      const startYear = currentYear;     // Start with current year
      const endYear = currentYear + 4;   // Allow 4 years forward

      const yearOptions = [];
      for (let year = startYear; year <= endYear; year++) {
        const yearStr = year.toString();
        yearOptions.push(`<option value="${yearStr}" ${value === yearStr ? 'selected' : ''}>${yearStr}</option>`);
      }

      return `
        <select id="${id}" name="${name}" ${required ? 'required' : ''}
                class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
          <option value="">Select Academic Year</option>
          ${yearOptions.join('')}
        </select>
      `;
    },

    // Term selector
    termSelect: (id, name, required = false, value = '') => `
      <select id="${id}" name="${name}" ${required ? 'required' : ''}
              class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
        <option value="">Select Term</option>
        <option value="term_1" ${value === 'term_1' ? 'selected' : ''}>Term 1</option>
        <option value="term_2" ${value === 'term_2' ? 'selected' : ''}>Term 2</option>
        <option value="term_3" ${value === 'term_3' ? 'selected' : ''}>Term 3</option>
      </select>
    `,

    // Education level selector (O Level, A Level)
    educationLevelSelect: (id, name, required = false, value = '') => `
      <select id="${id}" name="${name}" ${required ? 'required' : ''}
              class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
        <option value="">Select Education Level</option>
        <option value="o_level" ${value === 'o_level' ? 'selected' : ''}>O-Level</option>
        <option value="a_level" ${value === 'a_level' ? 'selected' : ''}>A-Level</option>
      </select>
    `,

    // Class level selector (S.1 - S.6)
    classLevelSelect: (id, name, required = false, value = '') => `
      <select id="${id}" name="${name}" ${required ? 'required' : ''}
              class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
        <option value="">Select Class Level</option>
        <option value="s1" ${value === 's1' ? 'selected' : ''}>S.1</option>
        <option value="s2" ${value === 's2' ? 'selected' : ''}>S.2</option>
        <option value="s3" ${value === 's3' ? 'selected' : ''}>S.3</option>
        <option value="s4" ${value === 's4' ? 'selected' : ''}>S.4</option>
        <option value="s5" ${value === 's5' ? 'selected' : ''}>S.5</option>
        <option value="s6" ${value === 's6' ? 'selected' : ''}>S.6</option>
      </select>
    `,

    // Gender selector
    genderSelect: (id, name, required = false, value = '') => `
      <select id="${id}" name="${name}" ${required ? 'required' : ''}
              class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
        <option value="">Select Gender</option>
        <option value="Male" ${value === 'Male' ? 'selected' : ''}>Male</option>
        <option value="Female" ${value === 'Female' ? 'selected' : ''}>Female</option>
      </select>
    `,

    // Student status selector
    studentStatusSelect: (id, name, required = false, value = '') => `
      <select id="${id}" name="${name}" ${required ? 'required' : ''}
              class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
        <option value="">Select Status</option>
        <option value="active" ${value === 'active' ? 'selected' : ''}>Active</option>
        <option value="transferred" ${value === 'transferred' ? 'selected' : ''}>Transferred</option>
        <option value="graduated" ${value === 'graduated' ? 'selected' : ''}>Graduated</option>
        <option value="dropped" ${value === 'dropped' ? 'selected' : ''}>Dropped</option>
        <option value="suspended" ${value === 'suspended' ? 'selected' : ''}>Suspended</option>
      </select>
    `,

    // Subject level selector
    subjectLevelSelect: (id, name, required = false, value = '') => `
      <select id="${id}" name="${name}" ${required ? 'required' : ''}
              class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
        <option value="">Select Subject Level</option>
        <option value="o_level" ${value === 'o_level' ? 'selected' : ''}>O-Level</option>
        <option value="a_level" ${value === 'a_level' ? 'selected' : ''}>A-Level</option>
      </select>
    `,

    // Subject type selector
    subjectTypeSelect: (id, name, required = false, value = '') => `
      <select id="${id}" name="${name}" ${required ? 'required' : ''}
              class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
        <option value="">Select Subject Type</option>
        <option value="Compulsory" ${value === 'Compulsory' ? 'selected' : ''}>Compulsory</option>
        <option value="Language" ${value === 'Language' ? 'selected' : ''}>Language</option>
        <option value="Practical (pre-vocational)" ${value === 'Practical (pre-vocational)' ? 'selected' : ''}>Practical (pre-vocational)</option>
        <option value="Religious Education" ${value === 'Religious Education' ? 'selected' : ''}>Religious Education</option>
      </select>
    `,

    // User role selector
    userRoleSelect: (id, name, required = false, value = '') => `
      <select id="${id}" name="${name}" ${required ? 'required' : ''}
              class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
        <option value="">Select Role</option>
        <option value="system_admin" ${value === 'system_admin' ? 'selected' : ''}>System Administrator</option>
        <option value="head_teacher" ${value === 'head_teacher' ? 'selected' : ''}>Head Teacher</option>
        <option value="deputy_head_teacher" ${value === 'deputy_head_teacher' ? 'selected' : ''}>Deputy Head Teacher</option>
        <option value="class_teacher" ${value === 'class_teacher' ? 'selected' : ''}>Class Teacher</option>
        <option value="subject_teacher" ${value === 'subject_teacher' ? 'selected' : ''}>Subject Teacher</option>
        <option value="bursar" ${value === 'bursar' ? 'selected' : ''}>Bursar</option>
        <option value="secretary" ${value === 'secretary' ? 'selected' : ''}>Secretary</option>
        <option value="librarian" ${value === 'librarian' ? 'selected' : ''}>Librarian</option>
      </select>
    `,

    // Competency score input (0-3 scale for O-Level)
    competencyScoreInput: (id, name, required = false, value = '') => `
      <input type="number" id="${id}" name="${name}" ${required ? 'required' : ''} value="${value}"
             min="0" max="3" step="0.1"
             class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
             placeholder="0.0 - 3.0">
    `,

    // Percentage input (0-100) - Integer only
    percentageInput: (id, name, required = false, value = '') => `
      <input type="number" id="${id}" name="${name}" ${required ? 'required' : ''} value="${value}"
             min="0" max="100" step="1"
             class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
             placeholder="0 - 100">
    `,

    // Admission number input (with validation pattern)
    admissionNumberInput: (id, name, required = false, value = '') => `
      <input type="text" id="${id}" name="${name}" ${required ? 'required' : ''} value="${value}"
             pattern="[A-Z0-9]{4,20}" maxlength="50"
             class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
             placeholder="e.g., SR2025001" style="text-transform: uppercase;">
    `,

    // Generic input method (wrapper for different input types)
    input: (id, label, value = '', options = {}) => {
      const type = options.type || 'text';
      const required = options.required || false;
      const placeholder = options.placeholder || '';
      const icon = options.icon || '';

      let inputHtml = '';

      switch (type) {
        case 'email':
          inputHtml = SRDesignSystem.forms.emailInput(id, id, placeholder, required, value);
          break;
        case 'number':
          inputHtml = SRDesignSystem.forms.numberInput(id, id, placeholder, required, value, options.min, options.max, options.step);
          break;
        case 'date':
          inputHtml = SRDesignSystem.forms.dateInput(id, id, required, value, options.min, options.max);
          break;
        case 'password':
          inputHtml = SRDesignSystem.forms.passwordInput(id, id, placeholder, required, value);
          break;
        default:
          inputHtml = SRDesignSystem.forms.textInput(id, id, placeholder, required, value, options.pattern, options.maxLength, options);
      }

      // Add icon if specified
      if (icon) {
        inputHtml = `
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="${icon} text-gray-400 text-base"></i>
            </div>
            ${inputHtml.replace('class="w-full px-3', 'class="w-full pl-10 pr-3')}
          </div>
        `;
      }

      return SRDesignSystem.forms.field(label, inputHtml, required, options.helpText, options.error);
    },

    // Generic select method
    select: (id, label, options = [], value = '', attributes = {}) => {
      const required = attributes.required || false;
      const multiple = attributes.multiple || false;

      const selectHtml = `
        <select id="${id}" name="${id}" ${required ? 'required' : ''} ${multiple ? 'multiple' : ''}
                class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
          ${options.map(option => `
            <option value="${option.value}" ${option.value === value ? 'selected' : ''}>
              ${option.label}
            </option>
          `).join('')}
        </select>
      `;

      return SRDesignSystem.forms.field(label, selectHtml, required, attributes.helpText, attributes.error);
    },

    // Generic textarea method
    textarea: (id, label, value = '', options = {}) => {
      const required = options.required || false;
      const placeholder = options.placeholder || '';
      const rows = options.rows || 4;

      const textareaHtml = `
        <textarea id="${id}" name="${id}" ${required ? 'required' : ''} rows="${rows}"
                  class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200 resize-vertical"
                  placeholder="${placeholder}">${value}</textarea>
      `;

      return SRDesignSystem.forms.field(label, textareaHtml, required, options.helpText, options.error);
    },

    // Desktop-optimized button method
    button: (id, text, type = 'primary', options = {}) => {
      const buttonType = options.type || 'button';
      const icon = options.icon || '';
      const disabled = options.disabled || false;
      const onclick = options.onclick || '';
      const customClass = options.class || '';

      let buttonClass = 'px-4 py-2 rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ';

      switch (type) {
        case 'primary':
          buttonClass += 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500';
          break;
        case 'secondary':
          buttonClass += 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500';
          break;
        case 'danger':
          buttonClass += 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500';
          break;
        case 'success':
        case 'green':
          buttonClass += 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500';
          break;
        case 'blue':
          buttonClass += 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500';
          break;
        case 'indigo':
          buttonClass += 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500';
          break;
        case 'ghost':
          buttonClass += 'bg-transparent text-gray-600 hover:bg-gray-100 focus:ring-gray-500';
          break;
        default:
          buttonClass += 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500';
      }

      if (disabled) {
        buttonClass += ' opacity-50 cursor-not-allowed';
      }

      // Add custom classes if provided
      if (customClass) {
        buttonClass += ` ${customClass}`;
      }

      return `
        <button id="${id}" type="${buttonType}" ${disabled ? 'disabled' : ''} ${onclick ? `onclick="${onclick}"` : ''}
                class="${buttonClass}" data-original-text="${text}">
          ${icon ? `<i class="${icon} mr-2"></i>` : ''}${text}
        </button>
      `;
    },

    // Generic file upload method (wrapper for fileInput with field styling)
    fileUpload: (id, label, options = {}) => {
      const required = options.required || false;
      const accept = options.accept || '';
      const multiple = options.multiple || false;

      const fileInputHtml = SRDesignSystem.forms.fileInput(id, id, accept, required, multiple);

      return SRDesignSystem.forms.field(label, fileInputHtml, required, options.helpText, options.error);
    },

    // Set button loading state
    setButtonLoading: (buttonId, loading) => {
      const button = document.getElementById(buttonId);
      if (!button) return;

      if (loading) {
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
      } else {
        button.disabled = false;
        button.innerHTML = button.getAttribute('data-original-text') || 'Submit';
      }
    },

    // Form validation helper
    validateForm: (formId) => {
      const form = document.getElementById(formId);
      if (!form) return false;

      const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
      let isValid = true;

      inputs.forEach(input => {
        const errorElement = input.parentElement.querySelector('.text-red-600');
        if (errorElement) errorElement.remove();

        if (!input.value.trim()) {
          isValid = false;
          const errorMsg = document.createElement('p');
          errorMsg.className = 'mt-1 text-sm text-red-600 flex items-center';
          errorMsg.innerHTML = '<i class="fas fa-exclamation-circle mr-1"></i>This field is required';
          input.parentElement.appendChild(errorMsg);
          input.classList.add('border-red-500');
        } else {
          input.classList.remove('border-red-500');
        }
      });

      return isValid;
    }
  },

  // Data table components
  tables: {
    // Standard data table with sticky headers and kebab menus
    dataTable: (id, columns, data, options = {}) => `
      <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
        ${options.title ? `
          <div class="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-4 border-b border-primary-200">
            <h3 class="text-lg font-semibold text-primary-900 flex items-center">
              ${options.icon ? `<i class="${options.icon} mr-3 text-primary-600"></i>` : ''}
              ${options.title}
            </h3>
            ${options.description ? `<p class="text-sm text-primary-700 mt-1">${options.description}</p>` : ''}
          </div>
        ` : ''}

        <div class="overflow-x-auto max-h-96 overflow-y-auto">
          <table id="${id}" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50 sticky top-0 z-10">
              <tr>
                ${columns.map(col => `
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50
                            ${col.sortable ? 'cursor-pointer hover:bg-gray-100' : ''}"
                      ${col.sortable ? `onclick="SRDesignSystem.tables.sortTable('${id}', '${col.key}')"` : ''}>
                    ${col.label}
                    ${col.sortable ? '<i class="fas fa-sort ml-1 text-gray-400"></i>' : ''}
                  </th>
                `).join('')}
                ${options.actions ? '<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Actions</th>' : ''}
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              ${data.map((row, index) => `
                <tr class="hover:bg-gray-50">
                  ${columns.map(col => `
                    <td class="px-6 py-4 text-sm text-gray-900
                              ${col.nowrap !== false ? 'whitespace-nowrap' : ''}">
                      ${SRDesignSystem.tables.formatCellValue(row[col.key], col.type)}
                    </td>
                  `).join('')}
                  ${options.actions ? `
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      ${SRDesignSystem.tables.generateKebabMenu(`${id}-${row.id || index}`, options.actions, row)}
                    </td>
                  ` : ''}
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>

        ${options.pagination ? SRDesignSystem.tables.pagination(options.pagination) : ''}
      </div>
    `,

    // Format cell values based on type
    formatCellValue: (value, type) => {
      if (value === null || value === undefined) return '-';

      switch (type) {
        case 'date':
          return new Date(value).toLocaleDateString();
        case 'datetime':
          return new Date(value).toLocaleString();
        case 'status':
          return SRDesignSystem.components.badge(value, SRDesignSystem.tables.getStatusColor(value));
        case 'boolean':
          return value ? '<i class="fas fa-check text-green-600"></i>' : '<i class="fas fa-times text-red-600"></i>';
        case 'percentage':
          return `${parseFloat(value).toFixed(1)}%`;
        case 'currency':
          return `UGX ${parseFloat(value).toLocaleString()}`;
        default:
          return value;
      }
    },

    // Get status color for badges
    getStatusColor: (status) => {
      const statusColors = {
        'active': 'success',
        'inactive': 'gray',
        'pending': 'warning',
        'completed': 'success',
        'cancelled': 'red',
        'suspended': 'red',
        'transferred': 'blue',
        'graduated': 'green',
        'dropped': 'red'
      };
      return statusColors[status?.toLowerCase()] || 'gray';
    },

    // Action dropdown for table rows
    actionDropdown: (uniqueId, actions, rowData) => {
      const dropdownId = `dropdown-${uniqueId}`;

      return `
        <div class="relative inline-block text-left">
          <button type="button"
                  onclick="SRDesignSystem.tables.toggleDropdown('${dropdownId}')"
                  class="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors duration-150"
                  title="More actions">
            <i class="fas fa-ellipsis-v text-sm"></i>
          </button>

          <div id="${dropdownId}"
               class="hidden fixed z-[9999] w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1"
               style="transform: translate(-100%, -100%);">
            ${actions.map(action => `
              <button onclick="SRDesignSystem.tables.closeDropdown('${dropdownId}'); ${action.onclick}('${rowData.id || rowData}')"
                      class="flex items-center w-full px-4 py-3 text-sm text-left hover:bg-gray-50 transition-colors duration-150 ${action.color || 'text-gray-700'}">
                <i class="${action.icon} w-4 mr-3"></i>
                ${action.label}
              </button>
            `).join('')}
          </div>
        </div>
      `;
    },

    // Pagination component
    pagination: (options) => `
      <div class="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Showing ${options.from} to ${options.to} of ${options.total} results
        </div>
        <div class="flex space-x-1">
          <button ${options.currentPage <= 1 ? 'disabled' : ''}
                  onclick="SRDesignSystem.tables.changePage(${options.currentPage - 1})"
                  class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Previous
          </button>
          ${Array.from({length: Math.min(5, options.totalPages)}, (_, i) => {
            const page = i + Math.max(1, options.currentPage - 2);
            return `
              <button onclick="SRDesignSystem.tables.changePage(${page})"
                      class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 ${page === options.currentPage ? 'bg-primary-600 text-white border-primary-600' : ''}">
                ${page}
              </button>
            `;
          }).join('')}
          <button ${options.currentPage >= options.totalPages ? 'disabled' : ''}
                  onclick="SRDesignSystem.tables.changePage(${options.currentPage + 1})"
                  class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Next
          </button>
        </div>
      </div>
    `,

    // Generate kebab menu for table actions
    generateKebabMenu: (id, actions, rowData) => {
      const dropdownId = `dropdown-${id}`;
      return `
        <div class="relative inline-block text-left">
          <button onclick="SRDesignSystem.tables.toggleDropdown('${dropdownId}')"
                  class="inline-flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <i class="fas fa-ellipsis-v text-gray-400"></i>
          </button>
          <div id="${dropdownId}"
               class="hidden absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
            <div class="py-1" role="menu">
              ${actions.map(action => `
                <button onclick="SRDesignSystem.tables.closeAllDropdowns(); ${action.onclick.replace('${row.id}', rowData.id || rowData.index || '')}"
                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                        role="menuitem">
                  <i class="${action.icon} mr-3 text-${action.color || 'gray'}-400"></i>
                  ${action.label}
                </button>
              `).join('')}
            </div>
          </div>
        </div>
      `;
    },

    // Dropdown control functions
    toggleDropdown: (dropdownId) => {
      const dropdown = document.getElementById(dropdownId);
      if (!dropdown) return;

      // Close all other dropdowns first
      SRDesignSystem.tables.closeAllDropdowns();

      // Toggle current dropdown
      dropdown.classList.toggle('hidden');

      // Position dropdown if now open
      if (!dropdown.classList.contains('hidden')) {
        const button = dropdown.previousElementSibling;
        if (button) {
          const buttonRect = button.getBoundingClientRect();

          // Position dropdown to the left of the kebab icon
          dropdown.style.position = 'fixed';
          dropdown.style.right = `${window.innerWidth - buttonRect.left + 8}px`;
          dropdown.style.top = `${buttonRect.top}px`;
          dropdown.style.zIndex = '1000';
        }

        setTimeout(() => {
          document.addEventListener('click', SRDesignSystem.tables.handleOutsideClick);
        }, 10);
      }
    },

    closeAllDropdowns: () => {
      document.querySelectorAll('[id^="dropdown-"]').forEach(dropdown => {
        dropdown.classList.add('hidden');
      });
      document.removeEventListener('click', SRDesignSystem.tables.handleOutsideClick);
    },

    handleOutsideClick: (event) => {
      if (!event.target.closest('[id^="dropdown-"]') && !event.target.closest('button[onclick*="toggleDropdown"]')) {
        SRDesignSystem.tables.closeAllDropdowns();
      }
    },

    // Sort table helper
    sortTable: (tableId, column) => {
      // Implementation would be added by specific components
      console.log(`Sorting table ${tableId} by column ${column}`);
    },

    // Change page helper
    changePage: (page) => {
      // Implementation would be added by specific components
      console.log(`Changing to page ${page}`);
    },

    // Dropdown control functions
    toggleDropdown: (dropdownId) => {
      const dropdown = document.getElementById(dropdownId);
      if (!dropdown) return;

      // Close all other dropdowns first
      SRDesignSystem.tables.closeAllDropdowns();

      // Toggle current dropdown
      dropdown.classList.toggle('hidden');

      // Position dropdown next to the button if dropdown is now open
      if (!dropdown.classList.contains('hidden')) {
        const button = dropdown.previousElementSibling;
        if (button) {
          const buttonRect = button.getBoundingClientRect();

          // Position dropdown to the left of the kebab icon, aligned to its top
          dropdown.style.position = 'fixed';
          dropdown.style.right = `${window.innerWidth - buttonRect.left + 8}px`; // 8px gap to the left of button
          dropdown.style.top = `${buttonRect.top}px`; // Same vertical position as button
          dropdown.style.zIndex = '1000';
        }

        setTimeout(() => {
          document.addEventListener('click', SRDesignSystem.tables.handleOutsideClick);
        }, 10);
      }
    },

    // Close specific dropdown
    closeDropdown: (dropdownId) => {
      const dropdown = document.getElementById(dropdownId);
      if (dropdown) {
        dropdown.classList.add('hidden');
      }
      document.removeEventListener('click', SRDesignSystem.tables.handleOutsideClick);
    },

    // Close all dropdowns
    closeAllDropdowns: () => {
      const dropdowns = document.querySelectorAll('[id^="dropdown-"]');
      dropdowns.forEach(dropdown => {
        dropdown.classList.add('hidden');
      });
      document.removeEventListener('click', SRDesignSystem.tables.handleOutsideClick);
    },

    // Handle clicks outside dropdown
    handleOutsideClick: (event) => {
      const isDropdownButton = event.target.closest('[onclick*="toggleDropdown"]');
      const isDropdownContent = event.target.closest('[id^="dropdown-"]');

      if (!isDropdownButton && !isDropdownContent) {
        SRDesignSystem.tables.closeAllDropdowns();
      }
    }
  },

  // Modal components
  modals: {
    // Desktop-optimized modal
    modal: (id, title, content, actions = []) => `
      <div id="${id}" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-6 border w-3/4 max-w-4xl shadow-lg rounded-xl bg-white">
          <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
              <h3 class="text-lg font-semibold text-gray-900">${title}</h3>
              <button onclick="SRDesignSystem.modals.close('${id}')" class="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-2 hover:bg-gray-100 rounded-lg">
                <i class="fas fa-times text-lg"></i>
              </button>
            </div>

            <!-- Modal Body -->
            <div class="mt-6">
              ${content}
            </div>

            <!-- Modal Footer -->
            ${actions.length > 0 ? `
              <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                ${actions.map(action => `
                  <button onclick="${action.onclick}" class="${action.class || 'btn-primary'}">
                    ${action.label}
                  </button>
                `).join('')}
              </div>
            ` : ''}
          </div>
        </div>
      </div>
    `,

    // Desktop confirmation modal
    confirm: (id, title, message, onConfirm, onCancel = null) => `
      <div id="${id}" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-6 border w-96 max-w-md shadow-lg rounded-xl bg-white">
          <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
              <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">${title}</h3>
            <p class="text-sm text-gray-500 mb-6">${message}</p>
            <div class="flex items-center justify-center space-x-4">
              <button onclick="${onCancel || `SRDesignSystem.modals.close('${id}')`}" class="btn-secondary">
                Cancel
              </button>
              <button onclick="${onConfirm}; SRDesignSystem.modals.close('${id}')" class="btn-danger">
                Confirm
              </button>
            </div>
          </div>
        </div>
      </div>
    `,

    // Show modal
    show: (modalId) => {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
      }
    },

    // Close modal
    close: (modalId) => {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
      }
    }
  },

  // Notification components
  notifications: {
    // Success notification
    success: (message, duration = 5000) => {
      SRDesignSystem.notifications.show(message, 'success', duration);
    },

    // Error notification
    error: (message, duration = 7000) => {
      SRDesignSystem.notifications.show(message, 'error', duration);
    },

    // Warning notification
    warning: (message, duration = 6000) => {
      SRDesignSystem.notifications.show(message, 'warning', duration);
    },

    // Info notification
    info: (message, duration = 5000) => {
      SRDesignSystem.notifications.show(message, 'info', duration);
    },

    // Show notification
    show: (message, type = 'info', duration = 5000) => {
      const id = 'notification-' + Date.now();
      const colors = {
        success: 'bg-green-500 text-white',
        error: 'bg-red-500 text-white',
        warning: 'bg-yellow-500 text-white',
        info: 'bg-blue-500 text-white'
      };

      const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
      };

      const notification = document.createElement('div');
      notification.id = id;
      notification.className = `fixed top-4 right-4 ${colors[type]} px-6 py-4 rounded-lg shadow-lg z-50 max-w-sm transform transition-all duration-300 translate-x-full`;
      notification.innerHTML = `
        <div class="flex items-center">
          <i class="${icons[type]} mr-3"></i>
          <span class="flex-1">${message}</span>
          <button onclick="SRDesignSystem.notifications.hide('${id}')" class="ml-4 text-white hover:text-gray-200">
            <i class="fas fa-times"></i>
          </button>
        </div>
      `;

      document.body.appendChild(notification);

      // Animate in
      setTimeout(() => {
        notification.classList.remove('translate-x-full');
      }, 100);

      // Auto hide
      setTimeout(() => {
        SRDesignSystem.notifications.hide(id);
      }, duration);
    },

    // Hide notification
    hide: (id) => {
      const notification = document.getElementById(id);
      if (notification) {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
          notification.remove();
        }, 300);
      }
    }
  },

  // Reusable UI components
  components: {
    // Badge component for status indicators
    badge: (text, color = 'gray', size = 'sm') => {
      const colorClasses = {
        'success': 'bg-success-100 text-success-800',
        'error': 'bg-error-100 text-error-800',
        'warning': 'bg-warning-100 text-warning-800',
        'primary': 'bg-primary-100 text-primary-800',
        'secondary': 'bg-secondary-100 text-secondary-800',
        'gray': 'bg-gray-100 text-gray-800',
        'red': 'bg-red-100 text-red-800',
        'green': 'bg-green-100 text-green-800',
        'blue': 'bg-blue-100 text-blue-800',
        'yellow': 'bg-yellow-100 text-yellow-800',
        'purple': 'bg-purple-100 text-purple-800',
        'indigo': 'bg-indigo-100 text-indigo-800',
        'pink': 'bg-pink-100 text-pink-800'
      };

      const sizeClasses = {
        'xs': 'px-2 py-0.5 text-xs',
        'sm': 'px-2.5 py-0.5 text-xs',
        'md': 'px-3 py-1 text-sm',
        'lg': 'px-4 py-1 text-sm'
      };

      const colorClass = colorClasses[color] || colorClasses['gray'];
      const sizeClass = sizeClasses[size] || sizeClasses['sm'];

      return `<span class="inline-flex items-center rounded-full font-medium ${colorClass} ${sizeClass}">${text}</span>`;
    },

    // Status badge with predefined colors
    statusBadge: (status) => {
      const statusConfig = {
        'active': { text: 'Active', color: 'success' },
        'inactive': { text: 'Inactive', color: 'gray' },
        'pending': { text: 'Pending', color: 'warning' },
        'completed': { text: 'Completed', color: 'success' },
        'cancelled': { text: 'Cancelled', color: 'error' },
        'suspended': { text: 'Suspended', color: 'error' },
        'transferred': { text: 'Transferred', color: 'blue' },
        'graduated': { text: 'Graduated', color: 'green' },
        'dropped': { text: 'Dropped', color: 'red' }
      };

      const config = statusConfig[status?.toLowerCase()] || { text: status || 'Unknown', color: 'gray' };
      return SRDesignSystem.components.badge(config.text, config.color);
    },

    // Icon with consistent styling
    icon: (iconClass, size = 'base', color = 'gray-400') => {
      const sizeClasses = {
        'xs': 'text-xs',
        'sm': 'text-sm',
        'base': 'text-base',
        'lg': 'text-lg',
        'xl': 'text-xl',
        '2xl': 'text-2xl',
        '3xl': 'text-3xl',
        '4xl': 'text-4xl'
      };

      const sizeClass = sizeClasses[size] || sizeClasses['base'];
      return `<i class="${iconClass} ${sizeClass} text-${color}"></i>`;
    },

    // Loading spinner
    spinner: (size = 'md', color = 'primary') => {
      const sizeClasses = {
        'sm': 'w-4 h-4',
        'md': 'w-6 h-6',
        'lg': 'w-8 h-8',
        'xl': 'w-12 h-12'
      };

      const sizeClass = sizeClasses[size] || sizeClasses['md'];
      return `<div class="${sizeClass} border-2 border-${color}-200 border-t-${color}-600 rounded-full animate-spin"></div>`;
    }
  },

  // Utility functions
  utils: {
    // Format date
    formatDate: (dateString) => {
      if (!dateString) return 'N/A';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-GB');
    },

    // Format time
    formatTime: (timeString) => {
      if (!timeString) return 'N/A';
      const time = new Date(`1970-01-01T${timeString}`);
      return time.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' });
    },

    // Format datetime
    formatDateTime: (dateTimeString) => {
      if (!dateTimeString) return 'N/A';
      const dateTime = new Date(dateTimeString);
      return dateTime.toLocaleString('en-GB');
    },

    // Generate unique ID
    generateId: () => {
      return 'id-' + Math.random().toString(36).substring(2, 11);
    },

    // Debounce function
    debounce: (func, wait) => {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    // Validate email
    validateEmail: (email) => {
      const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return re.test(email);
    },

    // Validate admission number
    validateAdmissionNumber: (admissionNumber) => {
      const re = /^[A-Z0-9]{4,20}$/;
      return re.test(admissionNumber);
    },

    // Calculate age from date of birth
    calculateAge: (dateOfBirth) => {
      if (!dateOfBirth) return null;
      const today = new Date();
      const birthDate = new Date(dateOfBirth);
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }

      return age;
    }
  },

  // Password validation utilities
  password: {
    // Validate password strength
    validate: (password) => {
      const requirements = {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        number: /[0-9]/.test(password),
        special: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
      };

      const metRequirements = Object.values(requirements).filter(Boolean).length;
      const hasMinLength = requirements.length;
      const hasThreeOfFour = metRequirements >= 3;

      return {
        isValid: hasMinLength && hasThreeOfFour,
        requirements,
        metRequirements,
        strength: metRequirements < 2 ? 'weak' : metRequirements < 4 ? 'medium' : 'strong'
      };
    },

    // Generate password strength indicator HTML
    getStrengthIndicator: (password) => {
      if (!password) return '';

      const validation = SRDesignSystem.password.validate(password);
      const { requirements, strength, isValid } = validation;

      const strengthColors = {
        weak: 'bg-red-500',
        medium: 'bg-yellow-500',
        strong: 'bg-green-500'
      };

      const strengthTexts = {
        weak: 'Weak',
        medium: 'Medium',
        strong: 'Strong'
      };

      return `
        <div class="mt-2 space-y-2">
          <!-- Strength Bar -->
          <div class="flex items-center space-x-2">
            <span class="text-sm font-medium text-gray-700">Strength:</span>
            <div class="flex-1 bg-gray-200 rounded-full h-2">
              <div class="${strengthColors[strength]} h-2 rounded-full transition-all duration-300"
                   style="width: ${validation.metRequirements * 25}%"></div>
            </div>
            <span class="text-sm font-medium ${isValid ? 'text-green-600' : 'text-red-600'}">
              ${strengthTexts[strength]}
            </span>
          </div>

          <!-- Requirements Checklist -->
          <div class="text-xs space-y-1">
            <div class="flex items-center space-x-2">
              <i class="fas ${requirements.length ? 'fa-check text-green-500' : 'fa-times text-red-500'}"></i>
              <span class="${requirements.length ? 'text-green-700' : 'text-red-700'}">
                At least 8 characters
              </span>
            </div>
            <div class="text-gray-600">Must include at least 3 of the following:</div>
            <div class="ml-4 space-y-1">
              <div class="flex items-center space-x-2">
                <i class="fas ${requirements.uppercase ? 'fa-check text-green-500' : 'fa-times text-gray-400'}"></i>
                <span class="${requirements.uppercase ? 'text-green-700' : 'text-gray-500'}">
                  Uppercase letter (A-Z)
                </span>
              </div>
              <div class="flex items-center space-x-2">
                <i class="fas ${requirements.lowercase ? 'fa-check text-green-500' : 'fa-times text-gray-400'}"></i>
                <span class="${requirements.lowercase ? 'text-green-700' : 'text-gray-500'}">
                  Lowercase letter (a-z)
                </span>
              </div>
              <div class="flex items-center space-x-2">
                <i class="fas ${requirements.number ? 'fa-check text-green-500' : 'fa-times text-gray-400'}"></i>
                <span class="${requirements.number ? 'text-green-700' : 'text-gray-500'}">
                  Number (0-9)
                </span>
              </div>
              <div class="flex items-center space-x-2">
                <i class="fas ${requirements.special ? 'fa-check text-green-500' : 'fa-times text-gray-400'}"></i>
                <span class="${requirements.special ? 'text-green-700' : 'text-gray-500'}">
                  Special character (!@#$%^&*)
                </span>
              </div>
            </div>
          </div>
        </div>
      `;
    },

    // Setup real-time password validation
    setupValidation: (passwordFieldId, confirmFieldId = null, containerId = null) => {
      const passwordField = document.getElementById(passwordFieldId);
      const confirmField = confirmFieldId ? document.getElementById(confirmFieldId) : null;
      const container = containerId ? document.getElementById(containerId) : passwordField?.parentElement;

      if (!passwordField) return;

      // Create strength indicator container
      let strengthContainer = container?.querySelector('.password-strength-indicator');
      if (!strengthContainer) {
        strengthContainer = document.createElement('div');
        strengthContainer.className = 'password-strength-indicator';
        passwordField.parentElement.appendChild(strengthContainer);
      }

      // Password input validation
      const validatePassword = () => {
        const password = passwordField.value;
        const validation = SRDesignSystem.password.validate(password);

        // Update strength indicator
        strengthContainer.innerHTML = SRDesignSystem.password.getStrengthIndicator(password);

        // Update field validation state
        if (password) {
          if (validation.isValid) {
            passwordField.setCustomValidity('');
            passwordField.classList.remove('border-red-500');
            passwordField.classList.add('border-green-500');
          } else {
            passwordField.setCustomValidity('Password does not meet requirements');
            passwordField.classList.remove('border-green-500');
            passwordField.classList.add('border-red-500');
          }
        } else {
          passwordField.setCustomValidity('');
          passwordField.classList.remove('border-red-500', 'border-green-500');
        }

        // Validate confirm password if present
        if (confirmField) {
          validateConfirmPassword();
        }
      };

      // Confirm password validation
      const validateConfirmPassword = () => {
        if (!confirmField) return;

        const password = passwordField.value;
        const confirmPassword = confirmField.value;

        if (confirmPassword) {
          if (password === confirmPassword) {
            confirmField.setCustomValidity('');
            confirmField.classList.remove('border-red-500');
            confirmField.classList.add('border-green-500');
          } else {
            confirmField.setCustomValidity('Passwords do not match');
            confirmField.classList.remove('border-green-500');
            confirmField.classList.add('border-red-500');
          }
        } else {
          confirmField.setCustomValidity('');
          confirmField.classList.remove('border-red-500', 'border-green-500');
        }
      };

      // Add event listeners
      passwordField.addEventListener('input', validatePassword);
      passwordField.addEventListener('blur', validatePassword);

      if (confirmField) {
        confirmField.addEventListener('input', validateConfirmPassword);
        confirmField.addEventListener('blur', validateConfirmPassword);
      }
    }
  }
};

// Export to global scope
window.SRDesignSystem = SRDesignSystem;
