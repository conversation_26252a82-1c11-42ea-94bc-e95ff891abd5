// SmartReport - CA Configuration Component
// Configure the number of Continuous Assessments (CAs) for each subject per term

// Uses global API services: window.CAConfigurationAPI, window.SubjectsAPI, etc.
// Uses global config: window.SRConfig
// Uses environment configuration: ../config/environment.js

const CAConfigurationComponent = {
  // Component state
  state: {
    configurations: [],
    subjects: [],
    classes: [],
    loading: false,
    currentAcademicYear: null,
    currentTerm: null
  },

  // Initialize component
  async init() {
    console.log('🔧 Initializing CA Configuration Component...');

    // Reset component state
    this.resetComponentState();

    // Check academic context first
    await window.AcademicContext.initialize();
    const activeYear = window.AcademicContext.getActiveAcademicYear();
    const activeTerm = window.AcademicContext.getActiveTerm();

    if (!activeYear || !activeTerm) {
      console.log('⚠️ No active academic year or term found - component will show error state');
      return;
    }

    // Load initial data first
    await this.loadInitialData();

    // Re-render the component with loaded data
    this.reRenderComponent();

    // Initialize directly - DOM should be ready due to lifecycle manager
    this.populateDropdowns();
    this.renderConfigurationsTable();
    this.initializeEventListeners();
  },

  // Reset component state
  resetComponentState() {
    this.state.configurations = [];
    this.state.subjects = [];
    this.state.classes = [];
    this.state.loading = false;
    this.state.currentAcademicYear = null;
    this.state.currentTerm = null;
    console.log('🔄 CA Configuration Component state reset');
  },

  // Re-render component with updated data
  reRenderComponent() {
    const contentArea = document.getElementById('content-area');
    if (contentArea) {
      contentArea.innerHTML = this.render();
    }
  },

  // Cleanup component
  cleanup() {
    console.log('🧹 Cleaning up CA Configuration Component...');
    this.resetComponentState();

    // Hide any open modals
    const modals = ['edit-ca-config-modal'];
    modals.forEach(modalId => {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.classList.add('hidden');
      }
    });

    console.log('✅ CA Configuration Component cleanup completed');
  },



  // Load initial data
  async loadInitialData() {
    try {
      this.state.loading = true;

      console.log('🔄 Loading CA configuration data...');

      // Get current academic context
      this.state.currentAcademicYear = window.AcademicContext.getActiveAcademicYear();
      this.state.currentTerm = window.AcademicContext.getActiveTerm();

      if (!this.state.currentAcademicYear || !this.state.currentTerm) {
        throw new Error('No active academic year or term found');
      }

      // Load data for current academic context using the API helper
      const apiContext = window.AcademicContext.getApiContext();
      const [configurations, oLevelSubjects, aLevelSubjects, classes] = await Promise.all([
        window.CAConfigurationAPI.getAll(apiContext),
        window.SubjectsAPI.oLevel.getAll(),
        window.SubjectsAPI.aLevel.getAll(),
        window.ClassesAPI.getAll()
      ]);

      // Combine subjects with level indicators
      const allSubjects = [
        ...(oLevelSubjects.data || []).map(s => ({ ...s, level: 'o_level' })),
        ...(aLevelSubjects.data || []).map(s => ({ ...s, level: 'a_level' }))
      ];

      this.state.configurations = configurations;
      this.state.subjects = { success: true, data: allSubjects };
      this.state.classes = classes;

      console.log('✅ CA configuration data loaded:', {
        configurations: configurations,
        configurationsCount: Array.isArray(configurations?.data) ? configurations.data.length : 'Not array',
        allSubjects: allSubjects.length,
        classes: Array.isArray(classes?.data) ? classes.data.length : 'Not array',
        academicYear: this.state.currentAcademicYear,
        term: this.state.currentTerm
      });

    } catch (error) {
      console.error('❌ Failed to load CA configuration data:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to load CA configuration data', 'error');
      }
    } finally {
      this.state.loading = false;
    }
  },

  // Render CA Configuration interface
  render() {
    // Check if academic context is available
    const hasAcademicContext = window.AcademicContext &&
                              window.AcademicContext.getActiveAcademicYear() &&
                              window.AcademicContext.getActiveTerm();

    if (!hasAcademicContext) {
      return this.renderAcademicContextError();
    }

    // Show loading state if data is still loading
    if (this.state.loading) {
      // Use centralized loading state from page router
      const container = document.getElementById('content-area');
      if (container && window.PageRouter) {
        window.PageRouter.showLoadingState(container, 'Loading CA configuration data...');
      }
      return '';
    }

    // Get current academic context for display
    const activeTerm = window.AcademicContext.getActiveTerm();

    return `
      <div class="space-y-6">
        <!-- Page Header -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="${SRDesignSystem.responsive.text['2xl']} font-bold text-gray-900">CA Configuration</h1>
              <p class="mt-1 ${SRDesignSystem.responsive.text.sm} text-gray-600">Configure Continuous Assessments for each subject - ${activeTerm?.name || 'Current Term'}</p>
            </div>
          </div>
        </div>

        <!-- Configuration Form -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-4 border-b border-primary-200">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-primary-900 flex items-center">
              ${SRDesignSystem.components.icon('fas fa-filter', 'base', 'primary-600')}
              <span class="ml-3">Select Parameters</span>
            </h3>
          </div>

          <div class="p-6">

          <!-- First Row: Education Level and Class -->
          <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap} mb-4">
            ${SRDesignSystem.forms.select('ca_level', 'Education Level', [], '', { required: false })}
            ${SRDesignSystem.forms.select('ca_class', 'Class', [], '', { required: false })}
          </div>

          <!-- Second Row: Subject and Paper (Paper hidden initially), Number of CAs initially here -->
          <div id="second_row" class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap} mb-4">
            ${SRDesignSystem.forms.select('ca_subject', 'Subject', [], '', { required: false })}
            <div id="ca_paper_container" style="display: none;">
              ${SRDesignSystem.forms.select('ca_paper', 'Paper', [], '', { required: false, id: 'ca_paper' })}
            </div>
            <div id="ca_number_of_cas_container">
              ${SRDesignSystem.forms.input('ca_number_of_cas', 'Number of CAs', '', {
                type: 'number',
                min: '1',
                max: '6',
                required: false,
                placeholder: 'e.g., 3',
                disabled: true
              })}
            </div>
          </div>

          <!-- Third Row: Number of CAs (when Paper appears, maintains original width) -->
          <div id="third_row" class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}" style="display: none;">
            <div></div> <!-- Empty space to maintain layout -->
            <div id="ca_number_of_cas_third_row_container"></div>
          </div>

            <div class="mt-6 flex justify-end">
              ${SRDesignSystem.forms.button('save-ca-config', 'Save Configuration', 'primary', {
                onclick: 'CAConfigurationComponent.saveConfiguration()'
              })}
            </div>
          </div>
        </div>

        <!-- Configurations Table -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-4 border-b border-primary-200">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-primary-900 flex items-center">
              ${SRDesignSystem.components.icon('fas fa-table', 'base', 'primary-600')}
              <span class="ml-3">Continuous Assessments</span>
            </h3>
          </div>
          <div class="overflow-x-auto max-h-96 overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="px-6 py-3 text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Class</th>
                  <th class="px-6 py-3 text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Subject</th>
                  <th class="px-6 py-3 text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Level</th>
                  <th class="px-6 py-3 text-center ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Total CAs</th>
                  <th class="px-6 py-3 text-center ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Actions</th>
                </tr>
              </thead>
              <tbody id="configurations-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Configurations will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Edit Configuration Modal -->
      <div id="edit-config-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-xl bg-white">
          <div class="mt-3">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">Edit Configuration</h3>
              <button onclick="CAConfigurationComponent.closeEditModal()" class="text-gray-400 hover:text-gray-600">
                ${SRDesignSystem.components.icon('fas fa-times', 'xl', 'current')}
              </button>
            </div>
            <form id="edit-config-form" class="mt-6 space-y-4">
              <input type="hidden" id="edit_config_id">
              <div id="edit-config-details" class="space-y-2 text-sm text-gray-600 mb-4">
                <!-- Configuration details will be populated here -->
              </div>
              ${SRDesignSystem.forms.input('edit_total_cas', 'Number of CAs', '', {
                type: 'number',
                min: '1',
                max: '6',
                required: true
              })}
              <div class="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
                ${SRDesignSystem.forms.button('cancel-edit-config', 'Cancel', 'secondary', {
                  type: 'button',
                  onclick: 'CAConfigurationComponent.closeEditModal()'
                })}
                ${SRDesignSystem.forms.button('update-config', 'Update', 'primary', {
                  type: 'submit'
                })}
              </div>
            </form>
          </div>
        </div>
      </div>
    `;
  },

  // Render academic context error
  renderAcademicContextError() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'CA Configuration',
          'Configure Continuous Assessments'
        )}

        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-600')}
            </div>
            <div class="ml-4">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-red-900">Academic Context Required</h3>
              <p class="text-red-700 mt-2">
                You cannot configure CA assessments without setting up the academic year and terms first.
              </p>
              <p class="text-red-700 mt-1">
                Please go to <strong>Academic Year → Setup</strong> to configure your academic year and terms before configuring CA assessments.
              </p>
              <div class="mt-4 text-center">
                ${SRDesignSystem.forms.button('setup-academic-ca', 'Setup Academic Year & Terms', 'danger', {
                  icon: 'fas fa-calendar-plus',
                  onclick: 'CAConfigurationComponent.navigateToAcademicSetup()'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Populate dropdowns
  populateDropdowns() {
    // Populate education levels
    const levelSelect = document.getElementById('ca_level');
    if (levelSelect) {
      levelSelect.innerHTML = '<option value="">Select Education Level</option>';

      // Get education levels from loaded data
      const educationLevels = [
        { code: 'o_level', name: 'O-Level' },
        { code: 'a_level', name: 'A-Level' }
      ];

      educationLevels.forEach(level => {
        const option = document.createElement('option');
        option.value = level.code;
        option.textContent = level.name;
        levelSelect.appendChild(option);
      });

      // Add event listener to filter classes by level
      levelSelect.addEventListener('change', () => {
        this.filterClassesByLevel(levelSelect.value);
        // Hide paper when education level is unselected
        if (!levelSelect.value) {
          this.hidePaperSelection();
        }
      });
    }

    // Populate classes (initially disabled until level is selected)
    const classSelect = document.getElementById('ca_class');
    if (classSelect) {
      classSelect.innerHTML = '<option value="">Select Education Level First</option>';
      classSelect.disabled = true;

      // Add event listener to filter subjects by class
      classSelect.addEventListener('change', () => {
        this.filterSubjectsByClass(classSelect.value, levelSelect.value);
        // Hide paper when class is unselected
        if (!classSelect.value) {
          this.hidePaperSelection();
        }
      });
    }

    // Populate subjects (initially disabled until class is selected)
    const subjectSelect = document.getElementById('ca_subject');
    if (subjectSelect) {
      subjectSelect.innerHTML = '<option value="">Select Class First</option>';
      subjectSelect.disabled = true;

      // Add event listener to handle subject change
      subjectSelect.addEventListener('change', () => {
        this.handleSubjectChange();
        // Hide paper when subject is unselected
        if (!subjectSelect.value) {
          this.hidePaperSelection();
        }
      });
    }

    // Populate papers (initially disabled until A-Level subject is selected)
    const paperContainer = document.getElementById('ca_paper_container');
    const paperSelect = document.getElementById('ca_paper');
    if (paperContainer && paperSelect) {
      paperSelect.innerHTML = '<option value="">Select Subject First</option>';
      paperSelect.disabled = true;
      // Hide the container, not the select element itself
      paperContainer.style.display = 'none';

      // Add event listener to handle paper change
      paperSelect.addEventListener('change', () => {
        this.handlePaperChange();
      });
    }

    // Initialize Number of CAs field as disabled
    const numberOfCAsInput = document.getElementById('ca_number_of_cas');
    if (numberOfCAsInput) {
      numberOfCAsInput.disabled = true;
      numberOfCAsInput.placeholder = 'Select Subject First';
    }
  },

  // Filter classes by education level
  filterClassesByLevel(level) {
    const classSelect = document.getElementById('ca_class');
    if (!classSelect) return;

    if (!level) {
      classSelect.innerHTML = '<option value="">Select Education Level First</option>';
      classSelect.disabled = true;
      this.resetSubjectDropdown();
      return;
    }

    // Enable class dropdown and populate with filtered classes
    classSelect.disabled = false;
    classSelect.innerHTML = '<option value="">Select Class</option>';

    const classes = this.state.classes.data || this.state.classes;
    if (Array.isArray(classes)) {
      const filteredClasses = classes.filter(classItem => {
        return classItem.education_level_code === level;
      });

      filteredClasses.forEach(classItem => {
        const option = document.createElement('option');
        option.value = classItem.id;
        option.textContent = classItem.name;
        option.dataset.educationLevel = classItem.education_level_code;
        classSelect.appendChild(option);
      });

      if (filteredClasses.length === 0) {
        classSelect.innerHTML = '<option value="">No classes available for this level</option>';
        classSelect.disabled = true;
      }
    }

    // Reset subject dropdown
    this.resetSubjectDropdown();
  },

  // Reset subject dropdown
  resetSubjectDropdown() {
    const subjectSelect = document.getElementById('ca_subject');
    if (subjectSelect) {
      subjectSelect.innerHTML = '<option value="">Select Class First</option>';
      subjectSelect.disabled = true;
    }
    // Hide paper selection and disable Number of CAs field
    this.hidePaperSelection();
    this.disableNumberOfCAsField();
  },

  // Filter subjects by class and level
  filterSubjectsByClass(classId, level) {
    const subjectSelect = document.getElementById('ca_subject');

    if (!subjectSelect) return;

    if (!classId || !level) {
      subjectSelect.innerHTML = '<option value="">Select Class First</option>';
      subjectSelect.disabled = true;
      this.disableNumberOfCAsField();
      return;
    }

    // Enable subject dropdown and populate with filtered subjects
    subjectSelect.disabled = false;
    subjectSelect.innerHTML = '<option value="">Select Subject</option>';

    const subjects = this.state.subjects.data || this.state.subjects;
    if (Array.isArray(subjects)) {
      const filteredSubjects = subjects.filter(subject => {
        return subject.level === level;
      });

      filteredSubjects.forEach(subject => {
        const option = document.createElement('option');
        option.value = subject.id;

        // Use only subject name without type
        option.textContent = subject.name;
        option.dataset.level = subject.level;
        option.dataset.subjectType = subject.subject_type || '';
        subjectSelect.appendChild(option);
      });

      if (filteredSubjects.length === 0) {
        subjectSelect.innerHTML = '<option value="">No subjects available for this level</option>';
        subjectSelect.disabled = true;
        this.disableNumberOfCAsField();
      } else {
        // Add event listener to subject dropdown to handle Number of CAs field
        subjectSelect.addEventListener('change', () => {
          this.handleSubjectChange();
        });
      }
    }

    // Initially disable Number of CAs field until subject is selected
    this.disableNumberOfCAsField();
  },

  // Handle subject selection change
  handleSubjectChange() {
    const subjectSelect = document.getElementById('ca_subject');
    const selectedSubject = subjectSelect.value;

    if (selectedSubject) {
      // Check if this is an A-level subject and show paper selection
      const selectedOption = subjectSelect.options[subjectSelect.selectedIndex];
      const level = selectedOption.dataset.level;

      if (level === 'a_level') {
        this.showPaperSelection(selectedSubject);
      } else {
        this.hidePaperSelection();
        this.enableNumberOfCAsField();
      }
    } else {
      this.hidePaperSelection();
      this.disableNumberOfCAsField();
    }
  },

  // Show paper selection for A-level subjects
  async showPaperSelection(subjectId) {
    try {
      // Show the paper container in second row
      const paperContainer = document.getElementById('ca_paper_container');
      const paperSelect = document.getElementById('ca_paper');
      const thirdRow = document.getElementById('third_row');

      if (paperContainer && paperSelect && thirdRow) {
        // Show paper container in second row
        paperContainer.style.display = 'block';
        paperSelect.disabled = false;
        paperSelect.innerHTML = '<option value="">Loading papers...</option>';

        // Show third row and move Number of CAs there
        thirdRow.style.display = 'block';
        this.moveNumberOfCAsToThirdRow();

        // Get papers for this subject from the API
        const response = await window.SubjectsAPI.aLevel.getPapers(subjectId);

        if (response.success && response.data) {
          paperSelect.innerHTML = '<option value="">Select Paper</option>';

          response.data.forEach(paper => {
            const option = document.createElement('option');
            option.value = paper.id;
            option.textContent = paper.paper_name || `Paper ${paper.paper_number}`;
            option.dataset.paperNumber = paper.paper_number;
            paperSelect.appendChild(option);
          });

          console.log(`📄 Loaded ${response.data.length} papers for subject ${subjectId}`);

        } else {
          paperSelect.innerHTML = '<option value="">No papers available</option>';
          console.error('Failed to load papers:', response.message);
        }
      }


      this.disableNumberOfCAsField(); // Disable until paper is selected

    } catch (error) {
      console.error('Error showing paper selection:', error);
    }
  },

  // Hide paper selection
  hidePaperSelection() {
    const paperContainer = document.getElementById('ca_paper_container');
    const paperSelect = document.getElementById('ca_paper');
    const thirdRow = document.getElementById('third_row');

    if (paperContainer && paperSelect && thirdRow) {
      // Hide paper container in second row
      paperContainer.style.display = 'none';
      paperSelect.disabled = true;
      paperSelect.innerHTML = '<option value="">Select Subject First</option>';

      // Hide third row and move Number of CAs back to second row
      thirdRow.style.display = 'none';
      this.moveNumberOfCAsToSecondRow();
    }
  },

  // Handle paper selection change
  handlePaperChange() {
    const paperSelect = document.getElementById('ca_paper');
    if (paperSelect && paperSelect.value) {
      this.enableNumberOfCAsField();
    } else {
      this.disableNumberOfCAsField();
    }
  },

  // Move Number of CAs field to third row (when Paper appears)
  moveNumberOfCAsToThirdRow() {
    const casContainer = document.getElementById('ca_number_of_cas_container');
    const thirdRowContainer = document.getElementById('ca_number_of_cas_third_row_container');

    if (casContainer && thirdRowContainer) {
      // Move the container to third row
      thirdRowContainer.appendChild(casContainer);
    }
  },

  // Move Number of CAs field back to second row (when Paper is hidden)
  moveNumberOfCAsToSecondRow() {
    const casContainer = document.getElementById('ca_number_of_cas_container');
    const secondRow = document.getElementById('second_row');

    if (casContainer && secondRow) {
      // Move the container back to second row
      secondRow.appendChild(casContainer);
    }
  },

  // Enable Number of CAs field
  enableNumberOfCAsField() {
    const numberOfCAsInput = document.getElementById('ca_number_of_cas');
    if (numberOfCAsInput) {
      numberOfCAsInput.disabled = false;
      numberOfCAsInput.placeholder = 'e.g., 3';
    }
  },

  // Disable Number of CAs field
  disableNumberOfCAsField() {
    const numberOfCAsInput = document.getElementById('ca_number_of_cas');
    if (numberOfCAsInput) {
      numberOfCAsInput.disabled = true;
      numberOfCAsInput.value = '';
      numberOfCAsInput.placeholder = 'Select Subject First';
    }
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Edit configuration form submission
    const editConfigForm = document.getElementById('edit-config-form');
    if (editConfigForm) {
      editConfigForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.updateConfiguration();
      });
    }
  },

  // Load configurations for current academic context
  async loadConfigurations() {
    try {
      // Use the academic context API helper method
      const apiContext = window.AcademicContext.getApiContext();

      const result = await window.CAConfigurationAPI.getAll(apiContext);
      this.state.configurations = result;
    } catch (error) {
      console.error('Failed to load configurations:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to load configurations', 'error');
      }
    }
  },

  // Render configurations table
  renderConfigurationsTable() {
    const tbody = document.getElementById('configurations-table-body');
    if (!tbody) return;

    const configurations = this.state.configurations.data || this.state.configurations;
    if (!Array.isArray(configurations) || configurations.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="5" class="px-6 py-12 text-center text-gray-500">
            <div class="flex flex-col items-center">
              <i class="fas fa-clipboard-list text-4xl mb-4 text-gray-300"></i>
              <p class="text-lg font-medium">No CA configurations found</p>
              <p class="text-sm">Start by configuring continuous assessments for your class-subjects.</p>
            </div>
          </td>
        </tr>
      `;
      return;
    }

    tbody.innerHTML = configurations.map(config => {
      const actions = [
        {
          label: 'Edit',
          icon: 'fas fa-edit',
          onclick: `CAConfigurationComponent.editConfiguration(${config.id})`,
          color: 'blue'
        },
        {
          label: 'Delete',
          icon: 'fas fa-trash',
          onclick: `CAConfigurationComponent.deleteConfiguration(${config.id})`,
          color: 'red'
        }
      ];

      return `
        <tr>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <span class="font-medium">${config.class_name || 'N/A'}</span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <div class="flex flex-col">
              <span class="font-medium">${config.subject_name || 'N/A'}</span>
              ${config.subject_level === 'a_level' && config.subject_type ?
                `<span class="text-xs text-gray-500">${config.subject_type}</span>` : ''
              }
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              config.subject_level === 'o_level' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
            }">
              ${config.subject_level ? config.subject_level.replace('_', '-').toUpperCase() : 'N/A'}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium text-gray-900">
            ${config.total_cas}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
            ${SRDesignSystem.tables.generateKebabMenu(`ca-config-${config.id}`, actions, config)}
          </td>
        </tr>
      `;
    }).join('');
  },

  // Generate dropdown action menu for CA configuration actions
  generateCAActionDropdown(configId, actions) {
    const dropdownId = `dropdown-ca-${configId}`;

    return `
      <div class="relative inline-block text-left">
        <button type="button"
                onclick="CAConfigurationComponent.toggleDropdown('${dropdownId}')"
                class="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors duration-150"
                title="More actions">
          <i class="fas fa-ellipsis-v text-sm"></i>
        </button>

        <div id="${dropdownId}"
             class="hidden fixed z-[9999] w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1"
             style="transform: translate(-100%, -100%);">
          ${actions.map(action => `
            <button onclick="CAConfigurationComponent.closeDropdown('${dropdownId}'); ${action.onclick}"
                    class="flex items-center w-full px-4 py-3 text-sm text-left hover:bg-gray-50 transition-colors duration-150 ${action.color || 'text-gray-700'}">
              <i class="${action.icon} w-4 mr-3"></i>
              ${action.label}
            </button>
          `).join('')}
        </div>
      </div>
    `;
  },

  // Toggle dropdown visibility
  toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (!dropdown) return;

    // Close all other dropdowns first
    this.closeAllDropdowns();

    // Toggle current dropdown
    dropdown.classList.toggle('hidden');

    // Position dropdown next to the button if dropdown is now open
    if (!dropdown.classList.contains('hidden')) {
      const button = dropdown.previousElementSibling;
      if (button) {
        const buttonRect = button.getBoundingClientRect();

        // Position dropdown to the left of the kebab icon, aligned to its top
        dropdown.style.position = 'fixed';
        dropdown.style.right = `${window.innerWidth - buttonRect.left + 8}px`; // 8px gap to the left of button
        dropdown.style.top = `${buttonRect.top}px`; // Same vertical position as button
        dropdown.style.zIndex = '1000';
      }

      setTimeout(() => {
        document.addEventListener('click', this.handleOutsideClick);
      }, 10);
    }
  },

  // Close specific dropdown
  closeDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (dropdown) {
      dropdown.classList.add('hidden');
    }
    document.removeEventListener('click', this.handleOutsideClick);
  },

  // Close all dropdowns
  closeAllDropdowns() {
    const dropdowns = document.querySelectorAll('[id^="dropdown-ca-"]');
    dropdowns.forEach(dropdown => {
      dropdown.classList.add('hidden');
    });
    document.removeEventListener('click', this.handleOutsideClick);
  },

  // Handle clicks outside dropdown
  handleOutsideClick(event) {
    const isDropdownButton = event.target.closest('[onclick*="toggleDropdown"]');
    const isDropdownContent = event.target.closest('[id^="dropdown-ca-"]');

    if (!isDropdownButton && !isDropdownContent) {
      CAConfigurationComponent.closeAllDropdowns();
    }
  },

  // Save new configuration
  async saveConfiguration() {
    try {
      if (window.SRDesignSystem && window.SRDesignSystem.forms) {
        window.SRDesignSystem.forms.setButtonLoading('save-ca-config', true);
      }

      // Get form values from the new form structure
      const level = document.getElementById('ca_level').value;
      const classId = document.getElementById('ca_class').value;
      const subjectId = document.getElementById('ca_subject').value;
      const numberOfCas = document.getElementById('ca_number_of_cas').value;

      // Get paper ID for A-level subjects
      let subjectPaperId = null;
      if (level === 'a_level') {
        const paperSelect = document.getElementById('ca_paper');
        if (paperSelect) {
          subjectPaperId = paperSelect.value;
        }
      }

      // Validate required fields
      let requiredFields = 'Education Level, Class, Subject, Number of CAs';
      if (level === 'a_level') {
        requiredFields += ', Paper';
        if (!level || !classId || !subjectId || !numberOfCas || !subjectPaperId) {
          if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
            window.SRDesignSystem.notifications.show(`Please fill in all required fields (${requiredFields})`, 'error');
          }
          return;
        }
      } else {
        if (!level || !classId || !subjectId || !numberOfCas) {
          if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
            window.SRDesignSystem.notifications.show(`Please fill in all required fields (${requiredFields})`, 'error');
          }
          return;
        }
      }

      // Map form fields to API fields using academic context
      const apiContext = window.AcademicContext.getApiContext();
      const apiData = {
        class_id: classId,
        ...apiContext,
        total_cas: parseInt(numberOfCas)
      };

      // Handle O-Level vs A-Level data structure
      if (level === 'o_level') {
        // O-Level: subject-based configuration
        apiData.subject_id = subjectId;
        apiData.education_level = 'o_level';
      } else if (level === 'a_level') {
        // A-Level: paper-based configuration
        if (!subjectPaperId) {
          if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
            window.SRDesignSystem.notifications.show('Please select a paper for A-Level subjects', 'error');
          }
          return;
        }
        apiData.subject_paper_id = parseInt(subjectPaperId);
        apiData.education_level = 'a_level';
        // For A-Level, we still need the subject_id for reference
        apiData.subject_id = subjectId;
      }

      const result = await window.CAConfigurationAPI.create(apiData);

      if (result.success) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('CA configuration saved successfully!', 'success');
        }

        // Reset form and reload data
        document.getElementById('ca_level').value = '';
        document.getElementById('ca_class').value = '';
        document.getElementById('ca_subject').value = '';
        document.getElementById('ca_number_of_cas').value = '';

        // Reset paper selection
        const paperSelect = document.getElementById('ca_paper');
        if (paperSelect) {
          paperSelect.value = '';
        }
        this.hidePaperSelection();

        // Reset dropdown states
        document.getElementById('ca_class').disabled = true;
        document.getElementById('ca_subject').disabled = true;

        await this.loadConfigurations();
        this.renderConfigurationsTable();
      } else {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to save configuration', 'error');
        }
      }
    } catch (error) {
      console.error('Save configuration error:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to save configuration', 'error');
      }
    } finally {
      if (window.SRDesignSystem && window.SRDesignSystem.forms) {
        window.SRDesignSystem.forms.setButtonLoading('save-ca-config', false);
      }
    }
  },









  // Edit configuration
  editConfiguration(configId) {
    const configurations = this.state.configurations.data || this.state.configurations;
    const config = configurations.find(c => c.id === configId);

    if (!config) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Configuration not found', 'error');
      }
      return;
    }

    // Populate edit modal
    document.getElementById('edit_config_id').value = config.id;
    document.getElementById('edit_total_cas').value = config.total_cas;

    // Show configuration details
    const detailsContainer = document.getElementById('edit-config-details');
    if (detailsContainer) {
      detailsContainer.innerHTML = `
        <div><strong>Academic Year:</strong> ${config.academic_year_name}</div>
        <div><strong>Term:</strong> ${config.term_name}</div>
        <div><strong>Subject:</strong> ${config.subject_name}</div>
        <div><strong>Level:</strong> ${config.subject_level ? config.subject_level.replace('_', '-').toUpperCase() : 'N/A'}</div>
      `;
    }

    // Show modal
    const modal = document.getElementById('edit-config-modal');
    if (modal) {
      modal.classList.remove('hidden');
    }
  },

  // Close edit modal
  closeEditModal() {
    const modal = document.getElementById('edit-config-modal');
    if (modal) {
      modal.classList.add('hidden');
      document.getElementById('edit-config-form').reset();
    }
  },

  // Update configuration
  async updateConfiguration() {
    try {
      if (window.SRDesignSystem && window.SRDesignSystem.forms) {
        window.SRDesignSystem.forms.setButtonLoading('update-config', true);
      }

      const configId = document.getElementById('edit_config_id').value;
      const totalCAs = document.getElementById('edit_total_cas').value;

      if (!configId || !totalCAs) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Please fill in all required fields', 'error');
        }
        return;
      }

      // Get the education level from the configuration being edited
      const configRow = this.state.configurations.data?.find(config => config.id == configId);
      const educationLevel = configRow?.subject_level || 'o_level';

      const result = await window.CAConfigurationAPI.update(configId, {
        total_cas: parseInt(totalCAs),
        education_level: educationLevel
      });

      if (result.success) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Configuration updated successfully!', 'success');
        }

        this.closeEditModal();
        await this.loadConfigurations();
        this.renderConfigurationsTable();
      } else {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to update configuration', 'error');
        }
      }
    } catch (error) {
      console.error('Update configuration error:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to update configuration', 'error');
      }
    } finally {
      if (window.SRDesignSystem && window.SRDesignSystem.forms) {
        window.SRDesignSystem.forms.setButtonLoading('update-config', false);
      }
    }
  },



  // Delete configuration
  async deleteConfiguration(configId) {
    if (!confirm('Are you sure you want to delete this CA configuration? This action cannot be undone.')) {
      return;
    }

    try {
      const result = await window.CAConfigurationAPI.delete(configId);

      if (result.success) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Configuration deleted successfully!', 'success');
        }

        await this.loadConfigurations();
        this.renderConfigurationsTable();
      } else {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to delete configuration', 'error');
        }
      }
    } catch (error) {
      console.error('Delete configuration error:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to delete configuration', 'error');
      }
    }
  },

  // Navigate to academic year setup
  navigateToAcademicSetup() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('academic-year-setup');
    }
  },


};

// Export to global scope
window.CAConfigurationComponent = CAConfigurationComponent;
