const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');

class PDFGenerator {
  constructor() {
    this.doc = null;
    this.pageMargin = 50;
    this.currentY = 0;
  }

  // Generate student report card PDF
  async generateStudentReportCard(reportData, outputPath) {
    try {
      this.doc = new PDFDocument({ margin: this.pageMargin });
      this.doc.pipe(fs.createWriteStream(outputPath));
      
      // Header
      this.addHeader(reportData.school_info);
      
      // Student Information
      this.addStudentInfo(reportData.student);
      
      // Academic Information
      this.addAcademicInfo(reportData.academic_info);
      
      // Grades Table
      this.addGradesTable(reportData.grades);
      
      // Summary
      this.addSummary(reportData.summary);
      
      // Attendance
      if (reportData.attendance) {
        this.addAttendance(reportData.attendance);
      }
      
      // Footer
      this.addFooter();
      
      this.doc.end();
      
      return { success: true, path: outputPath };
    } catch (error) {
      console.error('PDF generation error:', error);
      return { success: false, error: error.message };
    }
  }

  addHeader(schoolInfo) {
    const centerX = this.doc.page.width / 2;
    
    // School Logo (if available)
    // this.doc.image('path/to/logo.png', 50, 50, { width: 60 });
    
    // School Name
    this.doc.fontSize(18)
           .font('Helvetica-Bold')
           .text(schoolInfo.name || 'Nyabikoni Secondary School', centerX, 60, { align: 'center' });
    
    // School Address
    this.doc.fontSize(12)
           .font('Helvetica')
           .text(schoolInfo.address || 'P.O. Box 304 Kabale Uganda', centerX, 85, { align: 'center' });
    
    // Report Title
    this.doc.fontSize(16)
           .font('Helvetica-Bold')
           .text('STUDENT REPORT CARD', centerX, 120, { align: 'center' });
    
    this.currentY = 160;
  }

  addStudentInfo(student) {
    const leftCol = this.pageMargin;
    const rightCol = this.doc.page.width / 2 + 20;
    
    this.doc.fontSize(12).font('Helvetica-Bold');
    
    // Left column
    this.doc.text('Student Information:', leftCol, this.currentY);
    this.currentY += 20;
    
    this.doc.font('Helvetica');
    this.doc.text(`Name: ${student.full_name}`, leftCol, this.currentY);
    this.currentY += 15;
    this.doc.text(`Admission No: ${student.admission_number}`, leftCol, this.currentY);
    this.currentY += 15;
    this.doc.text(`Class: ${student.class_name}`, leftCol, this.currentY);
    
    // Right column
    this.currentY -= 30;
    this.doc.text(`Gender: ${student.gender}`, rightCol, this.currentY);
    this.currentY += 15;
    this.doc.text(`Date of Birth: ${student.date_of_birth}`, rightCol, this.currentY);
    this.currentY += 15;
    this.doc.text(`LIN: ${student.learners_identification_number || 'N/A'}`, rightCol, this.currentY);
    
    this.currentY += 30;
  }

  addAcademicInfo(academicInfo) {
    this.doc.fontSize(12).font('Helvetica-Bold');
    this.doc.text('Academic Information:', this.pageMargin, this.currentY);
    this.currentY += 20;
    
    this.doc.font('Helvetica');
    this.doc.text(`Academic Year: ${academicInfo.academic_year}`, this.pageMargin, this.currentY);
    this.doc.text(`Term: ${academicInfo.term}`, this.doc.page.width / 2 + 20, this.currentY);
    this.currentY += 15;
    this.doc.text(`Report Type: ${academicInfo.report_type}`, this.pageMargin, this.currentY);
    this.doc.text(`Generated: ${new Date().toLocaleDateString()}`, this.doc.page.width / 2 + 20, this.currentY);
    
    this.currentY += 30;
  }

  addGradesTable(grades) {
    this.doc.fontSize(12).font('Helvetica-Bold');
    this.doc.text('ACADEMIC PERFORMANCE', this.pageMargin, this.currentY);
    this.currentY += 25;
    
    // Table headers for Two-Tier Assessment System
    const tableTop = this.currentY;
    const tableLeft = this.pageMargin;
    const colWidths = [150, 80, 100, 80, 80, 80];
    const headers = ['Subject', 'CA (20%)', 'Summative (80%)', 'Final %', 'Grade', 'Descriptor'];
    
    // Draw table headers
    this.doc.fontSize(10).font('Helvetica-Bold');
    let currentX = tableLeft;
    
    headers.forEach((header, i) => {
      this.doc.rect(currentX, tableTop, colWidths[i], 25).stroke();
      this.doc.text(header, currentX + 5, tableTop + 8, { width: colWidths[i] - 10 });
      currentX += colWidths[i];
    });
    
    this.currentY = tableTop + 25;
    
    // Draw table rows
    this.doc.font('Helvetica');
    grades.forEach((grade, index) => {
      const rowTop = this.currentY;
      currentX = tableLeft;
      
      const rowData = [
        grade.subject_name,
        grade.ca_score ? `${grade.ca_score.toFixed(1)} (${(grade.ca_score * 20 / 3).toFixed(1)}%)` : 'N/A',
        grade.summative_score ? `${grade.summative_score.toFixed(1)}%` : 'N/A',
        grade.final_score ? grade.final_score.toFixed(1) + '%' : 'N/A',
        grade.grade_letter || 'N/A',
        grade.grade_descriptor || 'N/A'
      ];
      
      rowData.forEach((data, i) => {
        this.doc.rect(currentX, rowTop, colWidths[i], 20).stroke();
        this.doc.text(data, currentX + 5, rowTop + 6, { width: colWidths[i] - 10 });
        currentX += colWidths[i];
      });
      
      this.currentY += 20;
    });
    
    this.currentY += 20;
  }

  addSummary(summary) {
    this.doc.fontSize(12).font('Helvetica-Bold');
    this.doc.text('PERFORMANCE SUMMARY', this.pageMargin, this.currentY);
    this.currentY += 25;
    
    this.doc.font('Helvetica');
    this.doc.text(`Total Subjects: ${summary.total_subjects}`, this.pageMargin, this.currentY);
    this.currentY += 15;
    this.doc.text(`Average Points Score: ${summary.average_points_score ? summary.average_points_score.toFixed(2) : 'N/A'}`, this.pageMargin, this.currentY);
    this.currentY += 15;
    this.doc.text(`Overall Achievement: ${summary.overall_achievement || 'N/A'}`, this.pageMargin, this.currentY);
    this.currentY += 15;
    this.doc.text(`CA Percentage: ${summary.ca_percentage ? summary.ca_percentage.toFixed(1) + '%' : 'N/A'}`, this.pageMargin, this.currentY);
    
    this.currentY += 30;
  }

  addAttendance(attendance) {
    this.doc.fontSize(12).font('Helvetica-Bold');
    this.doc.text('ATTENDANCE SUMMARY', this.pageMargin, this.currentY);
    this.currentY += 25;
    
    this.doc.font('Helvetica');
    this.doc.text(`Days Present: ${attendance.present_days || 0}`, this.pageMargin, this.currentY);
    this.doc.text(`Days Absent: ${attendance.absent_days || 0}`, this.doc.page.width / 2 + 20, this.currentY);
    this.currentY += 15;
    this.doc.text(`Total School Days: ${attendance.total_days || 0}`, this.pageMargin, this.currentY);
    this.doc.text(`Attendance %: ${attendance.attendance_percentage ? attendance.attendance_percentage.toFixed(1) + '%' : 'N/A'}`, this.doc.page.width / 2 + 20, this.currentY);
    
    this.currentY += 30;
  }

  addFooter() {
    const footerY = this.doc.page.height - 100;
    
    // Signature lines
    this.doc.fontSize(10).font('Helvetica');
    this.doc.text('Class Teacher: ________________________', this.pageMargin, footerY);
    this.doc.text('Date: ________________', this.doc.page.width / 2 + 20, footerY);
    
    this.doc.text('Head Teacher: ________________________', this.pageMargin, footerY + 30);
    this.doc.text('School Stamp', this.doc.page.width / 2 + 20, footerY + 30);
    
    // System info
    this.doc.fontSize(8).font('Helvetica');
    this.doc.text('Generated by SmartReport v1.0.0', 
                  this.pageMargin, this.doc.page.height - 30, 
                  { align: 'center', width: this.doc.page.width - (this.pageMargin * 2) });
  }

  // Generate class summary report
  async generateClassSummaryReport(classData, outputPath) {
    try {
      this.doc = new PDFDocument({ margin: this.pageMargin });
      this.doc.pipe(fs.createWriteStream(outputPath));
      
      // Header
      this.addHeader(classData.school_info);
      
      // Class Information
      this.addClassInfo(classData.class_info);
      
      // Class Statistics
      this.addClassStatistics(classData.statistics);
      
      // Student Rankings
      if (classData.rankings) {
        this.addStudentRankings(classData.rankings);
      }
      
      // Footer
      this.addFooter();
      
      this.doc.end();
      
      return { success: true, path: outputPath };
    } catch (error) {
      console.error('Class report PDF generation error:', error);
      return { success: false, error: error.message };
    }
  }

  addClassInfo(classInfo) {
    this.doc.fontSize(12).font('Helvetica-Bold');
    this.doc.text('Class Information:', this.pageMargin, this.currentY);
    this.currentY += 20;
    
    this.doc.font('Helvetica');
    this.doc.text(`Class: ${classInfo.name}`, this.pageMargin, this.currentY);
    this.doc.text(`Level: ${classInfo.level}`, this.doc.page.width / 2 + 20, this.currentY);
    this.currentY += 15;
    this.doc.text(`Class Teacher: ${classInfo.class_teacher || 'N/A'}`, this.pageMargin, this.currentY);
    this.doc.text(`Total Students: ${classInfo.total_students}`, this.doc.page.width / 2 + 20, this.currentY);
    
    this.currentY += 30;
  }

  addClassStatistics(statistics) {
    this.doc.fontSize(12).font('Helvetica-Bold');
    this.doc.text('CLASS PERFORMANCE STATISTICS', this.pageMargin, this.currentY);
    this.currentY += 25;
    
    this.doc.font('Helvetica');
    this.doc.text(`Class Average: ${statistics.class_average ? statistics.class_average.toFixed(2) : 'N/A'}%`, this.pageMargin, this.currentY);
    this.currentY += 15;
    this.doc.text(`Highest Score: ${statistics.highest_score ? statistics.highest_score.toFixed(2) : 'N/A'}%`, this.pageMargin, this.currentY);
    this.currentY += 15;
    this.doc.text(`Lowest Score: ${statistics.lowest_score ? statistics.lowest_score.toFixed(2) : 'N/A'}%`, this.pageMargin, this.currentY);
    this.currentY += 15;
    
    // Grade distribution
    if (statistics.grade_distribution) {
      this.doc.text('Grade Distribution:', this.pageMargin, this.currentY);
      this.currentY += 15;
      
      Object.entries(statistics.grade_distribution).forEach(([grade, count]) => {
        this.doc.text(`  ${grade}: ${count} students`, this.pageMargin + 20, this.currentY);
        this.currentY += 12;
      });
    }
    
    this.currentY += 20;
  }

  addStudentRankings(rankings) {
    this.doc.fontSize(12).font('Helvetica-Bold');
    this.doc.text('STUDENT RANKINGS', this.pageMargin, this.currentY);
    this.currentY += 25;
    
    // Table headers
    const tableTop = this.currentY;
    const tableLeft = this.pageMargin;
    const colWidths = [50, 200, 100, 80, 80];
    const headers = ['Rank', 'Student Name', 'Admission No', 'Average', 'Grade'];
    
    // Draw table headers
    this.doc.fontSize(10).font('Helvetica-Bold');
    let currentX = tableLeft;
    
    headers.forEach((header, i) => {
      this.doc.rect(currentX, tableTop, colWidths[i], 25).stroke();
      this.doc.text(header, currentX + 5, tableTop + 8, { width: colWidths[i] - 10 });
      currentX += colWidths[i];
    });
    
    this.currentY = tableTop + 25;
    
    // Draw table rows
    this.doc.font('Helvetica');
    rankings.slice(0, 20).forEach((student, index) => { // Show top 20
      const rowTop = this.currentY;
      currentX = tableLeft;
      
      const rowData = [
        (index + 1).toString(),
        student.full_name,
        student.admission_number,
        student.average_score ? student.average_score.toFixed(2) + '%' : 'N/A',
        student.overall_grade || 'N/A'
      ];
      
      rowData.forEach((data, i) => {
        this.doc.rect(currentX, rowTop, colWidths[i], 20).stroke();
        this.doc.text(data, currentX + 5, rowTop + 6, { width: colWidths[i] - 10 });
        currentX += colWidths[i];
      });
      
      this.currentY += 20;
      
      // Add new page if needed
      if (this.currentY > this.doc.page.height - 150) {
        this.doc.addPage();
        this.currentY = this.pageMargin;
      }
    });
  }
}

module.exports = PDFGenerator;
