// SmartReport Layout System
// Simple, responsive layout components

const Layout = {
  // Layout state
  state: {
    sidebarCollapsed: false,
    currentPage: 'dashboard',
    currentUser: null,
    currentAcademicYear: null,
    currentTerm: null,

  },

  // Initialize the layout system
  init() {
    this.addLayoutStyles();
    this.createLayout();
    this.initializeEventListeners();
    this.initializeNavigation();
    this.updateAcademicInfo();
  },

  // Add layout-specific styles
  addLayoutStyles() {
    if (document.getElementById('layout-styles')) return;

    const style = document.createElement('style');
    style.id = 'layout-styles';
    style.textContent = `
      /* Ensure proper layout structure */
      html, body {
        height: 100%;
        overflow: hidden;
      }

      #app-layout {
        height: 100vh;
        overflow: hidden;
      }

      /* Sidebar styles */
      #sidebar {
        transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      #sidebar.sidebar-collapsed {
        width: 4rem !important;
      }

      /* Logo styling */
      .sidebar-logo {
        transition: all 0.3s ease;
      }

      #sidebar.sidebar-collapsed .sidebar-logo {
        width: 2rem;
        height: 2rem;
      }

      /* Content area scrolling */
      #content-area {
        min-height: 0; /* Allow flex shrinking */
        overflow-y: auto;
        overflow-x: hidden;
        max-height: 100%; /* Ensure it doesn't exceed container */
      }

      /* Ensure main content container allows scrolling */
      #main-content {
        min-height: 0; /* Allow flex shrinking */
        overflow: hidden; /* Prevent main content from scrolling */
      }

      /* Ensure app layout allows proper flex behavior */
      #app-layout {
        min-height: 100vh;
        max-height: 100vh;
      }

      /* Custom scrollbar for content area - Increased size for better UX */
      #content-area::-webkit-scrollbar {
        width: 12px;
      }

      #content-area::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 6px;
      }

      #content-area::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 6px;
        border: 2px solid #f1f5f9;
      }

      #content-area::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
      }

      #content-area::-webkit-scrollbar-thumb:active {
        background: #64748b;
      }

      /* Navigation menu scrolling - Increased size for better UX */
      nav {
        overflow-y: auto;
        overflow-x: hidden;
      }

      nav::-webkit-scrollbar {
        width: 8px;
      }

      nav::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 4px;
      }

      nav::-webkit-scrollbar-thumb {
        background: #e2e8f0;
        border-radius: 4px;
        border: 1px solid transparent;
      }

      nav::-webkit-scrollbar-thumb:hover {
        background: #cbd5e1;
      }

      nav::-webkit-scrollbar-thumb:active {
        background: #94a3b8;
      }

      /* Custom scrollbar for modals - Increased size */
      .custom-scrollbar::-webkit-scrollbar {
        width: 8px;
      }

      .custom-scrollbar::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 4px;
      }

      .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #e2e8f0;
        border-radius: 4px;
        border: 1px solid transparent;
      }

      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #cbd5e1;
      }

      .custom-scrollbar::-webkit-scrollbar-thumb:active {
        background: #94a3b8;
      }

      /* Enhanced navigation styling */
      .nav-section {
        position: relative;
        margin-bottom: 8px;
      }

      .nav-section:not(:last-child)::after {
        content: '';
        position: absolute;
        bottom: -4px;
        left: 16px;
        right: 16px;
        height: 1px;
        background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
        opacity: 0.5;
      }

      #sidebar.sidebar-collapsed .nav-section::after {
        display: none;
      }

      /* Section title styling improvements */
      .nav-section button {
        font-weight: 600;
        letter-spacing: 0.025em;
      }

      .nav-section .nav-text {
        font-size: 0.875rem;
        font-weight: 600;
      }

      /* Improved hover effects */
      .nav-item button:hover,
      .nav-section button:hover {
        transform: translateX(2px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      .nav-item.active button {
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
      }

      /* Better text truncation for long titles */
      .nav-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
      }

      /* Smooth transitions for all navigation elements */
      .nav-item, .nav-section, .nav-text {
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      }

      /* Special styling for dashboard navigation item */
      .dashboard-nav-item {
        position: relative;
      }

      .dashboard-nav-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1));
        border-radius: 8px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .dashboard-nav-item:hover::before {
        opacity: 1;
      }

      .dashboard-nav-item button {
        position: relative;
        z-index: 1;
      }

      /* Dashboard button glow effect */
      .dashboard-nav-item.active button {
        box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;
      }

      .dashboard-nav-item button:not(.active):hover {
        box-shadow: 0 2px 10px rgba(59, 130, 246, 0.2);
      }

      /* Separator after dashboard */
      .dashboard-nav-item::after {
        content: '';
        position: absolute;
        bottom: -12px;
        left: 16px;
        right: 16px;
        height: 2px;
        background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
        border-radius: 1px;
      }

      #sidebar.sidebar-collapsed .dashboard-nav-item::after {
        left: 8px;
        right: 8px;
      }

      /* Navigation layout - Dashboard fixed, sections scrollable */
      #navigation-menu {
        display: flex;
        flex-direction: column;
        height: 100%;
      }

      .dashboard-nav-item {
        flex-shrink: 0; /* Dashboard doesn't shrink */
      }

      .nav-sections-container {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        padding-top: 8px;
      }

      .nav-sections-container::-webkit-scrollbar {
        width: 8px;
      }

      .nav-sections-container::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 4px;
      }

      .nav-sections-container::-webkit-scrollbar-thumb {
        background: #e2e8f0;
        border-radius: 4px;
        border: 1px solid transparent;
      }

      .nav-sections-container::-webkit-scrollbar-thumb:hover {
        background: #cbd5e1;
      }

      .nav-sections-container::-webkit-scrollbar-thumb:active {
        background: #94a3b8;
      }

      /* Hide scrollbars when sidebar is collapsed */
      #sidebar.sidebar-collapsed nav::-webkit-scrollbar {
        width: 0px;
        background: transparent;
      }

      #sidebar.sidebar-collapsed .custom-scrollbar::-webkit-scrollbar {
        width: 0px;
        background: transparent;
      }

      #sidebar.sidebar-collapsed .nav-sections-container::-webkit-scrollbar {
        width: 0px;
        background: transparent;
      }

      /* Also hide scrollbar for Firefox */
      #sidebar.sidebar-collapsed nav {
        scrollbar-width: none;
      }

      #sidebar.sidebar-collapsed .custom-scrollbar {
        scrollbar-width: none;
      }

      #sidebar.sidebar-collapsed .nav-sections-container {
        scrollbar-width: none;
      }
    `;
    document.head.appendChild(style);
  },

  // Create the main layout structure
  createLayout() {
    const body = document.body;
    body.innerHTML = `
      <!-- App Layout -->
      <div id="app-layout" class="h-screen bg-gray-50 flex overflow-hidden">
        <!-- Sidebar -->
        ${this.renderSidebar()}

        <!-- Main Content -->
        <div id="main-content" class="flex-1 flex flex-col transition-all duration-300 ease-in-out min-w-0">
          <!-- Top Navigation -->
          ${this.renderTopNavbar()}

          <!-- Content Area -->
          <main id="content-area" class="flex-1 p-6 overflow-y-auto overflow-x-hidden bg-gray-50">
            <!-- Content will be loaded here -->
          </main>

          <!-- Footer -->
          ${this.renderFooter()}
        </div>
      </div>

      <!-- Modal Container -->
      <div id="modal-container" class="fixed inset-0 z-50 hidden"></div>
    `;
  },

  // Render desktop sidebar
  renderSidebar() {
    return `
      <aside id="sidebar" class="static w-72 h-screen bg-white border-r border-gray-200 flex flex-col transition-all duration-300 ease-in-out shadow-lg overflow-hidden flex-shrink-0">
        <!-- Sidebar Header -->
        <div class="h-16 flex items-center justify-between px-6 border-b border-gray-200 bg-gradient-to-r from-primary-500 to-primary-600 flex-shrink-0">
          <div class="flex items-center space-x-3 min-w-0">
            <div class="sidebar-logo w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center flex-shrink-0 p-1">
              <img src="../assets/images/logo.png" alt="SR Logo" class="w-full h-full object-contain">
            </div>
            <div class="text-white sidebar-text transition-opacity duration-300">
              <div class="font-bold text-lg whitespace-nowrap">SmartReport</div>
            </div>
          </div>
          <button id="sidebar-toggle" class="p-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors flex-shrink-0">
            <i class="fas fa-chevron-left text-white"></i>
          </button>
        </div>

        <!-- Navigation Menu -->
        <nav class="flex-1 overflow-x-hidden py-4 custom-scrollbar">
          <div id="navigation-menu" class="px-3 space-y-2">
            <!-- Navigation items will be loaded here -->
          </div>
        </nav>
      </aside>
    `;
  },

  // Render desktop top navbar
  renderTopNavbar() {
    return `
      <header class="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6 shadow-sm flex-shrink-0">
        <!-- Left Section - Desktop Only -->
        <div class="flex items-center space-x-4">
        </div>

        <!-- Center Section - Academic Info -->
        <div class="flex items-center space-x-6 bg-gray-50 px-4 py-2 rounded-lg">
          <div class="flex items-center space-x-2">
            <i class="fas fa-graduation-cap text-indigo-600 text-base"></i>
            <div class="text-sm">
              <div class="font-medium text-gray-900" id="academic-period">Loading...</div>
            </div>
          </div>
        </div>
        
        <!-- Right Section - User Menu -->
        <div class="flex items-center space-x-4">
          <!-- User Menu -->
          <div class="relative">
            <button id="user-menu-button" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors">
              <div class="w-8 h-8 rounded-full overflow-hidden border-2 border-gray-200">
                <img id="user-profile-image" class="w-full h-full object-cover"
                     src=""
                     alt="User Profile"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="w-full h-full bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center" style="display: none;">
                  <i class="fas fa-user text-white text-sm"></i>
                </div>
              </div>
              <div class="text-left">
                <div class="text-sm font-medium text-gray-900" id="user-display-name">Admin</div>
                <div class="text-xs text-gray-500">System Administrator</div>
              </div>
              <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
            </button>
            
            <!-- User Dropdown -->
            <div id="user-dropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 hidden">
              <div class="py-2">
                <a href="#" onclick="showProfileModal()" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  <i class="fas fa-user-cog mr-3"></i>
                  Profile Settings
                </a>
                <hr class="my-2">
                <a href="#" onclick="logout()" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                  <i class="fas fa-sign-out-alt mr-3"></i>
                  Logout
                </a>
              </div>
            </div>
          </div>
        </div>
      </header>
    `;
  },

  // Render modern footer
  renderFooter() {
    return `
      <footer class="h-12 bg-white border-t border-gray-200 flex items-center justify-center px-6 flex-shrink-0">
        <div class="text-sm text-gray-500">
          © 2025 SmartReport Systems. All rights reserved.
        </div>
      </footer>
    `;
  },

  // Initialize event listeners
  initializeEventListeners() {
    console.log('🔧 Initializing Layout event listeners...');

    // Sidebar toggle
    const sidebarToggle = document.getElementById('sidebar-toggle');
    if (sidebarToggle) {
      sidebarToggle.addEventListener('click', () => {
        this.toggleSidebar();
      });
      console.log('✅ Sidebar toggle listener added');
    }


    // User menu toggle
    const userMenuButton = document.getElementById('user-menu-button');
    if (userMenuButton) {
      userMenuButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.toggleUserMenu();
        console.log('👤 User menu toggled');
      });
      console.log('✅ User menu toggle listener added');
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', (e) => {
      const userDropdown = document.getElementById('user-dropdown');
      if (userDropdown && !e.target.closest('#user-menu-button') && !userDropdown.contains(e.target)) {
        userDropdown.classList.add('hidden');
      }
    });


    console.log('✅ Layout event listeners initialized');
  },

  // Initialize navigation system
  initializeNavigation() {
    console.log('🧭 Initializing navigation system...');

    // Wait a bit for the DOM to be ready, then initialize navigation
    setTimeout(() => {
      if (window.ModernNavigation) {
        console.log('✅ Navigation found, initializing...');
        window.ModernNavigation.init();
      } else {
        console.warn('⚠️ Navigation not found, navigation may not work properly');
      }
    }, 100);
  },

  // Toggle sidebar (desktop only)
  toggleSidebar() {
    const sidebar = document.getElementById('sidebar');

    // Desktop sidebar collapse/expand
    const toggleIcon = document.querySelector('#sidebar-toggle i');
    const sidebarTexts = document.querySelectorAll('.sidebar-text');

    this.state.sidebarCollapsed = !this.state.sidebarCollapsed;

    if (this.state.sidebarCollapsed) {
      // Collapse sidebar
      sidebar.classList.remove('w-72');
      sidebar.classList.add('w-16');
      sidebar.classList.add('sidebar-collapsed');
      if (toggleIcon) {
        toggleIcon.classList.remove('fa-chevron-left');
        toggleIcon.classList.add('fa-chevron-right');
      }

      // Hide text elements
      sidebarTexts.forEach(text => {
        text.style.opacity = '0';
        text.style.visibility = 'hidden';
      });

      // Hide scrollbars when collapsed
      const navElement = sidebar.querySelector('nav');
      if (navElement) {
        navElement.style.overflow = 'hidden';
      }
    } else {
      // Expand sidebar
      sidebar.classList.remove('w-16');
      sidebar.classList.add('w-72');
      sidebar.classList.remove('sidebar-collapsed');
      if (toggleIcon) {
        toggleIcon.classList.remove('fa-chevron-right');
        toggleIcon.classList.add('fa-chevron-left');
      }

      // Show text elements
      sidebarTexts.forEach(text => {
        text.style.opacity = '1';
        text.style.visibility = 'visible';
      });

      // Restore scrollbars when expanded
      const navElement = sidebar.querySelector('nav');
      if (navElement) {
        navElement.style.overflow = 'auto';
      }
    }

    // Notify navigation component about state change
    if (window.ModernNavigation) {
      window.ModernNavigation.handleSidebarToggle(this.state.sidebarCollapsed);
    }
  },

  // Helper method to get proper user photo URL with fallback
  getUserPhotoUrl(profilePicture) {
    const serverUrl = window.SRConfig.getServerUrl();

    // Handle null, undefined, empty string, or string "null"
    if (profilePicture === null ||
        profilePicture === undefined ||
        profilePicture === '' ||
        profilePicture === 'null' ||
        profilePicture === 'undefined' ||
        (typeof profilePicture === 'string' && profilePicture.trim() === '')) {
      return `${serverUrl}/assets/images/default-avatar.png`;
    }

    // Convert to string and trim
    const photoPath = String(profilePicture).trim();

    // If photo path starts with 'C:' or contains backslashes, it's an invalid path
    if (photoPath.startsWith('C:') || photoPath.includes('\\') || photoPath.startsWith('file://')) {
      return `${serverUrl}/assets/images/default-avatar.png`;
    }

    // If photo path doesn't start with '/', add it
    if (!photoPath.startsWith('/')) {
      return `${serverUrl}/${photoPath}`;
    }

    return `${serverUrl}${photoPath}`;
  },

  // Update user profile image in top bar
  updateUserProfileImage() {
    const profileImage = document.getElementById('user-profile-image');
    if (profileImage && window.SR?.currentUser) {
      const photoUrl = this.getUserPhotoUrl(window.SR.currentUser.profile_picture);
      profileImage.src = photoUrl;

      // Reset fallback display
      profileImage.style.display = 'block';
      const fallbackDiv = profileImage.nextElementSibling;
      if (fallbackDiv) {
        fallbackDiv.style.display = 'none';
      }
    }
  },

  // Toggle user menu
  toggleUserMenu() {
    const dropdown = document.getElementById('user-dropdown');
    if (dropdown) {
      dropdown.classList.toggle('hidden');
      console.log('👤 User dropdown toggled:', !dropdown.classList.contains('hidden') ? 'shown' : 'hidden');
    } else {
      console.error('❌ User dropdown element not found');
    }
  },

  // Show profile settings modal
  showProfileModal() {
    const currentUser = SR.currentUser;
    if (!currentUser) {
      console.error('No current user data available');
      return;
    }

    const modalHtml = `
      <div id="profile-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 rounded-full overflow-hidden border-2 border-gray-200">
                  <img class="w-full h-full object-cover"
                       src="${this.getUserPhotoUrl(currentUser.profile_picture)}"
                       alt="Profile Picture"
                       onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                  <div class="w-full h-full bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center" style="display: none;">
                    <i class="fas fa-user-cog text-white text-lg"></i>
                  </div>
                </div>
                <div>
                  <h3 class="text-xl font-semibold text-gray-900">Profile Settings</h3>
                  <p class="text-sm text-gray-500">Manage your account information and preferences</p>
                </div>
              </div>
              <button onclick="Layout.closeProfileModal()"
                      class="text-gray-400 hover:text-gray-600 transition-colors">
                <i class="fas fa-times text-xl"></i>
              </button>
            </div>

            <!-- Profile Form -->
            <form id="profile-form" class="space-y-6">
              <!-- Personal Information Section -->
              <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
                <div class="flex items-center mb-4">
                  <div class="bg-blue-100 rounded-full p-2 mr-3">
                    <i class="fas fa-user text-blue-600"></i>
                  </div>
                  <h4 class="text-lg font-semibold text-gray-900">Personal Information</h4>
                </div>

                <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                  <div>
                    <label for="profile-first-name" class="block text-sm font-medium text-gray-700 mb-2">
                      First Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="profile-first-name" name="first_name"
                           value="${currentUser.first_name || ''}" required
                           class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
                  </div>

                  <div>
                    <label for="profile-last-name" class="block text-sm font-medium text-gray-700 mb-2">
                      Last Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="profile-last-name" name="last_name"
                           value="${currentUser.last_name || ''}" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  </div>

                  <div>
                    <label for="profile-middle-name" class="block text-sm font-medium text-gray-700 mb-2">
                      Middle Name
                    </label>
                    <input type="text" id="profile-middle-name" name="middle_name"
                           value="${currentUser.middle_name || ''}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  </div>

                  <div>
                    <label for="profile-phone" class="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number
                    </label>
                    <input type="tel" id="profile-phone" name="phone_number"
                           value="${currentUser.phone_number || ''}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  </div>

                  <div>
                    <label for="profile-gender" class="block text-sm font-medium text-gray-700 mb-2">
                      Gender
                    </label>
                    <select id="profile-gender" name="gender"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <option value="">Select Gender</option>
                      <option value="Male" ${currentUser.gender === 'Male' ? 'selected' : ''}>Male</option>
                      <option value="Female" ${currentUser.gender === 'Female' ? 'selected' : ''}>Female</option>
                    </select>
                  </div>

                  <div>
                    <label for="profile-dob" class="block text-sm font-medium text-gray-700 mb-2">
                      Date of Birth
                    </label>
                    <input type="date" id="profile-dob" name="date_of_birth"
                           value="${currentUser.date_of_birth || ''}"
                           class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
                  </div>
                </div>
              </div>

              <!-- Account Information Section -->
              <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-6 border border-green-200">
                <div class="flex items-center mb-4">
                  <div class="bg-green-100 rounded-full p-2 mr-3">
                    <i class="fas fa-key text-green-600"></i>
                  </div>
                  <h4 class="text-lg font-semibold text-gray-900">Account Information</h4>
                </div>

                <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                  <div>
                    <label for="profile-username" class="block text-sm font-medium text-gray-700 mb-2">
                      Username <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="profile-username" name="username"
                           value="${currentUser.username || ''}" required
                           class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
                  </div>

                  <div>
                    <label for="profile-email" class="block text-sm font-medium text-gray-700 mb-2">
                      Email Address <span class="text-red-500">*</span>
                    </label>
                    <input type="email" id="profile-email" name="email"
                           value="${currentUser.email || ''}" required
                           class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
                  </div>

                  <div class="col-span-2">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div class="flex items-center">
                        <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                        <div>
                          <p class="text-sm font-medium text-blue-900">Account Role</p>
                          <p class="text-sm text-blue-700">System Administrator</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Password Change Section -->
              <div class="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg p-6 border border-yellow-200">
                <div class="flex items-center mb-4">
                  <div class="bg-yellow-100 rounded-full p-2 mr-3">
                    <i class="fas fa-lock text-yellow-600"></i>
                  </div>
                  <h4 class="text-lg font-semibold text-gray-900">Change Password</h4>
                </div>

                <div class="space-y-4">
                  <div>
                    <label for="profile-current-password" class="block text-sm font-medium text-gray-700 mb-2">
                      Current Password
                    </label>
                    <input type="password" id="profile-current-password" name="current_password"
                           class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                           placeholder="Enter current password to change">
                  </div>

                  <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
                    <div>
                      <label for="profile-new-password" class="block text-sm font-medium text-gray-700 mb-2">
                        New Password <span class="text-red-500">*</span>
                      </label>
                      <input type="password" id="profile-new-password" name="new_password"
                             class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                             placeholder="Enter new password"
                             minlength="8">
                    </div>

                    <div>
                      <label for="profile-confirm-password" class="block text-sm font-medium text-gray-700 mb-2">
                        Confirm New Password <span class="text-red-500">*</span>
                      </label>
                      <input type="password" id="profile-confirm-password" name="confirm_password"
                             class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                             placeholder="Confirm new password">
                    </div>
                  </div>

                  <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <p class="text-sm text-yellow-800">
                      <i class="fas fa-exclamation-triangle mr-2"></i>
                      Leave password fields empty if you don't want to change your password.
                    </p>
                  </div>
                </div>
              </div>

              <!-- Form Actions -->
              <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="Layout.closeProfileModal()"
                        class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                  Cancel
                </button>
                <button type="submit" id="save-profile-btn"
                        class="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                  <i class="fas fa-save mr-2"></i>
                  Save Changes
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    `;

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Add form event listener
    const form = document.getElementById('profile-form');
    if (form) {
      form.addEventListener('submit', this.handleProfileSubmit.bind(this));
    }

    // Close modal when clicking outside
    const modal = document.getElementById('profile-modal');
    if (modal) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          this.closeProfileModal();
        }
      });
    }

    // Setup password validation with real-time feedback
    SRDesignSystem.password.setupValidation('profile-new-password', 'profile-confirm-password');
  },

  // Close profile modal
  closeProfileModal() {
    const modal = document.getElementById('profile-modal');
    if (modal) {
      modal.remove();
    }
  },

  // Handle profile form submission
  async handleProfileSubmit(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());

    // Validate required fields
    if (!data.first_name || !data.last_name || !data.username || !data.email) {
      this.showNotification('Please fill in all required fields', 'error');
      return;
    }

    // Handle password change
    const isChangingPassword = data.current_password && data.new_password;
    if (isChangingPassword) {
      if (data.new_password !== data.confirm_password) {
        this.showNotification('New passwords do not match', 'error');
        return;
      }
      // Validate password strength using design system
      const passwordValidation = SRDesignSystem.password.validate(data.new_password);
      if (!passwordValidation.isValid) {
        this.showNotification('New password does not meet security requirements. Please check the password strength indicator.', 'error');
        return;
      }
    }

    try {
      // Set loading state
      const saveBtn = document.getElementById('save-profile-btn');
      if (saveBtn) {
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
        saveBtn.disabled = true;
      }

      // Prepare update data (remove password fields if not changing password)
      const updateData = {
        first_name: data.first_name,
        last_name: data.last_name,
        middle_name: data.middle_name,
        username: data.username,
        email: data.email,
        phone_number: data.phone_number,
        gender: data.gender,
        date_of_birth: data.date_of_birth
      };

      // Update profile
      const result = await window.SystemUsersAPI.update(SR.currentUser.id, updateData);

      if (result.success) {
        // Handle password change separately if needed
        if (isChangingPassword) {
          const passwordResult = await window.SystemUsersAPI.updatePassword(SR.currentUser.id, {
            current_password: data.current_password,
            new_password: data.new_password
          });

          if (!passwordResult.success) {
            this.showNotification(passwordResult.message || 'Failed to update password', 'error');
            return;
          }
        }

        // Update current user data
        SR.currentUser = { ...SR.currentUser, ...updateData };

        // Update UI
        updateUserInterface();

        // Update user profile image in top bar
        this.updateUserProfileImage();

        this.showNotification('Profile updated successfully!', 'success');
        this.closeProfileModal();
      } else {
        this.showNotification(result.message || 'Failed to update profile', 'error');
      }
    } catch (error) {
      console.error('Profile update error:', error);
      this.showNotification('Failed to update profile', 'error');
    } finally {
      // Reset button state
      const saveBtn = document.getElementById('save-profile-btn');
      if (saveBtn) {
        saveBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Save';
        saveBtn.disabled = false;
      }
    }
  },

  // Show notification
  showNotification(message, type = 'info') {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, type);
    }
  },

  // Show logout confirmation modal
  showLogoutConfirmation() {
    console.log('🚪 Layout.showLogoutConfirmation() called');

    const modalHtml = `
      <div id="logout-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-1/2 transform -translate-y-1/2 mx-auto p-5 border w-full max-w-md shadow-lg rounded-lg bg-white">
          <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-center mb-6">
              <div class="w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center">
                <i class="fas fa-sign-out-alt text-white text-2xl"></i>
              </div>
            </div>

            <!-- Modal Content -->
            <div class="text-center">
              <h3 class="text-xl font-semibold text-gray-900 mb-3">Confirm Logout</h3>
              <p class="text-gray-600 mb-6">Are you sure you want to logout?</p>

              <!-- Action Buttons -->
              <div class="flex items-center justify-center space-x-4">
                <button onclick="Layout.closeLogoutModal()"
                        class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500">
                  <i class="fas fa-times mr-2"></i>
                  Cancel
                </button>
                <button onclick="Layout.confirmLogout()" id="confirm-logout-btn"
                        class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500">
                  <i class="fas fa-sign-out-alt mr-2"></i>
                  Yes, Logout
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    // Add modal to body
    try {
      document.body.insertAdjacentHTML('beforeend', modalHtml);
      console.log('✅ Logout confirmation modal displayed');
    } catch (error) {
      console.error('❌ Error adding logout modal to DOM:', error);
      return;
    }

    // Close modal when clicking outside
    const modal = document.getElementById('logout-modal');
    if (modal) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          this.closeLogoutModal();
        }
      });

      // Focus on the cancel button for better UX
      setTimeout(() => {
        const cancelBtn = modal.querySelector('button');
        if (cancelBtn) {
          cancelBtn.focus();
        }
      }, 100);
    } else {
      console.error('❌ Logout modal element not found after creation');
    }

    // Handle ESC key to close modal
    const handleEscKey = (e) => {
      if (e.key === 'Escape') {
        this.closeLogoutModal();
        document.removeEventListener('keydown', handleEscKey);
      }
    };
    document.addEventListener('keydown', handleEscKey);
  },

  // Close logout confirmation modal
  closeLogoutModal() {
    const modal = document.getElementById('logout-modal');
    if (modal) {
      modal.remove();
    }
  },

  // Confirm logout and proceed
  async confirmLogout() {
    try {
      // Set loading state
      const confirmBtn = document.getElementById('confirm-logout-btn');
      if (confirmBtn) {
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Logging out...';
        confirmBtn.disabled = true;
      }

      // Close the modal first
      this.closeLogoutModal();

      // Use the centralized AuthManager logout method
      await window.AuthManager.logout();

    } catch (error) {
      console.error('Logout confirmation error:', error);
      this.showNotification('Error during logout', 'error');

      // Reset button state
      const confirmBtn = document.getElementById('confirm-logout-btn');
      if (confirmBtn) {
        confirmBtn.innerHTML = '<i class="fas fa-sign-out-alt mr-2"></i>Yes, Logout';
        confirmBtn.disabled = false;
      }
    }
  },

  // Update academic information using global academic context
  async updateAcademicInfo() {
    const academicPeriod = document.getElementById('academic-period');
    if (!academicPeriod) return;

    try {
      console.log('📅 Loading academic info from global context...');

      // Use the global academic context manager
      await window.AcademicContext.initialize();

      const activeYear = window.AcademicContext.getActiveAcademicYear();
      const activeTerm = window.AcademicContext.getActiveTerm();

      if (activeYear && activeTerm) {
        this.displayAcademicInfo({
          text: `${activeYear.name} - ${activeTerm.name}`,
          className: 'text-gray-900'
        });

        // Update layout state
        this.state.currentAcademicYear = activeYear;
        this.state.currentTerm = activeTerm;
      } else if (activeYear && !activeTerm) {
        this.displayAcademicInfo({
          text: `${activeYear.name} - No Active Term`,
          className: 'text-yellow-600'
        });
      } else {
        // Check if academic context is loaded but no active period found
        if (window.AcademicContext.isContextLoaded()) {
          this.displayAcademicInfo({
            text: 'Setup Academic Year & Terms',
            className: 'text-red-600 cursor-pointer',
            onclick: 'window.PageRouter && window.PageRouter.loadPage("academic-year-setup")'
          });
        } else {
          this.displayAcademicInfo({
            text: 'Loading Academic Context...',
            className: 'text-gray-500'
          });
        }
      }

      // Listen for academic context changes
      window.AcademicContext.addListener((context) => {
        if (context.activeAcademicYear && context.activeTerm) {
          this.displayAcademicInfo({
            text: `${context.activeAcademicYear.name} - ${context.activeTerm.name}`,
            className: 'text-gray-900'
          });
        }
      });

    } catch (error) {
      console.error('❌ Error loading academic info:', error);
      this.displayAcademicInfo({
        text: 'Academic Setup Required',
        className: 'text-yellow-600'
      });
    }
  },

  // Display academic info in the navbar
  displayAcademicInfo(data) {
    const academicPeriod = document.getElementById('academic-period');
    if (!academicPeriod) return;

    academicPeriod.textContent = data.text;
    academicPeriod.className = `font-medium ${data.className}`;

    // Handle onclick functionality
    if (data.onclick) {
      academicPeriod.style.cursor = 'pointer';
      academicPeriod.onclick = () => {
        try {
          eval(data.onclick);
        } catch (error) {
          console.error('Error executing onclick:', error);
        }
      };
    } else {
      academicPeriod.style.cursor = 'default';
      academicPeriod.onclick = null;
    }
  },



  // Clock functionality removed since date display was removed from navbar

  // Load page content (deprecated - use PageRouter.loadPage instead)
  loadPage(page) {
    console.warn('Layout.loadPage is deprecated. Use PageRouter.loadPage instead.');
    if (window.PageRouter && window.PageRouter.state.currentPage !== page) {
      window.PageRouter.loadPage(page);
    }
  }
};

// Export to global scope
window.Layout = Layout;
