const fs = require('fs').promises;
const path = require('path');
const csv = require('csv-parser');
const { createObjectCsvWriter } = require('csv-writer');
const { executeQuery } = require('../../database/connection');

class DataImportExport {
  constructor() {
    this.tempDir = path.join(__dirname, '../../temp');
    this.ensureTempDirectory();
  }

  async ensureTempDirectory() {
    try {
      await fs.access(this.tempDir);
    } catch (error) {
      await fs.mkdir(this.tempDir, { recursive: true });
    }
  }

  // Export students data
  async exportStudents(options = {}) {
    try {
      const { format = 'csv', class_id, status = 'active' } = options;
      
      let query = `
        SELECT s.admission_number, s.fees_payment_code, s.learners_identification_number,
               s.first_name, s.middle_name, s.last_name, s.gender, s.date_of_birth,
               s.religion, s.nationality, s.section, s.admission_date, s.status,
               s.student_house, s.medical_history, s.allergies, s.special_needs,
               s.address, s.previous_school, s.previous_class,
               c.name as current_class_name, c.level as current_level
        FROM students s
        LEFT JOIN classes c ON s.current_class_id = c.id
        WHERE s.status = ?
      `;
      
      const params = [status];
      
      if (class_id) {
        query += ' AND s.current_class_id = ?';
        params.push(class_id);
      }
      
      query += ' ORDER BY s.last_name, s.first_name';
      
      const result = await executeQuery(query, params);
      
      if (!result.success) {
        throw new Error(result.error);
      }
      
      const fileName = `students_export_${Date.now()}.${format}`;
      const filePath = path.join(this.tempDir, fileName);
      
      if (format === 'csv') {
        await this.exportToCSV(result.data, filePath, this.getStudentHeaders());
      } else if (format === 'json') {
        await this.exportToJSON(result.data, filePath);
      }
      
      return {
        success: true,
        file_path: filePath,
        file_name: fileName,
        record_count: result.data.length
      };
      
    } catch (error) {
      console.error('Export students error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Import students data
  async importStudents(filePath, options = {}) {
    try {
      const { format = 'csv', validate = true, dryRun = false } = options;
      
      let studentsData;
      
      if (format === 'csv') {
        studentsData = await this.parseCSV(filePath);
      } else if (format === 'json') {
        studentsData = await this.parseJSON(filePath);
      } else {
        throw new Error('Unsupported format');
      }
      
      const results = {
        total_records: studentsData.length,
        successful_imports: 0,
        failed_imports: 0,
        errors: [],
        imported_students: []
      };
      
      for (let i = 0; i < studentsData.length; i++) {
        const studentData = studentsData[i];
        
        try {
          if (validate) {
            const validation = this.validateStudentData(studentData);
            if (!validation.isValid) {
              throw new Error(`Row ${i + 1}: ${validation.errors.join(', ')}`);
            }
          }
          
          if (!dryRun) {
            const importResult = await this.importSingleStudent(studentData);
            if (importResult.success) {
              results.successful_imports++;
              results.imported_students.push({
                row: i + 1,
                admission_number: studentData.admission_number,
                name: `${studentData.first_name} ${studentData.last_name}`
              });
            } else {
              throw new Error(importResult.error);
            }
          } else {
            results.successful_imports++;
          }
          
        } catch (error) {
          results.failed_imports++;
          results.errors.push({
            row: i + 1,
            error: error.message,
            data: studentData
          });
        }
      }
      
      return {
        success: true,
        results: results
      };
      
    } catch (error) {
      console.error('Import students error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Export teachers data
  async exportTeachers(options = {}) {
    try {
      const { format = 'csv', role } = options;
      
      let query = `
        SELECT first_name, middle_name, last_name, initials,
               teacher_type, joining_date, employment_status, academic_year_id,
               created_at, updated_at
        FROM teachers
        WHERE 1=1
      `;
      
      const params = [];

      if (options.teacher_type) {
        query += ' AND teacher_type = ?';
        params.push(options.teacher_type);
      }

      if (options.employment_status) {
        query += ' AND employment_status = ?';
        params.push(options.employment_status);
      }

      query += ' ORDER BY last_name, first_name';
      
      const result = await executeQuery(query, params);
      
      if (!result.success) {
        throw new Error(result.error);
      }
      
      const fileName = `teachers_export_${Date.now()}.${format}`;
      const filePath = path.join(this.tempDir, fileName);
      
      if (format === 'csv') {
        await this.exportToCSV(result.data, filePath, this.getTeacherHeaders());
      } else if (format === 'json') {
        await this.exportToJSON(result.data, filePath);
      }
      
      return {
        success: true,
        file_path: filePath,
        file_name: fileName,
        record_count: result.data.length
      };
      
    } catch (error) {
      console.error('Export teachers error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Export grades data
  async exportGrades(options = {}) {
    try {
      const { format = 'csv', class_id, subject_id, term_id } = options;
      
      let query = `
        SELECT s.admission_number, s.first_name, s.last_name,
               c.name as class_name, sub.name as subject_name,
               cg.ca_average_score, cg.term_exam_score, cg.final_score,
               cg.grade_letter, cg.grade_descriptor,
               t.name as term_name, ay.name as academic_year_name
        FROM competency_grades cg
        JOIN students s ON cg.student_id = s.id
        JOIN subjects sub ON cg.subject_id = sub.id
        JOIN classes c ON s.current_class_id = c.id
        JOIN terms t ON cg.term_id = t.id
        JOIN academic_years ay ON t.academic_year_id = ay.id
        WHERE 1=1
      `;
      
      const params = [];
      
      if (class_id) {
        query += ' AND s.current_class_id = ?';
        params.push(class_id);
      }
      
      if (subject_id) {
        query += ' AND cg.subject_id = ?';
        params.push(subject_id);
      }
      
      if (term_id) {
        query += ' AND cg.term_id = ?';
        params.push(term_id);
      }
      
      query += ' ORDER BY c.name, s.last_name, s.first_name, sub.name';
      
      const result = await executeQuery(query, params);
      
      if (!result.success) {
        throw new Error(result.error);
      }
      
      const fileName = `grades_export_${Date.now()}.${format}`;
      const filePath = path.join(this.tempDir, fileName);
      
      if (format === 'csv') {
        await this.exportToCSV(result.data, filePath, this.getGradesHeaders());
      } else if (format === 'json') {
        await this.exportToJSON(result.data, filePath);
      }
      
      return {
        success: true,
        file_path: filePath,
        file_name: fileName,
        record_count: result.data.length
      };
      
    } catch (error) {
      console.error('Export grades error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Helper methods
  async exportToCSV(data, filePath, headers) {
    const csvWriter = createObjectCsvWriter({
      path: filePath,
      header: headers
    });
    
    await csvWriter.writeRecords(data);
  }

  async exportToJSON(data, filePath) {
    await fs.writeFile(filePath, JSON.stringify(data, null, 2));
  }

  async parseCSV(filePath) {
    return new Promise((resolve, reject) => {
      const results = [];
      const stream = require('fs').createReadStream(filePath);
      
      stream
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', () => resolve(results))
        .on('error', (error) => reject(error));
    });
  }

  async parseJSON(filePath) {
    const fileContent = await fs.readFile(filePath, 'utf8');
    return JSON.parse(fileContent);
  }

  validateStudentData(data) {
    const errors = [];
    
    if (!data.admission_number) {
      errors.push('Admission number is required');
    }
    
    if (!data.first_name) {
      errors.push('First name is required');
    }
    
    if (!data.last_name) {
      errors.push('Last name is required');
    }
    
    if (!data.gender || !['Male', 'Female'].includes(data.gender)) {
      errors.push('Valid gender is required (Male/Female)');
    }
    
    if (!data.date_of_birth) {
      errors.push('Date of birth is required');
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  async importSingleStudent(studentData) {
    try {
      // Check if student already exists
      const existingQuery = 'SELECT id FROM students WHERE admission_number = ?';
      const existingResult = await executeQuery(existingQuery, [studentData.admission_number]);
      
      if (existingResult.success && existingResult.data.length > 0) {
        throw new Error('Student with this admission number already exists');
      }
      
      // Insert student
      const insertQuery = `
        INSERT INTO students (
          admission_number, fees_payment_code, learners_identification_number,
          first_name, middle_name, last_name, gender, date_of_birth,
          religion, nationality, section, admission_date, student_house,
          medical_history, allergies, special_needs, address,
          previous_school, previous_class, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
      `;
      
      const result = await executeQuery(insertQuery, [
        studentData.admission_number,
        studentData.fees_payment_code || `FEE${Date.now()}`,
        studentData.learners_identification_number,
        studentData.first_name,
        studentData.middle_name,
        studentData.last_name,
        studentData.gender,
        studentData.date_of_birth,
        studentData.religion,
        studentData.nationality || 'Ugandan',
        studentData.section || 'Day',
        studentData.admission_date || new Date().toISOString().split('T')[0],
        studentData.student_house,
        studentData.medical_history,
        studentData.allergies,
        studentData.special_needs,
        studentData.address,
        studentData.previous_school,
        studentData.previous_class
      ]);
      
      return {
        success: result.success,
        student_id: result.data?.insertId,
        error: result.error
      };
      
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  getStudentHeaders() {
    return [
      { id: 'admission_number', title: 'Admission Number' },
      { id: 'fees_payment_code', title: 'Fees Payment Code' },
      { id: 'learners_identification_number', title: 'LIN' },
      { id: 'first_name', title: 'First Name' },
      { id: 'middle_name', title: 'Middle Name' },
      { id: 'last_name', title: 'Last Name' },
      { id: 'gender', title: 'Gender' },
      { id: 'date_of_birth', title: 'Date of Birth' },
      { id: 'religion', title: 'Religion' },
      { id: 'nationality', title: 'Nationality' },
      { id: 'current_class_name', title: 'Current Class' },
      { id: 'section', title: 'Section' },
      { id: 'admission_date', title: 'Admission Date' },
      { id: 'status', title: 'Status' }
    ];
  }

  getTeacherHeaders() {
    return [
      { id: 'first_name', title: 'First Name' },
      { id: 'middle_name', title: 'Middle Name' },
      { id: 'last_name', title: 'Last Name' },
      { id: 'initials', title: 'Initials' },
      { id: 'teacher_type', title: 'Teacher Type' },
      { id: 'employment_status', title: 'Employment Status' },
      { id: 'joining_date', title: 'Joining Date' },
      { id: 'teaching_subjects', title: 'Teaching Subjects' }
    ];
  }

  getGradesHeaders() {
    return [
      { id: 'admission_number', title: 'Admission Number' },
      { id: 'first_name', title: 'First Name' },
      { id: 'last_name', title: 'Last Name' },
      { id: 'class_name', title: 'Class' },
      { id: 'subject_name', title: 'Subject' },
      { id: 'ca_average_score', title: 'CA Score' },
      { id: 'term_exam_score', title: 'Exam Score' },
      { id: 'final_score', title: 'Final Score' },
      { id: 'grade_letter', title: 'Grade' },
      { id: 'grade_descriptor', title: 'Descriptor' },
      { id: 'term_name', title: 'Term' },
      { id: 'academic_year_name', title: 'Academic Year' }
    ];
  }
}

module.exports = DataImportExport;
