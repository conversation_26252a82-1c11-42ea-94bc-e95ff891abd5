// SmartReport Modern Navigation System
// Clean, intuitive navigation with smooth animations

const ModernNavigation = {
  // Navigation state
  state: {
    activeSection: null,
    activePage: 'dashboard',
    expandedSections: new Set(),
    sidebarCollapsed: false,
    useShortTitles: true // Toggle for using short titles
  },

  // Initialize navigation
  init() {
    // Ensure dashboard is set as active page
    this.state.activePage = 'dashboard';
    this.renderNavigation();
    this.initializeEventListeners();
  },

  // Refresh navigation (useful when user role changes)
  refresh() {
    this.renderNavigation();
  },

  // Get navigation menu structure
  getMenuStructure() {
    const currentUser = SR.currentUser;
    const isSuperUser = currentUser && currentUser.role === 'super_user';

    const menuItems = [
      {
        type: 'item',
        id: 'dashboard',
        title: 'Dashboard',
        icon: 'fas fa-tachometer-alt',
        page: 'dashboard',
        badge: null
      },
      {
        type: 'section',
        id: 'academic-year-management',
        title: 'Academic Year Management',
        shortTitle: 'Academic',
        icon: 'fas fa-calendar-alt',
        items: [
          { title: 'Setup', page: 'academic-year-setup', icon: 'fas fa-calendar-plus', description: 'Initial academic year and terms setup' },
          { title: 'Manage', page: 'academic-years-management', icon: 'fas fa-calendar-check', description: 'Manage current academic year and terms' }
        ]
      },
      {
        type: 'section',
        id: 'class-management',
        title: 'Class Management',
        shortTitle: 'Classes',
        icon: 'fas fa-chalkboard',
        items: [
          { title: 'Streams', page: 'manage-streams', icon: 'fas fa-layer-group', description: 'Manage class streams' }
        ]
      },
      {
        type: 'section',
        id: 'student-management',
        title: 'Student Management',
        shortTitle: 'Students',
        icon: 'fas fa-user-graduate',
        items: [
          { title: 'Register', page: 'register-student', icon: 'fas fa-user-plus', description: 'Add new students - Choose O-Level or A-Level' },
          { title: 'Manage', page: 'manage-students', icon: 'fas fa-users-cog', description: 'Student profiles and enrollment' }
        ]
      },
      {
        type: 'section',
        id: 'teacher-management',
        title: 'Teacher Management',
        shortTitle: 'Teachers',
        icon: 'fas fa-chalkboard-teacher',
        items: [
          { title: 'Register', page: 'register-teacher', icon: 'fas fa-user-plus' },
          { title: 'Manage', page: 'manage-teachers', icon: 'fas fa-users-cog' }
        ]
      }
    ];

    // Only super users can access User Management
    if (isSuperUser) {
      menuItems.push({
        type: 'section',
        id: 'user-management',
        title: 'User Management',
        shortTitle: 'Users',
        icon: 'fas fa-user-shield',
        items: [
          { title: 'Register', page: 'register-admin', icon: 'fas fa-user-plus', description: 'Add new system administrators' },
          { title: 'Manage', page: 'manage-admins', icon: 'fas fa-user-cog', description: 'Manage system users' }
        ]
      });
    }

    menuItems.push(
      {
        type: 'section',
        id: 'assessment-settings',
        title: 'Assessment Settings',
        shortTitle: 'Assessments',
        icon: 'fas fa-tasks', 
        items: [
          { title: 'Continuous', page: 'ca-configuration', icon: 'fas fa-sync-alt', description: 'Configure number of CAs per subject per term' },
          { title: 'Exam Types', page: 'exam-types-management', icon: 'fas fa-stream', description: 'Configure exam types and weights for classes' }
        ]
      },
      {
        type: 'section',
        id: 'grading-scales',
        title: 'Grading Scales',
        shortTitle: 'Grading',
        icon: 'fas fa-sliders-h', 
        items: [
          { title: 'O-Level', page: 'o-level-grade-boundaries', icon: 'fas fa-ruler-horizontal' },
          { title: 'A-Level', page: 'a-level-grade-boundaries', icon: 'fas fa-ruler-combined' }
        ]
      },
      {
        type: 'section',
        id: 'marks-entry',
        title: 'Marks Entry',
        shortTitle: 'Marks',
        icon: 'fas fa-keyboard', 
        items: [
          { title: 'CA Scores', page: 'enter-ca-scores', icon: 'fas fa-pen-nib', description: 'Input continuous assessment scores' },
          { title: 'Exam Marks', page: 'enter-exam-marks', icon: 'fas fa-pen-square', description: 'Input examination marks' }
        ]
      },
      {
        type: 'section',
        id: 'report-cards',
        title: 'Report Cards',
        shortTitle: 'Reports',
        icon: 'fas fa-file-alt',
        items: [
          { title: 'O-Level', page: 'generate-o-level-report-cards', icon: 'fas fa-file-pdf', description: 'Create O-Level student report cards' },
          { title: 'A-Level', page: 'generate-a-level-report-cards', icon: 'fas fa-file-pdf', description: 'Create A-Level student report cards' }
        ]
      }
    );

    // Only super users can access System Configurations
    if (isSuperUser) {
      menuItems.push({
        type: 'section',
        id: 'system-configuration',
        title: 'System Configurations',
        shortTitle: 'System',
        icon: 'fas fa-tools',
        items: [
          { title: 'School Settings', page: 'school-settings', icon: 'fas fa-school', description: 'Configure school information' },
          { title: 'Import/ Export', page: 'data-import-export', icon: 'fas fa-file-import', description: 'Data import/export tools' }
        ]
      });
    }

    return menuItems;
  },

  // Render navigation menu
  renderNavigation() {
    const container = document.getElementById('navigation-menu');
    if (!container) return;

    const menuStructure = this.getMenuStructure();
    let dashboardHtml = '';
    let sectionsHtml = '';

    menuStructure.forEach(item => {
      if (item.type === 'item') {
        dashboardHtml += this.renderMenuItem(item);
      } else if (item.type === 'section') {
        sectionsHtml += this.renderMenuSection(item);
      }
    });

    container.innerHTML = `
      ${dashboardHtml}
      <div class="nav-sections-container">
        ${sectionsHtml}
      </div>
    `;
  },

  // Render single menu item
  renderMenuItem(item) {
    const isActive = this.state.activePage === item.page;
    const isCollapsed = this.state.sidebarCollapsed;
    const isDashboard = item.page === 'dashboard';

    // Special styling for dashboard
    const dashboardClasses = isDashboard ? {
      container: 'dashboard-nav-item mb-3',
      button: isActive
        ? 'bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-lg border-none'
        : 'bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700 shadow-md',
      icon: 'text-white',
      text: 'text-white font-semibold'
    } : {
      container: '',
      button: isActive
        ? 'bg-primary-50 text-primary-700 border-r-4 border-primary-600 shadow-sm'
        : 'text-gray-700 hover:bg-gray-100',
      icon: isActive ? 'text-primary-600' : 'text-gray-500',
      text: 'font-medium'
    };

    return `
      <div class="nav-item ${isActive ? 'active' : ''} ${dashboardClasses.container}" data-page="${item.page}">
        <button class="w-full flex items-center ${isCollapsed ? 'justify-center px-2' : 'space-x-3 px-4'} py-3 rounded-lg text-left transition-all duration-200 hover:shadow-sm ${dashboardClasses.button}">
          <i class="${item.icon} text-base flex-shrink-0 ${dashboardClasses.icon}"></i>
          <span class="nav-text transition-all duration-300 ${isCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'} text-sm leading-tight ${dashboardClasses.text}">${item.title}</span>
        </button>
      </div>
    `;
  },

  // Render menu section with collapsible items
  renderMenuSection(section) {
    const isExpanded = this.state.expandedSections.has(section.id);
    const hasActiveItem = section.items.some(item => item.page === this.state.activePage);
    const isCollapsed = this.state.sidebarCollapsed;

    // Use shortTitle for better layout - show shortTitle when available and enabled
    const displayTitle = (this.state.useShortTitles && section.shortTitle) ? section.shortTitle : section.title;

    let html = `
      <div class="nav-section mb-1" data-section="${section.id}">
        <button class="w-full flex items-center ${isCollapsed ? 'justify-center px-2' : 'justify-between px-4'} py-3 rounded-lg text-left transition-all duration-200 hover:bg-gray-50 hover:shadow-sm text-gray-700 font-medium ${hasActiveItem ? 'bg-primary-50 text-primary-700' : ''}">
          <div class="flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'}">
            <i class="${section.icon} text-base ${hasActiveItem ? 'text-primary-600' : 'text-gray-500'} flex-shrink-0"></i>
            <span class="nav-text transition-all duration-300 ${isCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'} text-sm leading-tight truncate" ${section.shortTitle ? `title="${section.title}"` : ''}>${displayTitle}</span>
          </div>
          <i class="fas fa-chevron-down text-xs transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''} ${hasActiveItem ? 'text-primary-600' : 'text-gray-400'} nav-text ${isCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'} flex-shrink-0"></i>
        </button>
        <div class="nav-section-items ${isExpanded && !isCollapsed ? 'expanded' : 'collapsed'} ${isCollapsed ? '' : 'ml-2'} mt-1 space-y-1 overflow-hidden transition-all duration-300 ${isExpanded && !isCollapsed ? 'max-h-96' : 'max-h-0'}">
    `;

    if (!isCollapsed) {
      section.items.forEach(item => {
        const isActive = this.state.activePage === item.page;
        html += `
          <div class="nav-item ${isActive ? 'active' : ''}" data-page="${item.page}">
            <button class="w-full flex items-center space-x-3 px-6 py-2.5 rounded-lg text-left transition-all duration-200 hover:bg-gray-50 hover:shadow-sm ${isActive ? 'bg-primary-100 text-primary-700 border-l-4 border-primary-600 font-medium' : 'text-gray-600 hover:text-gray-800'} text-sm">
              <i class="${item.icon} text-xs ${isActive ? 'text-primary-600' : 'text-gray-400'} flex-shrink-0"></i>
              <span class="nav-text leading-tight">${item.title}</span>
            </button>
          </div>
        `;
      });
    }

    html += `
        </div>
      </div>
    `;

    // Auto-expand section if it has active item and sidebar is not collapsed
    if (hasActiveItem && !isExpanded && !isCollapsed) {
      this.state.expandedSections.add(section.id);
    }

    return html;
  },

  // Initialize event listeners
  initializeEventListeners() {
    const container = document.getElementById('navigation-menu');
    if (!container) return;

    // Handle menu item clicks
    container.addEventListener('click', (e) => {
      const navItem = e.target.closest('.nav-item');
      const navSection = e.target.closest('.nav-section');

      if (navItem) {
        const page = navItem.dataset.page;
        if (page) {
          this.navigateToPage(page);
        }
      } else if (navSection && !navItem) {
        const sectionId = navSection.dataset.section;
        if (sectionId) {
          this.toggleSection(sectionId);
        }
      }
    });
  },

  // Navigate to page
  navigateToPage(page) {
    this.setActivePage(page);

    // Load page content using PageRouter
    if (window.PageRouter) {
      window.PageRouter.loadPage(page);
    } else if (window.Layout) {
      window.Layout.loadPage(page);
    }
  },

  // Set active page
  setActivePage(page) {
    // Update state first
    this.state.activePage = page;

    // Re-render navigation to update active states
    this.renderNavigation();
  },

  // Toggle section expansion with auto-close functionality
  toggleSection(sectionId) {
    // Don't allow section expansion when sidebar is collapsed
    if (this.state.sidebarCollapsed) return;

    const section = document.querySelector(`[data-section="${sectionId}"]`);
    if (!section) return;

    const itemsContainer = section.querySelector('.nav-section-items');
    const chevron = section.querySelector('.fa-chevron-down');

    if (this.state.expandedSections.has(sectionId)) {
      // Collapse the clicked section
      this.state.expandedSections.delete(sectionId);
      itemsContainer.classList.remove('expanded');
      itemsContainer.classList.add('collapsed');
      chevron.classList.remove('rotate-180');
    } else {
      // Auto-close all other expanded sections first
      this.closeAllSections();

      // Then expand the clicked section
      this.state.expandedSections.add(sectionId);
      itemsContainer.classList.remove('collapsed');
      itemsContainer.classList.add('expanded');
      chevron.classList.add('rotate-180');
    }
  },

  // Close all expanded sections
  closeAllSections() {
    this.state.expandedSections.forEach(sectionId => {
      const section = document.querySelector(`[data-section="${sectionId}"]`);
      if (section) {
        const itemsContainer = section.querySelector('.nav-section-items');
        const chevron = section.querySelector('.fa-chevron-down');

        if (itemsContainer && chevron) {
          itemsContainer.classList.remove('expanded');
          itemsContainer.classList.add('collapsed');
          chevron.classList.remove('rotate-180');
        }
      }
    });
    this.state.expandedSections.clear();
  },

  // Handle sidebar toggle from layout component
  handleSidebarToggle(isCollapsed) {
    this.state.sidebarCollapsed = isCollapsed;

    // Re-render navigation to update collapsed state
    this.renderNavigation();

    // If collapsing, close all expanded sections
    if (isCollapsed) {
      this.state.expandedSections.clear();
    }
  },

  // Ensure dashboard is highlighted (utility method)
  highlightDashboard() {
    this.setActivePage('dashboard');
  }
};

// Export to global scope
window.ModernNavigation = ModernNavigation;
