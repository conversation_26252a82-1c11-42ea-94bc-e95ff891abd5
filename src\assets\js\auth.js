// SmartReport - Authentication Module
// Handles user authentication and session management


// Authentication manager
const AuthManager = {
  // Login form handler
  initializeLoginForm() {
    const loginForm = document.getElementById('login-form');
    if (!loginForm) {
      console.warn('Login form not found!');
      return;
    }

    console.log('✅ Login form found, adding event listener...');

    // Check for remembered username
    this.loadRememberedCredentials();

    loginForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      console.log('🔄 Login form submitted');
      await this.handleLogin(e.target);
    });
  },

  // Load remembered credentials
  loadRememberedCredentials() {
    const rememberedUsername = localStorage.getItem('smartreport_remember_username');
    if (rememberedUsername) {
      const usernameField = document.getElementById('username');
      const rememberCheckbox = document.getElementById('remember-me');

      if (usernameField) {
        usernameField.value = rememberedUsername;
        console.log('✅ Remembered username loaded');
      }

      if (rememberCheckbox) {
        rememberCheckbox.checked = true;
        console.log('✅ Remember me checkbox checked');
      }
    }
  },
  
  // Handle login
  async handleLogin(form) {
    console.log('🔄 Handling login...');
    const formData = new FormData(form);
    const username = formData.get('username');
    const password = formData.get('password');
    const rememberMe = formData.get('remember_me') === 'on';

    console.log('📝 Form data:', {
      username: username ? '***' : 'empty',
      password: password ? '***' : 'empty',
      rememberMe: rememberMe
    });

    // Clear any previous error messages
    if (window.hideLoginError && typeof window.hideLoginError === 'function') {
      window.hideLoginError();
    }

    // Validate inputs
    if (!username || !password) {
      console.warn('❌ Validation failed: missing username or password');
      this.showNotification('Please enter both username and password', 'error');
      return;
    }

    try {
      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      const btnText = document.getElementById('login-btn-text');
      const btnIcon = document.getElementById('login-btn-icon');

      if (submitBtn) {
        submitBtn.disabled = true;
        if (btnText) btnText.textContent = 'Signing in...';
        if (btnIcon) {
          btnIcon.classList.remove('fa-sign-in-alt');
          btnIcon.classList.add('fa-spinner', 'fa-spin');
        }
      }

      // Attempt login
      const response = await AuthAPI.login({ username, password });

      if (response.success) {
        // Store token (check both response.token and response.data.token)
        const token = response.token || response.data?.token;
        if (token) {
          localStorage.setItem('smartreport_token', token);
        }

        // Store user data (check both response.user and response.data.user)
        const user = response.user || response.data?.user;
        if (user) {
          SR.currentUser = user;
        }

        // Handle remember me functionality
        if (rememberMe) {
          localStorage.setItem('smartreport_remember_username', username);
          console.log('✅ Username saved for remember me');
        } else {
          localStorage.removeItem('smartreport_remember_username');
          console.log('🗑️ Remember me cleared');
        }

        // Enable application menu
        if (window.require) {
          const { ipcRenderer } = window.require('electron');
          await ipcRenderer.invoke('login-success');
        }

        // Show success message briefly
        this.showNotification('Login successful! Redirecting...', 'success');

        // Debug: Check if token was stored correctly
        const storedToken = localStorage.getItem('smartreport_token');
        console.log('🔍 Token check after login:', storedToken ? 'Token stored successfully' : 'Token NOT stored');

        // Small delay to ensure token is properly stored and show success message
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Redirect to main application
        await showMainApplication();


      } else {
        throw new Error(response.message || 'Login failed');
      }

    } catch (error) {
      console.error('Login error:', error);
      this.showNotification(error.message || 'Login failed. Please try again.', 'error');

      // Reset button state
      const submitBtn = form.querySelector('button[type="submit"]');
      const btnText = document.getElementById('login-btn-text');
      const btnIcon = document.getElementById('login-btn-icon');

      if (submitBtn) {
        submitBtn.disabled = false;
        if (btnText) btnText.textContent = 'Sign In';
        if (btnIcon) {
          btnIcon.classList.remove('fa-spinner', 'fa-spin');
          btnIcon.classList.add('fa-sign-in-alt');
        }
      }
    }
  },



  // Logout
  async logout() {
    try {
      // Call logout API
      await AuthAPI.logout();
    } catch (error) {
      console.error('Logout API error:', error);
      // Continue with logout even if API call fails
    }

    // Stop persistent academic check
    if (window.stopPersistentAcademicCheck && typeof window.stopPersistentAcademicCheck === 'function') {
      window.stopPersistentAcademicCheck();
    }

    // Clear local storage
    localStorage.removeItem('smartreport_token');

    // Clear current user
    SR.currentUser = null;
    SR.isInitialized = false;

    // Hide application menu
    if (window.require) {
      const { ipcRenderer } = window.require('electron');
      await ipcRenderer.invoke('logout');
    }

    // Show notification
    this.showNotification('You have been logged out successfully.', 'info');

    // Redirect to login
    showLoginScreen();
  },
  
  // Check if user is authenticated
  isAuthenticated() {
    return !!localStorage.getItem('smartreport_token') && !!SR.currentUser;
  },
  
  // Get current user
  getCurrentUser() {
    return SR.currentUser;
  },
  
  // Check if user has specific role
  hasRole(role) {
    return SR.currentUser && SR.currentUser.role === role;
  },
  
  // Check if user has any of the specified roles
  hasAnyRole(roles) {
    return SR.currentUser && roles.includes(SR.currentUser.role);
  },
  
  // Get user's full name
  getUserFullName() {
    if (!SR.currentUser) return 'Unknown User';
    
    const { first_name, middle_name, last_name } = SR.currentUser;
    let fullName = first_name || '';
    
    if (middle_name) {
      fullName += ` ${middle_name}`;
    }
    
    if (last_name) {
      fullName += ` ${last_name}`;
    }
    
    return fullName.trim() || 'Unknown User';
  },
  
  // Get user's role display name
  getRoleDisplayName() {
    if (!SR.currentUser) return 'Unknown Role';

    const roleNames = {
      'super_user': 'Super User',
      'system_admin': 'System Administrator',
      'class_teacher': 'Class Teacher',
      'subject_teacher': 'Subject Teacher'
    };

    return roleNames[SR.currentUser.role] || 'Unknown Role';
  },
  

  
  // Initialize user profile dropdown
  initializeUserProfile() {
    const userProfile = document.getElementById('user-profile');
    const userDropdown = document.getElementById('user-dropdown');
    
    if (userProfile && userDropdown) {
      userProfile.addEventListener('click', (e) => {
        e.preventDefault();
        userDropdown.classList.toggle('show');
      });
      
      // Close dropdown when clicking outside
      document.addEventListener('click', (e) => {
        if (!userProfile.contains(e.target)) {
          userDropdown.classList.remove('show');
        }
      });
    }
  },
  
  // Update user interface with current user info
  updateUserInterface() {
    if (!SR.currentUser) return;
    
    // Update user name display
    const userNameElements = document.querySelectorAll('.user-name');
    userNameElements.forEach(element => {
      element.textContent = this.getUserFullName();
    });
    
    // Update user role display
    const userRoleElements = document.querySelectorAll('.user-role');
    userRoleElements.forEach(element => {
      element.textContent = this.getRoleDisplayName();
    });
    
    // Update user email display
    const userEmailElements = document.querySelectorAll('.user-email');
    userEmailElements.forEach(element => {
      element.textContent = SR.currentUser.email || '';
    });
    
    // Update profile picture if available
    const profilePicElements = document.querySelectorAll('.user-profile-pic');
    profilePicElements.forEach(element => {
      if (SR.currentUser.profile_picture) {
        element.src = SR.currentUser.profile_picture;
      } else {
        // Use default avatar based on role
        const defaultAvatars = {
          'system_admin': '../assets/images/admin-avatar.png',
          'class_teacher': '../assets/images/teacher-avatar.png',
          'subject_teacher': '../assets/images/teacher-avatar.png'
        };
        element.src = defaultAvatars[SR.currentUser.role] || '../assets/images/default-avatar.png';
      }
    });

    // Update user profile image in top bar
    if (window.Layout && window.Layout.updateUserProfileImage) {
      window.Layout.updateUserProfileImage();
    }

    // Update academic information in navbar
    this.updateAcademicInfo();
  },

  // Update academic information in navbar
  updateAcademicInfo() {
    const dateElement = document.getElementById('navbar-date');
    const academicYearElement = document.getElementById('navbar-academic-year');

    // Update date and time
    const currentTime = new Date().toLocaleString('en-UG', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    if (dateElement) {
      dateElement.textContent = currentTime;
    }

    if (academicYearElement) {
      // Check if academic data is available from SR global object
      const currentYear = window.SR?.currentAcademicYear;
      const currentTerm = window.SR?.currentTerm;

      if (currentYear && currentTerm) {
        academicYearElement.textContent = `Academic Year ${currentYear} - ${currentTerm}`;
        academicYearElement.classList.remove('text-yellow-600');
      } else {
        academicYearElement.textContent = 'Academic Setup Required';
        academicYearElement.classList.add('text-yellow-600');
      }
    }
  },
  
  // Show notification
  showNotification(message, type = 'info') {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, type);
    }
  },

  // Initialize all authentication components
  initialize() {
    this.initializeLoginForm();
    this.initializeUserProfile();

    // Update UI if user is already authenticated
    if (this.isAuthenticated()) {
      this.updateUserInterface();
    }
  }
};

// Session management
const SessionManager = {
  // Check session validity
  async checkSession() {
    try {
      const token = localStorage.getItem('smartreport_token');
      if (!token) return false;

      const response = await AuthAPI.validateToken(token);
      if (response.success) {
        SR.currentUser = response.user;
        return true;
      }
    } catch (error) {
      console.error('Session validation failed:', error);
    }
    
    // Clear invalid session
    localStorage.removeItem('smartreport_token');
    SR.currentUser = null;
    return false;
  },
  
  // Refresh session periodically
  startSessionRefresh() {
    // Check session every 30 minutes
    setInterval(async () => {
      if (AuthManager.isAuthenticated()) {
        const isValid = await this.checkSession();
        if (!isValid) {
          UIUtils.showNotification('Your session has expired. Please login again.', 'warning');
          AuthManager.logout();
        }
      }
    }, 30 * 60 * 1000); // 30 minutes
  }
};

// Initialize authentication when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('🔧 DOM loaded, initializing AuthManager...');
  AuthManager.initialize();
  SessionManager.startSessionRefresh();

  // Add a test function to help debug
  window.testLogin = function() {
    console.log('🧪 Testing login form...');
    const form = document.getElementById('login-form');
    const username = document.getElementById('username');
    const password = document.getElementById('password');

    console.log('Form found:', !!form);
    console.log('Username field found:', !!username);
    console.log('Password field found:', !!password);
    console.log('Username value:', username?.value || 'empty');
    console.log('Password value:', password?.value ? '***' : 'empty');

    if (form && username && password) {
      console.log('✅ All form elements found');
      if (username.value && password.value) {
        console.log('🚀 Triggering login...');
        AuthManager.handleLogin(form);
      } else {
        console.log('❌ Please enter username and password');
        alert('Please enter both username and password');
      }
    } else {
      console.log('❌ Form elements missing');
      alert('Form elements not found');
    }
  };

  // Add a quick login test function
  window.quickLogin = function() {
    console.log('🚀 Quick login test with default credentials...');
    const username = document.getElementById('username');
    const password = document.getElementById('password');

    if (username && password) {
      username.value = 'admin';
      password.value = 'admin123';
      console.log('✅ Credentials filled, triggering login...');

      const form = document.getElementById('login-form');
      if (form) {
        AuthManager.handleLogin(form);
      } else {
        console.error('❌ Login form not found');
      }
    } else {
      console.error('❌ Username or password field not found');
    }
  };
});

// Toggle password visibility function is now in index.html to avoid conflicts

// Show forgot password modal
function showForgotPassword() {
  if (window.UIUtils) {
    UIUtils.showNotification('Forgot Password feature coming soon!', 'info');
  } else {
    alert('Forgot Password feature coming soon!');
  }
}

// Export to global scope
window.AuthManager = AuthManager;
window.SessionManager = SessionManager;
window.togglePassword = togglePassword;
window.showForgotPassword = showForgotPassword;
