const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get all class assignments with optional filters
router.get('/', async (req, res) => {
  try {
    const { academic_year_id, class_id, teacher_id } = req.query;

    let query = `
      SELECT 
        csa.id, csa.class_id, csa.subject_id, csa.subject_level, csa.teacher_id,
        csa.academic_year_id, csa.periods_per_week,
        c.name as class_name, cl.name as class_level_name, cl.display_name as class_level_display,
        s.name as stream_name,
        CASE 
          WHEN csa.subject_level = 'o_level' THEN os.name
          WHEN csa.subject_level = 'a_level' THEN als.name
        END as subject_name,
        CASE 
          WHEN csa.subject_level = 'o_level' THEN os.short_name
          WHEN csa.subject_level = 'a_level' THEN als.short_name
        <PERSON><PERSON> as subject_short_name,
        CONCAT(t.first_name, ' ', t.last_name) as teacher_name,
        t.initials as teacher_initials,
        ay.name as academic_year_name
      FROM class_subject_assignments csa
      JOIN classes c ON csa.class_id = c.id
      JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN streams s ON c.stream_id = s.id
      LEFT JOIN o_level_subjects os ON csa.subject_id = os.id AND csa.subject_level = 'o_level'
      LEFT JOIN a_level_subjects als ON csa.subject_id = als.id AND csa.subject_level = 'a_level'
      LEFT JOIN teachers t ON csa.teacher_id = t.id
      JOIN academic_years ay ON csa.academic_year_id = ay.id
      WHERE 1=1
    `;

    let params = [];

    if (academic_year_id) {
      query += ' AND csa.academic_year_id = ?';
      params.push(academic_year_id);
    }

    if (class_id) {
      query += ' AND csa.class_id = ?';
      params.push(class_id);
    }

    if (teacher_id) {
      query += ' AND csa.teacher_id = ?';
      params.push(teacher_id);
    }



    query += ' ORDER BY ay.name DESC, cl.sort_order, subject_name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get class assignments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve class assignments'
    });
  }
});

// Get class assignment by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT 
        csa.*, c.name as class_name, cl.name as class_level_name,
        CASE 
          WHEN csa.subject_level = 'o_level' THEN os.name
          WHEN csa.subject_level = 'a_level' THEN als.name
        END as subject_name,
        CONCAT(t.first_name, ' ', t.last_name) as teacher_name,
        ay.name as academic_year_name
      FROM class_subject_assignments csa
      JOIN classes c ON csa.class_id = c.id
      JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN o_level_subjects os ON csa.subject_id = os.id AND csa.subject_level = 'o_level'
      LEFT JOIN a_level_subjects als ON csa.subject_id = als.id AND csa.subject_level = 'a_level'
      LEFT JOIN teachers t ON csa.teacher_id = t.id
      JOIN academic_years ay ON csa.academic_year_id = ay.id
      WHERE csa.id = ?
    `;

    const result = await executeQuery(query, [id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class assignment not found'
      });
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get class assignment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve class assignment'
    });
  }
});

// Create new class assignment
router.post('/', async (req, res) => {
  try {
    const { 
      class_id, subject_id, subject_level, teacher_id, 
      academic_year_id, periods_per_week 
    } = req.body;

    // Validate required fields
    if (!class_id || !subject_id || !subject_level || !academic_year_id) {
      return res.status(400).json({
        success: false,
        message: 'Class ID, subject ID, subject level, and academic year ID are required'
      });
    }

    // Validate subject level
    if (!['o_level', 'a_level'].includes(subject_level)) {
      return res.status(400).json({
        success: false,
        message: 'Subject level must be either o_level or a_level'
      });
    }

    // Check if assignment already exists
    const existingCheck = await executeQuery(
      'SELECT id FROM class_subject_assignments WHERE class_id = ? AND subject_id = ? AND subject_level = ? AND academic_year_id = ?',
      [class_id, subject_id, subject_level, academic_year_id]
    );

    if (existingCheck.success && existingCheck.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'This subject is already assigned to this class for this academic year'
      });
    }

    // Business Logic Validation: If teacher is provided, ensure they are qualified to teach this subject
    if (teacher_id) {
      const teacherQualificationCheck = await executeQuery(
        'SELECT id FROM teacher_subjects WHERE teacher_id = ? AND subject_id = ? AND subject_level = ? AND academic_year_id = ?',
        [teacher_id, subject_id, subject_level, academic_year_id]
      );

      if (!teacherQualificationCheck.success || teacherQualificationCheck.data.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Teacher is not qualified to teach this subject for this academic year'
        });
      }
    }

    // Insert new assignment
    const insertQuery = `
      INSERT INTO class_subject_assignments (
        class_id, subject_id, subject_level, teacher_id,
        academic_year_id, periods_per_week
      ) VALUES (?, ?, ?, ?, ?, ?)
    `;

    const insertResult = await executeQuery(insertQuery, [
      class_id, subject_id, subject_level, teacher_id, 
      academic_year_id, periods_per_week || 1
    ]);

    if (!insertResult.success) {
      throw new Error(insertResult.error);
    }

    res.status(201).json({
      success: true,
      message: 'Class assignment created successfully',
      data: { assignment_id: insertResult.data.insertId }
    });

  } catch (error) {
    console.error('Create class assignment error:', error);
    res.status(500).json({
      success: false,
      message: error.message.includes('chk_teacher_subject_qualification') 
        ? 'Teacher is not qualified to teach this subject'
        : 'Failed to create class assignment'
    });
  }
});

// Update class assignment
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { teacher_id, periods_per_week } = req.body;

    // Check if assignment exists
    const checkResult = await executeQuery(
      'SELECT id FROM class_subject_assignments WHERE id = ?', 
      [id]
    );

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class assignment not found'
      });
    }

    // Update assignment
    const updateQuery = `
      UPDATE class_subject_assignments
      SET teacher_id = ?, periods_per_week = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const updateResult = await executeQuery(updateQuery, [
      teacher_id, periods_per_week, id
    ]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    res.json({
      success: true,
      message: 'Class assignment updated successfully'
    });

  } catch (error) {
    console.error('Update class assignment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update class assignment'
    });
  }
});

// Delete class assignment
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if assignment exists
    const checkResult = await executeQuery(
      'SELECT id FROM class_subject_assignments WHERE id = ?', 
      [id]
    );

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class assignment not found'
      });
    }

    // Delete assignment
    const deleteResult = await executeQuery(
      'DELETE FROM class_subject_assignments WHERE id = ?', 
      [id]
    );

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'Class assignment deleted successfully'
    });

  } catch (error) {
    console.error('Delete class assignment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete class assignment'
    });
  }
});

module.exports = router;
