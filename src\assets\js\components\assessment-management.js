// SmartReport - Assessment Management Components
// Comprehensive assessment management system for CAs and Exams

// Uses global API services: window.AssessmentsAPI, window.SubjectsAPI, etc.
// Uses global config: window.SRConfig
// Uses environment configuration: ../config/environment.js

const AssessmentManagementComponents = {
  // Standardized dropdown placeholder messages
  DROPDOWN_PLACEHOLDERS: {
    DEFAULT: {
      ACADEMIC_YEAR: 'Select Academic Year',
      TERM: 'Select Term',
      EDUCATION_LEVEL: 'Select Education Level',
      CLASS: 'Select Class',
      SUBJECT: 'Select Subject',
      CA_NUMBER: 'Select CA Number',
      EXAM_TYPE: 'Select Exam Type',
      CA_METHOD: 'Select CA Method',
      ASSESSMENT: 'Select Assessment'
    },
    DISABLED: {
      TERM: 'Select Academic Year First',
      EDUCATION_LEVEL: 'Select Term First',
      CLASS: 'Select Education Level First',
      SUBJECT: 'Select Class First',
      CA_NUMBER: 'Select Subject First',
      EXAM_TYPE: 'Select Subject First'
    }
  },

  // Component state
  state: {
    subjects: [], // Combined subjects for backward compatibility
    oLevelSubjects: [],
    aLevelSubjects: [],
    classes: [],
    academicYears: [],
    terms: [],
    educationLevels: [],
    caConfigurations: [],
    loading: false,
    filters: {
      academicYear: '',
      term: '',
      subject: '',
      class: '',
      assessmentType: ''
    }
  },

  // Initialize component
  async init() {
    await this.loadInitialData();
  },

  // Load initial data
  async loadInitialData() {
    if (this.state.loading) return;

    const hasEssentialData =
      this.state.educationLevels?.data?.length > 0 &&
      this.state.classes?.data?.length > 0 &&
      this.state.oLevelSubjects?.data?.length > 0 &&
      this.state.aLevelSubjects?.data?.length > 0;

    if (hasEssentialData) return;

    try {
      this.state.loading = true;

      // Initialize academic context first
      await window.AcademicContext.initialize();

      // Use the API services with proper error handling
      // Only load what's needed for assessment management
      const [oLevelSubjects, aLevelSubjects, classes, educationLevels, caConfigurations] = await Promise.all([
        window.SubjectsAPI.oLevel.getAll(),
        window.SubjectsAPI.aLevel.getAll(),
        window.ClassesAPI.getAll(),
        window.LevelsAPI.getAll(),
        window.CAConfigurationAPI.getAll()
      ]);

      // Get academic years and terms from academic context
      const academicContext = window.AcademicContext.getContext();
      this.state.academicYears = { success: true, data: academicContext.allAcademicYears || [] };
      this.state.terms = { success: true, data: academicContext.allTerms || [] };

      // Store the essential data for assessment management
      this.state.oLevelSubjects = oLevelSubjects;
      this.state.aLevelSubjects = aLevelSubjects;
      this.state.classes = classes;
      this.state.educationLevels = educationLevels;
      this.state.caConfigurations = caConfigurations;

      // Create combined subjects list for backward compatibility (if needed)
      const allSubjects = [
        ...(oLevelSubjects.data || []).map(s => ({ ...s, level: 'o_level' })),
        ...(aLevelSubjects.data || []).map(s => ({ ...s, level: 'a_level' }))
      ];
      this.state.subjects = { success: true, data: allSubjects };

    } catch (error) {
      // Show error notification if available
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to load assessment data', 'error');
      }
    } finally {
      this.state.loading = false;
    }
  },

  // Cleanup component
  cleanup() {
    this.state.loading = false;
    this.state.subjects = [];
    this.state.oLevelSubjects = null;
    this.state.aLevelSubjects = null;
    this.state.classes = null;
    this.state.academicYears = null;
    this.state.terms = null;
    this.state.educationLevels = null;
    this.state.caConfigurations = null;
  },

  // Helper function to show notifications
  showNotification(message, type = 'info') {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, type);
    } else if (window.showNotification) {
      window.showNotification(message, type);
    } else {
      if (type === 'error') {
        alert('Error: ' + message);
      } else {
        alert(message);
      }
    }
  }
};



// Enter CA Scores Component
const EnterCAScoresComponent = {
  // Component state
  state: {
    selectedAssessment: null,
    students: [],
    scores: {}
  },

  // Render enter CA scores interface
  render() {
    // Check if academic context is available
    const hasAcademicContext = window.AcademicContext &&
                              window.AcademicContext.getActiveAcademicYear() &&
                              window.AcademicContext.getActiveTerm();

    if (!hasAcademicContext) {
      return this.renderAcademicContextError();
    }

    // Show loading state if data is still loading
    if (AssessmentManagementComponents.state.loading) {
      // Use centralized loading state from page router
      const container = document.getElementById('content-area');
      if (container && window.PageRouter) {
        window.PageRouter.showLoadingState(container, 'Loading assessment data...');
      }
      return '';
    }

    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Enter CA Scores',
          'Input continuous assessment scores for students'
        )}

        <!-- Assessment Selection -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-4 border-b border-primary-200">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-primary-900 flex items-center">
              ${SRDesignSystem.components.icon('fas fa-filter', 'base', 'primary-600')}
              <span class="ml-3">Select Parameters</span>
            </h3>
          </div>

          <div class="p-6">

          <!-- First Row: Academic Context -->
          <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap} mb-4">
            ${SRDesignSystem.forms.select('ca_academic_year', 'Academic Year', [], '')}
            ${SRDesignSystem.forms.select('ca_term', 'Term', [], '')}
            ${SRDesignSystem.forms.select('ca_level', 'Education Level', [
              { value: '', label: AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DEFAULT.EDUCATION_LEVEL },
              { value: 'o_level', label: 'O-Level' },
              { value: 'a_level', label: 'A-Level' }
            ], '')}
          </div>

          <!-- Second Row: Class and Subject -->
          <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap} mb-4">
            ${SRDesignSystem.forms.select('ca_class', 'Class', [], '')}
            ${SRDesignSystem.forms.select('ca_subject', 'Subject', [], '')}
          </div>

          <!-- Third Row: Paper and CA Number -->
          <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
            <div id="ca_paper_container" style="display: none;">
              ${SRDesignSystem.forms.select('ca_paper', 'Paper', [], '', { id: 'ca_paper' })}
            </div>
            ${SRDesignSystem.forms.select('ca_number', 'CA Number', [], '')}
          </div>

            <div class="mt-6 flex justify-end">
              ${SRDesignSystem.forms.button('load-students', 'Load Students', 'primary', {
                onclick: 'EnterCAScoresComponent.loadStudents()',
                  icon: 'fas fa-search'
              })}
            </div>
          </div>
        </div>

        <!-- Scores Entry -->
        <div id="scores-entry-section" class="hidden bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-green-50 to-emerald-100 px-6 py-4 border-b border-green-200">
            <div class="flex items-center justify-between">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-green-900 flex items-center">
                ${SRDesignSystem.components.icon('fas fa-edit', 'base', 'green-600')}
                <span class="ml-3">Enter Scores</span>
              </h3>
              <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-600">Scores are saved automatically</span>
              </div>
            </div>
          </div>
          <div class="overflow-x-auto max-h-96 overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="px-6 py-3 text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Student Name</th>
                  <th class="px-6 py-3 text-center ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Competency Score</th>
                  <th class="px-6 py-3 text-center ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Status</th>
                </tr>
              </thead>
              <tbody id="scores-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Students and scores will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

    `;
  },

  // Render academic context error
  renderAcademicContextError(title = 'Assessment Management', description = 'Manage assessments', componentName = 'AssessmentManagementComponents') {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(title, description)}

        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-600')}
            </div>
            <div class="ml-4">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-red-900">Academic Context Required</h3>
              <p class="text-red-700 mt-2">
                You cannot enter assessment data without setting up the academic year and terms first.
              </p>
              <p class="text-red-700 mt-1">
                Please go to <strong>Academic Year → Setup</strong> to configure your academic year and terms before entering assessments.
              </p>
              <div class="mt-4 text-center">
                ${SRDesignSystem.forms.button('setup-academic-assessment', 'Setup Academic Year & Terms', 'danger', {
                  icon: 'fas fa-calendar-plus',
                  onclick: `${componentName}.navigateToAcademicSetup()`
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Set default academic context in dropdowns
  async setDefaultAcademicContext() {
    const activeYear = window.AcademicContext.getActiveAcademicYear();
    const activeTerm = window.AcademicContext.getActiveTerm();

    const academicYearSelect = document.getElementById('ca_academic_year');
    if (academicYearSelect && activeYear && activeYear.id) {
      academicYearSelect.value = activeYear.id;
      this.updateTermsDropdown();

      // Set term value directly - DOM should be ready due to lifecycle manager
      const termSelect = document.getElementById('ca_term');
      if (termSelect && activeTerm && activeTerm.id) {
        termSelect.value = activeTerm.id;
        this.updateLevelDropdown();
      }
    }
  },

  // Load students for selected assessment
  async loadStudents() {
    const academicYear = document.getElementById('ca_academic_year').value;
    const term = document.getElementById('ca_term').value;
    const level = document.getElementById('ca_level').value;
    const subject = document.getElementById('ca_subject').value;
    const classId = document.getElementById('ca_class').value;
    const caNumber = document.getElementById('ca_number').value;

    // For A-Level, also get paper selection
    let paperId = null;
    if (level === 'a_level') {
      paperId = document.getElementById('ca_paper').value;
      if (!paperId) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Please select a paper for A-Level subjects', 'error');
        }
        return;
      }
    }

    if (!academicYear || !term || !level || !subject || !classId || !caNumber) {
      // Show error notification if available
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Please select all required fields', 'error');
      }
      return;
    }

    try {
      // Set up the assessment context (simplified - no need to fetch assessments for CA entry)
      this.state.selectedAssessment = {
        subject_id: subject,
        academic_year_id: academicYear,
        term_id: term,
        class_id: classId,
        ca_number: caNumber,
        level: level,
        max_score: 3, // Competency scale is 0-3
        subject_paper_id: paperId // For A-Level subjects
      };

      // Load students with existing CA scores using CAScoresAPI
      // O-Level: subject-based (o_level_subject_continuous_assessments_scores)
      // A-Level: subject paper-based (a_level_paper_continuous_assessments_scores)
      const studentsResult = await window.CAScoresAPI.getStudents({
        class_id: classId,
        subject_id: subject,
        academic_year_id: academicYear,
        term_id: term,
        ca_number: caNumber,
        subject_paper_id: paperId // For A-Level subjects only
      });

      if (studentsResult.success && studentsResult.data && studentsResult.data.length > 0) {
        // Map students for CA scores entry - the new API already includes existing scores
        this.state.students = studentsResult.data.map(student => ({
          id: student.id,
          student_id: student.id,
          admission_number: student.admission_number,
          first_name: student.first_name,
          last_name: student.last_name,
          student_name: `${student.first_name} ${student.last_name}`,
          gender: student.gender,
          stream_name: student.stream_name,
          class_name: student.class_name,
          current_score: student.ca_score // CA score from the new API
        }));

        this.renderScoresTable();
        document.getElementById('scores-entry-section').classList.remove('hidden');
      } else {
        // Handle empty state - no students found
        this.state.students = [];
        this.renderEmptyState();
        document.getElementById('scores-entry-section').classList.remove('hidden');
      }
    } catch (error) {
      console.error('Error loading students for CA scores:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show(`Failed to load students: ${error.message}`, 'error');
      }
    }
  },

  // Render empty state when no students are found
  renderEmptyState() {
    const container = document.getElementById('scores-entry-section');
    if (!container) return;

    const selectedClass = document.getElementById('ca_class');
    const selectedSubject = document.getElementById('ca_subject');
    const selectedCANumber = document.getElementById('ca_number');

    const className = selectedClass?.options[selectedClass.selectedIndex]?.text || 'Selected Class';
    const subjectName = selectedSubject?.options[selectedSubject.selectedIndex]?.text || 'Selected Subject';
    const caNumber = selectedCANumber?.value || 'Selected CA';

    container.innerHTML = `
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 mb-4">
            <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-2.239" />
            </svg>
          </div>
          <h3 class="${SRDesignSystem.responsive.text.lg} font-medium text-gray-900 mb-2">No Students Found</h3>
          <p class="text-gray-500 mb-4">
            No students are enrolled in <strong>${className}</strong> for the selected academic context.
          </p>
          <div class="bg-gray-50 rounded-lg p-4 mb-4">
            <h4 class="font-medium text-gray-700 mb-2">Selected Configuration:</h4>
            <ul class="${SRDesignSystem.responsive.text.sm} text-gray-600 space-y-1">
              <li><strong>Class:</strong> ${className}</li>
              <li><strong>Subject:</strong> ${subjectName}</li>
              <li><strong>CA Number:</strong> ${caNumber}</li>
            </ul>
          </div>
          <p class="${SRDesignSystem.responsive.text.sm} text-gray-500">
            Please ensure students are enrolled in this class for the current academic year and term.
          </p>
        </div>
      </div>
    `;

  },


  // Render scores table
  renderScoresTable() {
    const tbody = document.getElementById('scores-table-body');
    if (!tbody) return;

    // Check if students array is empty
    if (!this.state.students || this.state.students.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="6" class="px-6 py-12 text-center text-gray-500">
            <div class="text-gray-400 mb-4">
              ${SRDesignSystem.components.icon('fas fa-users', '4xl', 'gray-300')}
            </div>
            <p class="${SRDesignSystem.responsive.text.lg} font-medium">No students found</p>
            <p class="${SRDesignSystem.responsive.text.sm}">Select a class and subject to view students for assessment.</p>
          </td>
        </tr>
      `;
      return;
    }

    tbody.innerHTML = this.state.students.map(student => `
      <tr>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-900">${student.first_name} ${student.last_name}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <input type="number"
                 id="competency_score_${student.id}"
                 min="0.0"
                 max="3.0"
                 step="0.1"
                 value="${student.current_score || ''}"
                 class="w-20 px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                 onchange="EnterCAScoresComponent.updateCompetencyScore(${student.id}, this.value)"
                 title="Enter 0.0 for absent students, or 0.9-3.0 for present students">
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <span id="status_${student.id}" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            student.current_score !== null ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          }">
            ${student.current_score !== null ? 'Entered' : 'Pending'}
          </span>
        </td>
      </tr>
    `).join('');
  },

  // Update competency score for student
  updateCompetencyScore(studentId, score) {
    if (!this.state.scores[studentId]) {
      this.state.scores[studentId] = {};
    }

    // Validate CA score (0.0 for absent, 0.9-3.0 with one decimal place)
    if (score) {
      const numScore = parseFloat(score);
      const roundedScore = Math.round(numScore * 10) / 10;

      // Allow 0.0 for absent students, otherwise require 0.9-3.0
      if (roundedScore !== 0.0 && (roundedScore < 0.9 || roundedScore > 3.0)) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Competency score must be 0.0 (absent) or between 0.9 and 3.0', 'error');
        }
        // Reset the input field
        const inputElement = document.getElementById(`competency_score_${studentId}`);
        if (inputElement) {
          inputElement.value = '';
        }
        return;
      }

      // Check decimal precision
      if (score.toString().includes('.') && score.toString().split('.')[1].length > 1) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Competency score must have at most one decimal place', 'error');
        }
        // Reset the input field
        const inputElement = document.getElementById(`competency_score_${studentId}`);
        if (inputElement) {
          inputElement.value = '';
        }
        return;
      }

      this.state.scores[studentId].competency_score = roundedScore;
    } else {
      this.state.scores[studentId].competency_score = null;
    }

    // Update status indicator and save automatically
    const statusElement = document.getElementById(`status_${studentId}`);
    if (statusElement) {
      if (score) {
        statusElement.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800';
        statusElement.textContent = 'Saving...';

        // Auto-save the score
        this.autoSaveScore(studentId);
      } else {
        statusElement.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800';
        statusElement.textContent = 'Pending';
      }
    }
  },

  // Auto-save individual score
  async autoSaveScore(studentId) {
    if (!this.state.selectedAssessment || !this.state.scores[studentId]) {
      return;
    }

    const scoreData = {
      student_id: parseInt(studentId),
      academic_year_id: this.state.selectedAssessment.academic_year_id,
      term_id: this.state.selectedAssessment.term_id,
      ca_number: this.state.selectedAssessment.ca_number,
      competency_score: this.state.scores[studentId].competency_score
    };

    // For A-Level, use subject_paper_id; for O-Level, use subject_id
    if (this.state.selectedAssessment.level === 'a_level') {
      scoreData.subject_paper_id = this.state.selectedAssessment.subject_paper_id;
    } else {
      scoreData.subject_id = this.state.selectedAssessment.subject_id;
    }

    try {
      const result = await window.CAScoresAPI.create([scoreData]);

      const statusElement = document.getElementById(`status_${studentId}`);
      if (statusElement) {
        if (result.success) {
          statusElement.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800';
          statusElement.textContent = 'Entered';
        } else {
          statusElement.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800';
          statusElement.textContent = 'Error';
          console.error('Failed to save CA score:', result.message);
        }
      }
    } catch (error) {
      console.error('Error auto-saving CA score:', error);
      const statusElement = document.getElementById(`status_${studentId}`);
      if (statusElement) {
        statusElement.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800';
        statusElement.textContent = 'Error';
      }
    }
  },




  // Initialize component
  async init() {
    this.resetComponentState();

    await window.AcademicContext.initialize();
    const activeYear = window.AcademicContext.getActiveAcademicYear();
    const activeTerm = window.AcademicContext.getActiveTerm();

    if (!activeYear || !activeTerm) return;

    const academicYearsLength = AssessmentManagementComponents.state.academicYears?.data?.length || 0;
    const termsLength = AssessmentManagementComponents.state.terms?.data?.length || 0;

    if (academicYearsLength === 0 || termsLength === 0) {
      await AssessmentManagementComponents.loadInitialData();
    }

    // Initialize directly - DOM should be ready due to lifecycle manager
    this.populateDropdowns();
    await this.setDefaultAcademicContext();
    this.initializeEventListeners();
  },

  // Reset component state
  resetComponentState() {
    this.state.selectedAssessment = null;
    this.state.students = [];
    this.state.scores = {};
  },

  // Cleanup component
  cleanup() {
    this.resetComponentState();

    // Hide any sections that might be visible
    const scoresEntry = document.getElementById('scores-entry-section');
    if (scoresEntry) scoresEntry.classList.add('hidden');

    // Reset any form elements
    const form = document.querySelector('#ca-scores-form');
    if (form) form.reset();

    // Clear any notifications or modals
  },

  // Populate dropdowns with data
  populateDropdowns() {
    const academicYearSelect = document.getElementById('ca_academic_year');

    if (academicYearSelect) {
      academicYearSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DEFAULT.ACADEMIC_YEAR}</option>`;

      const academicContext = window.AcademicContext.getContext();
      const academicYears = academicContext.allAcademicYears || [];

      if (Array.isArray(academicYears) && academicYears.length > 0) {
        academicYears.forEach(year => {
          const option = document.createElement('option');
          option.value = year.id;
          option.textContent = year.name;
          academicYearSelect.appendChild(option);
        });
      }
    }

    this.initializeDisabledDropdowns();
  },

  // Initialize dropdowns in disabled state
  initializeDisabledDropdowns() {
    // Terms dropdown - disabled until academic year is selected
    const termSelect = document.getElementById('ca_term');
    if (termSelect) {
      termSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.TERM}</option>`;
      termSelect.disabled = true;
    }

    // Level dropdown - disabled until term is selected
    const levelSelect = document.getElementById('ca_level');
    if (levelSelect) {
      levelSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.EDUCATION_LEVEL}</option>`;
      levelSelect.disabled = true;
    }

    // Class dropdown - disabled until level is selected
    const classSelect = document.getElementById('ca_class');
    if (classSelect) {
      classSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.CLASS}</option>`;
      classSelect.disabled = true;
    }

    // Subject dropdown - disabled until level is selected
    const subjectSelect = document.getElementById('ca_subject');
    if (subjectSelect) {
      subjectSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.SUBJECT}</option>`;
      subjectSelect.disabled = true;
    }

    // CA Number dropdown - disabled until class and subject are selected
    const caNumberSelect = document.getElementById('ca_number');
    if (caNumberSelect) {
      caNumberSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.CA_NUMBER}</option>`;
      caNumberSelect.disabled = true;
    }
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Academic Year change - enables Term dropdown
    const academicYearSelect = document.getElementById('ca_academic_year');
    if (academicYearSelect) {
      academicYearSelect.addEventListener('change', () => {
        this.updateTermsDropdown();
      });
    }

    // Term change - enables Level dropdown
    const termSelect = document.getElementById('ca_term');
    if (termSelect) {
      termSelect.addEventListener('change', () => {
        this.updateLevelDropdown();
      });
    }

    // Level change - enables Class and Subject dropdowns
    const levelSelect = document.getElementById('ca_level');
    if (levelSelect) {
      levelSelect.addEventListener('change', () => {
        this.updateClassAndSubjectDropdowns();
        // Hide paper when education level is unselected
        if (!levelSelect.value) {
          this.hidePaperSelection();
        }
      });
    }

    // Class change - handles Subject and CA Number cascading
    const classSelect = document.getElementById('ca_class');
    if (classSelect) {
      classSelect.addEventListener('change', () => {
        this.handleClassChange();
        // Hide paper when class is unselected
        if (!classSelect.value) {
          this.hidePaperSelection();
        }
      });
    }

    // Subject change - handles paper selection and updates CA Number dropdown
    const subjectSelect = document.getElementById('ca_subject');
    if (subjectSelect) {
      subjectSelect.addEventListener('change', () => {
        this.handleSubjectChange();
        // Hide paper when subject is unselected
        if (!subjectSelect.value) {
          this.hidePaperSelection();
        }
      });
    }
  },

  // Handle subject selection change
  handleSubjectChange() {
    const subjectSelect = document.getElementById('ca_subject');
    const levelSelect = document.getElementById('ca_level');

    if (!subjectSelect || !levelSelect) return;

    const selectedSubject = subjectSelect.value;
    const selectedLevel = levelSelect.value;

    if (selectedSubject && selectedLevel === 'a_level') {
      // Check if this is an A-level subject and show paper selection
      const selectedOption = subjectSelect.options[subjectSelect.selectedIndex];
      const level = selectedOption.dataset.level;

      if (level === 'a_level') {
        this.handleALevelPaperSelection(selectedSubject);
      } else {
        this.hidePaperSelection();
      }
    } else {
      this.hidePaperSelection();
    }

    // Always update CA number dropdown
    this.updateCANumberDropdown();
  },

  // Update terms dropdown when academic year is selected
  updateTermsDropdown() {
    const academicYearSelect = document.getElementById('ca_academic_year');
    const termSelect = document.getElementById('ca_term');

    if (!academicYearSelect || !termSelect) return;

    const selectedYear = academicYearSelect.value;

    if (!selectedYear) {
      // Reset and disable subsequent dropdowns
      termSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.TERM}</option>`;
      termSelect.disabled = true;
      this.resetLevelAndBelow();
      return;
    }

    // Enable and populate terms for the selected academic year
    termSelect.disabled = false;
    termSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DEFAULT.TERM}</option>`;

    const academicContext = window.AcademicContext.getContext();
    const allTerms = academicContext.allTerms || [];
    const termsForYear = allTerms.filter(term => term.academic_year_id == selectedYear);

    if (Array.isArray(termsForYear) && termsForYear.length > 0) {
      termsForYear.forEach(term => {
        const option = document.createElement('option');
        option.value = term.id;
        option.textContent = term.name;
        termSelect.appendChild(option);
      });
    } else {
      termSelect.innerHTML = '<option value="">No terms available</option>';
    }

    // Reset subsequent dropdowns
    this.resetLevelAndBelow();
  },

  // Update level dropdown when term is selected
  updateLevelDropdown() {
    const termSelect = document.getElementById('ca_term');
    const levelSelect = document.getElementById('ca_level');

    if (!termSelect || !levelSelect) return;

    const selectedTerm = termSelect.value;

    if (!selectedTerm) {
      // Reset and disable subsequent dropdowns
      levelSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.EDUCATION_LEVEL}</option>`;
      levelSelect.disabled = true;
      this.resetClassAndBelow();
      return;
    }

    // Enable level dropdown and populate with database education levels
    levelSelect.disabled = false;
    levelSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DEFAULT.EDUCATION_LEVEL}</option>`;

    // Get education levels from loaded data
    const educationLevels = AssessmentManagementComponents.state.educationLevels?.data || [];
    if (Array.isArray(educationLevels) && educationLevels.length > 0) {
      educationLevels.forEach(level => {
        const option = document.createElement('option');
        option.value = level.code;
        option.textContent = level.name;
        levelSelect.appendChild(option);
      });
    }

    // Reset subsequent dropdowns
    this.resetClassAndBelow();
  },

  // Update class and subject dropdowns when level is selected
  updateClassAndSubjectDropdowns() {
    const levelSelect = document.getElementById('ca_level');
    const classSelect = document.getElementById('ca_class');
    const subjectSelect = document.getElementById('ca_subject');

    if (!levelSelect || !classSelect || !subjectSelect) return;

    const selectedLevel = levelSelect.value;

    if (!selectedLevel) {
      // Reset and disable subsequent dropdowns
      this.resetClassAndBelow();
      return;
    }

    // Enable and populate classes filtered by level
    classSelect.disabled = false;
    classSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DEFAULT.CLASS}</option>`;

    const classes = AssessmentManagementComponents.state.classes.data || AssessmentManagementComponents.state.classes;
    if (Array.isArray(classes)) {
      // Filter classes by education level
      const filteredClasses = classes.filter(classItem => {
        // Assuming classes have education_level_code property
        if (selectedLevel === 'o_level') {
          return classItem.education_level_code === 'o_level';
        } else if (selectedLevel === 'a_level') {
          return classItem.education_level_code === 'a_level';
        }
        return false;
      });

      filteredClasses.forEach(classItem => {
        const option = document.createElement('option');
        option.value = classItem.id;
        option.textContent = classItem.name;
        classSelect.appendChild(option);
      });
    }

    // Reset subject dropdown - it will be enabled when class is selected
    if (subjectSelect) {
      subjectSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.SUBJECT}</option>`;
      subjectSelect.disabled = true;
    }

    // Reset CA Number dropdown
    this.resetCANumberDropdown();
  },

  // Update subjects dropdown based on selected level
  updateSubjectsDropdown() {
    const levelSelect = document.getElementById('ca_level');
    const subjectSelect = document.getElementById('ca_subject');

    if (!levelSelect || !subjectSelect) return;

    const selectedLevel = levelSelect.value;

    // Clear existing options
    subjectSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DEFAULT.SUBJECT}</option>`;

    if (!selectedLevel) {
      subjectSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.SUBJECT}</option>`;
      subjectSelect.disabled = true;
      return;
    }

    // Get subjects based on selected level from the proper database tables
    let subjects = [];
    if (selectedLevel === 'o_level') {
      // Get O-Level subjects from o_level_subjects table
      const oLevelSubjects = AssessmentManagementComponents.state.oLevelSubjects;
      subjects = oLevelSubjects?.data || oLevelSubjects || [];
    } else if (selectedLevel === 'a_level') {
      // Get A-Level subjects from a_level_subjects table
      const aLevelSubjects = AssessmentManagementComponents.state.aLevelSubjects;
      subjects = aLevelSubjects?.data || aLevelSubjects || [];
    }

    if (Array.isArray(subjects) && subjects.length > 0) {
      subjects.forEach(subject => {
        const option = document.createElement('option');
        option.value = subject.id;
        option.textContent = subject.name;
        option.dataset.level = selectedLevel;
        option.dataset.subjectType = subject.subject_type || '';
        subjectSelect.appendChild(option);
      });
    } else {
      subjectSelect.innerHTML = '<option value="">No subjects available</option>';
    }
  },

  // Reset methods for cascading dropdowns
  resetLevelAndBelow() {
    const levelSelect = document.getElementById('ca_level');
    if (levelSelect) {
      levelSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.EDUCATION_LEVEL}</option>`;
      levelSelect.disabled = true;
    }
    this.resetClassAndBelow();
  },

  resetClassAndBelow() {
    const classSelect = document.getElementById('ca_class');
    const subjectSelect = document.getElementById('ca_subject');

    if (classSelect) {
      classSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.CLASS}</option>`;
      classSelect.disabled = true;
    }

    if (subjectSelect) {
      subjectSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.SUBJECT}</option>`;
      subjectSelect.disabled = true;
    }

    this.resetCANumberDropdown();
  },

  // Handle class selection change
  handleClassChange() {
    const classSelect = document.getElementById('ca_class');
    const subjectSelect = document.getElementById('ca_subject');

    if (!classSelect) return;

    const selectedClass = classSelect.value;

    if (!selectedClass) {
      // If class is unselected, disable and reset subject and CA number
      if (subjectSelect) {
        subjectSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.SUBJECT}</option>`;
        subjectSelect.disabled = true;
      }
      this.resetCANumberDropdown();
    } else {
      // If class is selected, enable subject dropdown and update it
      if (subjectSelect) {
        subjectSelect.disabled = false;
      }
      this.updateSubjectsDropdown();
      // Still reset CA Number until subject is selected
      this.resetCANumberDropdown();
    }
  },

  resetCANumberDropdown() {
    const caNumberSelect = document.getElementById('ca_number');
    if (caNumberSelect) {
      caNumberSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.CA_NUMBER}</option>`;
      caNumberSelect.disabled = true;
    }
  },

  // Update CA Number dropdown based on selected class and subject
  async updateCANumberDropdown() {
    const classId = document.getElementById('ca_class')?.value;
    const subjectId = document.getElementById('ca_subject')?.value;
    const academicYearId = document.getElementById('ca_academic_year')?.value;
    const termId = document.getElementById('ca_term')?.value;
    const level = document.getElementById('ca_level')?.value;
    const caNumberSelect = document.getElementById('ca_number');

    if (!caNumberSelect) return;

    // First, handle paper selection for A-Level subjects
    if (level === 'a_level' && subjectId) {
      await this.handleALevelPaperSelection(subjectId);
    } else {
      this.hidePaperSelection();
    }

    // If not all required fields are selected, keep disabled
    if (!classId || !subjectId || !academicYearId || !termId) {
      caNumberSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.CA_NUMBER}</option>`;
      caNumberSelect.disabled = true;
      return;
    }

    // For A-Level, also check if paper is selected
    if (level === 'a_level') {
      const paperId = document.getElementById('ca_paper')?.value;
      if (!paperId) {
        caNumberSelect.innerHTML = `<option value="">Select Paper First</option>`;
        caNumberSelect.disabled = true;
        return;
      }
    }

    // Enable and clear existing options
    caNumberSelect.disabled = false;
    caNumberSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DEFAULT.CA_NUMBER}</option>`;

    try {
      // Get CA configuration for this class-subject combination
      const caConfigResult = await window.CAConfigurationAPI.getAll({
        class_id: classId,
        subject_id: subjectId,
        academic_year_id: academicYearId,
        term_id: termId
      });

      if (caConfigResult.success && caConfigResult.data && caConfigResult.data.length > 0) {
        const config = caConfigResult.data[0];
        const totalCAs = config.total_cas || 0;

        // Populate CA Number dropdown based on configuration
        for (let i = 1; i <= totalCAs; i++) {
          const option = document.createElement('option');
          option.value = i;
          option.textContent = `CA ${i}`;
          caNumberSelect.appendChild(option);
        }
      } else {
        // No configuration found, show message
        const option = document.createElement('option');
        option.value = '';
        option.textContent = 'No CA configuration found for this class-subject';
        option.disabled = true;
        caNumberSelect.appendChild(option);
      }
    } catch (error) {
      const option = document.createElement('option');
      option.value = '';
      option.textContent = 'Error loading CA configuration';
      option.disabled = true;
      caNumberSelect.appendChild(option);
    }
  },

  // Handle A-Level paper selection
  async handleALevelPaperSelection(subjectId) {
    const paperContainer = document.getElementById('ca_paper_container');
    const paperSelect = document.getElementById('ca_paper');
    if (!paperContainer || !paperSelect) return;

    // Show paper dropdown container
    paperContainer.style.display = 'block';
    paperSelect.disabled = false;
    paperSelect.innerHTML = '<option value="">Loading papers...</option>';

    try {
      // Get papers for this subject
      const response = await window.SubjectsAPI.aLevel.getPapers(subjectId);

      if (response.success && response.data) {
        paperSelect.innerHTML = '<option value="">Select Paper</option>';

        response.data.forEach(paper => {
          const option = document.createElement('option');
          option.value = paper.id;
          option.textContent = paper.paper_name || `Paper ${paper.paper_number}`;
          option.dataset.paperNumber = paper.paper_number;
          paperSelect.appendChild(option);
        });

        // Add event listener for paper change
        paperSelect.addEventListener('change', () => {
          this.updateCANumberDropdown();
        });

        console.log(`📄 Loaded ${response.data.length} papers for subject ${subjectId}`);
      } else {
        paperSelect.innerHTML = '<option value="">No papers available</option>';
        console.error('Failed to load papers:', response.message);
      }
    } catch (error) {
      paperSelect.innerHTML = '<option value="">Error loading papers</option>';
      console.error('Error loading papers:', error);
    }
  },

  // Hide paper selection for O-Level subjects
  hidePaperSelection() {
    const paperContainer = document.getElementById('ca_paper_container');
    const paperSelect = document.getElementById('ca_paper');
    if (paperContainer && paperSelect) {
      paperContainer.style.display = 'none';
      paperSelect.disabled = true;
      paperSelect.innerHTML = '<option value="">Select Subject First</option>';
    }
  },

  // Render academic context error
  renderAcademicContextError() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader('Enter CA Scores', 'Input continuous assessment scores')}

        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-600')}
            </div>
            <div class="ml-4">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-red-900">Academic Context Required</h3>
              <p class="text-red-700 mt-2">
                You cannot enter CA scores without setting up the academic year and terms first.
              </p>
              <p class="text-red-700 mt-1">
                Please go to <strong>Academic Year → Setup</strong> to configure your academic year and terms before entering CA scores.
              </p>
              <div class="mt-4 text-center">
                ${SRDesignSystem.forms.button('setup-academic-ca-scores', 'Setup Academic Year & Terms', 'danger', {
                  icon: 'fas fa-calendar-plus',
                  onclick: 'EnterCAScoresComponent.navigateToAcademicSetup()'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Navigate to academic year setup
  navigateToAcademicSetup() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('academic-year-setup');
    }
  }
};

// Enter Exam  MarkComponent
const EnterExamMarksComponent = {
  // Component state
  state: {
    selectedExam: null,
    students: [],
    marks: {}
  },

  // Render enter exam marks interface
  render() {
    // Check if academic context is available
    const hasAcademicContext = window.AcademicContext &&
                              window.AcademicContext.getActiveAcademicYear() &&
                              window.AcademicContext.getActiveTerm();

    if (!hasAcademicContext) {
      return this.renderAcademicContextError();
    }

    // Show loading state if data is still loading
    if (AssessmentManagementComponents.state.loading) {
      // Use centralized loading state from page router
      const container = document.getElementById('content-area');
      if (container && window.PageRouter) {
        window.PageRouter.showLoadingState(container, 'Loading exam data...');
      }
      return '';
    }

    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Enter Exam Marks',
          'Input term examination marks'
        )}

        <!-- Exam Selection -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-4 border-b border-primary-200">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-primary-900 flex items-center">
              ${SRDesignSystem.components.icon('fas fa-filter', 'base', 'primary-600')}
              <span class="ml-3">Select Parameters</span>
            </h3>
          </div>

          <div class="p-6">

          <!-- First Row: Academic Context -->
          <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap} mb-4">
            ${SRDesignSystem.forms.select('exam_academic_year', 'Academic Year', [], '')}
            ${SRDesignSystem.forms.select('exam_term', 'Term', [], '')}
            ${SRDesignSystem.forms.select('exam_level', 'Education Level', [
              { value: '', label: AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DEFAULT.EDUCATION_LEVEL },
              { value: 'o_level', label: 'O-Level' },
              { value: 'a_level', label: 'A-Level' }
            ], '')}
          </div>

          <!-- Second Row: Class and Subject -->
          <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap} mb-4">
            ${SRDesignSystem.forms.select('exam_class', 'Class', [], '')}
            ${SRDesignSystem.forms.select('exam_subject', 'Subject', [], '')}
          </div>

          <!-- Third Row: Paper and Exam Type -->
          <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gap}">
            <div id="exam_paper_container" style="display: none;">
              ${SRDesignSystem.forms.select('exam_paper', 'Paper', [], '', { id: 'exam_paper' })}
            </div>
            ${SRDesignSystem.forms.select('exam_type', 'Exam Type', [], '')}
          </div>

            <div class="mt-6 flex justify-end">
              ${SRDesignSystem.forms.button('load-exam-students', 'Load Students', 'primary', {
                onclick: 'EnterExamMarksComponent.loadStudents()',
                  icon: 'fas fa-search'
              })}
            </div>
          </div>
        </div>

        <!-- Marks Entry -->
        <div id="marks-entry-section" class="hidden bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-blue-50 to-indigo-100 px-6 py-4 border-b border-blue-200">
            <div class="flex items-center justify-between">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-blue-900 flex items-center">
                ${SRDesignSystem.components.icon('fas fa-pencil-alt', 'base', 'blue-600')}
                <span class="ml-3">Enter Exam Marks</span>
              </h3>
              <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-600">Marks are saved automatically</span>
              </div>
            </div>
          </div>
          <div class="overflow-x-auto max-h-96 overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="px-6 py-3 text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Student Name</th>
                  <th class="px-6 py-3 text-center ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Exam Mark</th>
                  <th class="px-6 py-3 text-center ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Status</th>
                </tr>
              </thead>
              <tbody id="marks-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Students and grades will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  },



  // Load students for selected exam
  async loadStudents() {
    const academicYear = document.getElementById('exam_academic_year').value;
    const term = document.getElementById('exam_term').value;
    const level = document.getElementById('exam_level').value;
    const classId = document.getElementById('exam_class').value;
    const subject = document.getElementById('exam_subject').value;
    const examType = document.getElementById('exam_type').value;

    if (!academicYear || !term || !level || !classId || !subject || !examType) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Please select all required fields', 'error');
      }
      return;
    }

    // For A-Level, also get paper selection
    let paperId = null;
    if (level === 'a_level') {
      paperId = document.getElementById('exam_paper').value;
      if (!paperId) {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Please select a paper for A-Level subjects', 'error');
        }
        return;
      }
    }

    try {
      // Set up the exam context
      this.state.selectedExam = {
        subject_id: subject,
        academic_year_id: academicYear,
        term_id: term,
        class_id: classId,
        exam_type_id: examType,
        level: level,
        subject_paper_id: paperId // For A-Level subjects
      };

      // Load students with existing exam marks using ExamMarksAPI
      const studentsResult = await window.ExamMarksAPI.getStudents({
        class_id: classId,
        subject_id: subject,
        exam_type_id: examType,
        academic_year_id: academicYear,
        term_id: term
      });

      if (studentsResult.success && studentsResult.data && studentsResult.data.length > 0) {
        // Map students for exam marks entry
        this.state.students = studentsResult.data.map(student => ({
          id: student.id,
          admission_number: student.admission_number,
          first_name: student.first_name,
          last_name: student.last_name,
          photo_url: student.photo_url,
          class_name: student.class_name,
          stream_name: student.stream_name,
          current_exam_mark: student.exam_mark || null // Load existing exam marks
        }));

        this.renderMarksTable();
        document.getElementById('marks-entry-section').classList.remove('hidden');
      } else {
        // Handle empty state - no students found
        this.state.students = [];
        this.renderEmptyExamState();
        document.getElementById('marks-entry-section').classList.remove('hidden');
      }
    } catch (error) {
      console.error('Error loading students for exam marks:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show(`Failed to load students: ${error.message}`, 'error');
      }
    }
  },



  // Render marks table
  renderMarksTable() {
    const tbody = document.getElementById('marks-table-body');
    if (!tbody) return;

    // Check if students array is empty
    if (!this.state.students || this.state.students.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="3" class="px-6 py-12 text-center text-gray-500">
            <div class="text-gray-400 mb-4">
              ${SRDesignSystem.components.icon('fas fa-users', '4xl', 'gray-300')}
            </div>
            <p class="${SRDesignSystem.responsive.text.lg} font-medium">No students found</p>
            <p class="${SRDesignSystem.responsive.text.sm}">Select exam details to view students for grading.</p>
          </td>
        </tr>
      `;
      return;
    }

    tbody.innerHTML = this.state.students.map(student => `
      <tr>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-900">${student.first_name} ${student.last_name}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <input type="number"
                 id="exam_mark_${student.id}"
                 min="0"
                 max="100"
                 step="1"
                 value="${student.current_exam_mark || ''}"
                 class="w-20 px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                 onchange="EnterExamMarksComponent.updateExamMark(${student.id}, this.value)"
                 title="Enter exam mark (0-100)">
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <span id="status_${student.id}" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            student.current_exam_mark !== null && student.current_exam_mark !== '' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          }">
            ${student.current_exam_mark !== null && student.current_exam_mark !== '' ? 'Entered' : 'Pending'}
          </span>
        </td>
      </tr>
    `).join('');
  },

  // Update exam mark for student
  updateExamMark(studentId, examMark) {
    if (!this.state.marks[studentId]) {
      this.state.marks[studentId] = {};
    }
    this.state.marks[studentId].exam_mark = examMark ? parseFloat(examMark) : null;

    // Update status badge and auto-save
    const statusElement = document.getElementById(`status_${studentId}`);
    if (statusElement) {
      const hasValue = examMark && examMark.trim() !== '';
      if (hasValue) {
        statusElement.textContent = 'Saving...';
        statusElement.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800';

        // Auto-save the mark
        this.autoSaveMark(studentId);
      } else {
        statusElement.textContent = 'Pending';
        statusElement.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800';
      }
    }
  },

  // Auto-save individual exam mark
  async autoSaveMark(studentId) {
    if (!this.state.selectedExam || !this.state.marks[studentId]) {
      return;
    }

    try {
      // First, create or get the examination record following schema structure
      const examData = {
        class_id: this.state.selectedExam.class_id,
        academic_year_id: this.state.selectedExam.academic_year_id,
        term_id: this.state.selectedExam.term_id,
        exam_type_id: this.state.selectedExam.exam_type_id,
        subject_level: this.state.selectedExam.level
      };

      // O-Level: subject-based (o_level_term_examinations table)
      // A-Level: subject paper-based (a_level_paper_examinations table)
      if (this.state.selectedExam.level === 'o_level') {
        examData.subject_id = this.state.selectedExam.subject_id;
      } else {
        examData.subject_paper_id = this.state.selectedExam.subject_paper_id;
      }

      const examResult = await window.AcademicAPI.termExaminations.create(examData);

      if (!examResult.success) {
        throw new Error(examResult.message || 'Failed to create examination record');
      }

      const examinationId = examResult.data.id;

      // Prepare mark data - both levels use same structure with examination_id
      // O-Level marks: o_level_student_exam_marks table
      // A-Level marks: a_level_student_paper_exam_marks table
      const markData = {
        student_id: parseInt(studentId),
        examination_id: examinationId,
        marks_obtained: parseInt(this.state.marks[studentId].exam_mark)
      };

      // Save the mark using the exam marks API
      const result = await window.ExamMarksAPI.create([markData]);

      const statusElement = document.getElementById(`status_${studentId}`);
      if (statusElement) {
        if (result.success) {
          statusElement.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800';
          statusElement.textContent = 'Entered';
        } else {
          statusElement.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800';
          statusElement.textContent = 'Error';
          console.error('Failed to save exam mark:', result.message);
        }
      }
    } catch (error) {
      console.error('Error auto-saving exam mark:', error);
      const statusElement = document.getElementById(`status_${studentId}`);
      if (statusElement) {
        statusElement.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800';
        statusElement.textContent = 'Error';
      }
    }
  },

  // Calculate grade based on final score using database boundaries
  calculateGrade(finalScore, subjectLevel = 'o_level', isSubsidiary = false) {
    if (finalScore === null || finalScore === undefined) return 'N/A';

    // Use the grade boundaries component for accurate calculation
    if (window.GradeBoundariesComponents && window.GradeBoundariesComponents.getGradeLetterForPercentage) {
      if (subjectLevel === 'a_level') {
        return window.GradeBoundariesComponents.getGradeLetterForPercentage(finalScore, isSubsidiary);
      } else {
        // For O-Level, use the O-Level boundaries method
        return window.GradeBoundariesComponents.getOLevelGradeLetterForPercentage(finalScore);
      }
    }

    return 'N/A';
  },

  // Get grade badge CSS class
  getGradeBadgeClass(grade) {
    const classes = {
      'A': 'bg-green-100 text-green-800',
      'B': 'bg-blue-100 text-blue-800',
      'C': 'bg-yellow-100 text-yellow-800',
      'D': 'bg-orange-100 text-orange-800',
      'E': 'bg-red-100 text-red-800'
    };
    return classes[grade] || 'bg-gray-100 text-gray-800';
  },





  // Initialize component
  async init() {
    this.resetComponentState();

    await window.AcademicContext.initialize();
    const activeYear = window.AcademicContext.getActiveAcademicYear();
    const activeTerm = window.AcademicContext.getActiveTerm();

    if (!activeYear || !activeTerm) return;

    const academicYearsLength = AssessmentManagementComponents.state.academicYears?.data?.length || 0;
    const termsLength = AssessmentManagementComponents.state.terms?.data?.length || 0;
    const educationLevelsLength = AssessmentManagementComponents.state.educationLevels?.data?.length || 0;

    if (academicYearsLength === 0 || termsLength === 0 || educationLevelsLength === 0) {
      await AssessmentManagementComponents.loadInitialData();
    }

    // Initialize directly - DOM should be ready due to lifecycle manager
    this.populateDropdowns();
    await this.setDefaultAcademicContext();
    this.initializeEventListeners();
  },

  // Reset component state
  resetComponentState() {
    this.state.selectedExam = null;
    this.state.students = [];
    this.state.marks = {};
  },

  // Cleanup component
  cleanup() {
    this.resetComponentState();

    // Hide any sections that might be visible
    const marksEntry = document.getElementById('marks-entry-section');
    if (marksEntry) marksEntry.classList.add('hidden');

    // Reset any form elements
    const form = document.querySelector('#exam-marks-form');
    if (form) form.reset();

    // Clear any notifications or modals
    const modals = ['exam-details-modal'];
    modals.forEach(modalId => {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.classList.add('hidden');
      }
    });
  },

  // Populate dropdowns with data
  populateDropdowns() {
    const academicYearSelect = document.getElementById('exam_academic_year');
    if (academicYearSelect) {
      academicYearSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DEFAULT.ACADEMIC_YEAR}</option>`;

      const academicContext = window.AcademicContext.getContext();
      const academicYears = academicContext.allAcademicYears || [];

      if (Array.isArray(academicYears) && academicYears.length > 0) {
        academicYears.forEach(year => {
          const option = document.createElement('option');
          option.value = year.id;
          option.textContent = year.name;
          academicYearSelect.appendChild(option);
        });
      }
    }

    this.initializeDisabledDropdowns();
  },

  // Initialize dropdowns in disabled state
  initializeDisabledDropdowns() {
    // Terms dropdown - disabled until academic year is selected
    const termSelect = document.getElementById('exam_term');
    if (termSelect) {
      termSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.TERM}</option>`;
      termSelect.disabled = true;
    }

    // Level dropdown - disabled until term is selected
    const levelSelect = document.getElementById('exam_level');
    if (levelSelect) {
      levelSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.EDUCATION_LEVEL}</option>`;
      levelSelect.disabled = true;
    }

    // Class dropdown - disabled until level is selected
    const classSelect = document.getElementById('exam_class');
    if (classSelect) {
      classSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.CLASS}</option>`;
      classSelect.disabled = true;
    }

    // Subject dropdown - disabled until level is selected
    const subjectSelect = document.getElementById('exam_subject');
    if (subjectSelect) {
      subjectSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.SUBJECT}</option>`;
      subjectSelect.disabled = true;
    }

    // Exam Type dropdown - disabled until class and subject are selected
    const examTypeSelect = document.getElementById('exam_type');
    if (examTypeSelect) {
      examTypeSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.EXAM_TYPE}</option>`;
      examTypeSelect.disabled = true;
    }
  },

  // Set default academic context in dropdowns
  async setDefaultAcademicContext() {
    // Use the global academic context service
    const activeYear = window.AcademicContext.getActiveAcademicYear();
    const activeTerm = window.AcademicContext.getActiveTerm();

    if (activeYear) {
      const academicYearSelect = document.getElementById('exam_academic_year');
      if (academicYearSelect) {
        academicYearSelect.value = activeYear.id;
        this.updateTermsDropdown();

        if (activeTerm) {
          // Set term value directly - DOM should be ready due to lifecycle manager
          const termSelect = document.getElementById('exam_term');
          if (termSelect) {
            termSelect.value = activeTerm.id;
            this.updateLevelDropdown();
          }
        }
      }
    }
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Academic Year change - enables Term dropdown
    const academicYearSelect = document.getElementById('exam_academic_year');
    if (academicYearSelect) {
      academicYearSelect.addEventListener('change', () => {
        this.updateTermsDropdown();
      });
    }

    // Term change - enables Level dropdown
    const termSelect = document.getElementById('exam_term');
    if (termSelect) {
      termSelect.addEventListener('change', () => {
        this.updateLevelDropdown();
      });
    }

    // Level change - enables Class and Subject dropdowns
    const levelSelect = document.getElementById('exam_level');
    if (levelSelect) {
      levelSelect.addEventListener('change', () => {
        this.updateClassAndSubjectDropdowns();
        // Hide paper when education level is unselected
        if (!levelSelect.value) {
          this.hidePaperSelection();
        }
      });
    }

    // Class change - handles Subject and Exam Type cascading
    const classSelect = document.getElementById('exam_class');
    if (classSelect) {
      classSelect.addEventListener('change', () => {
        this.handleClassChange();
        // Hide paper when class is unselected
        if (!classSelect.value) {
          this.hidePaperSelection();
        }
      });
    }

    // Subject change - handles paper selection and updates Exam Type dropdown
    const subjectSelect = document.getElementById('exam_subject');
    if (subjectSelect) {
      subjectSelect.addEventListener('change', () => {
        this.handleSubjectChange();
        // Hide paper when subject is unselected
        if (!subjectSelect.value) {
          this.hidePaperSelection();
        }
      });
    }


  },

  // Handle subject selection change
  handleSubjectChange() {
    const subjectSelect = document.getElementById('exam_subject');
    const levelSelect = document.getElementById('exam_level');

    if (!subjectSelect || !levelSelect) return;

    const selectedSubject = subjectSelect.value;
    const selectedLevel = levelSelect.value;

    if (selectedSubject && selectedLevel === 'a_level') {
      // Check if this is an A-level subject and show paper selection
      const selectedOption = subjectSelect.options[subjectSelect.selectedIndex];
      const level = selectedOption.dataset.level;

      if (level === 'a_level') {
        this.showExamPaperSelection(selectedSubject);
      } else {
        this.hideExamPaperSelection();
      }
    } else {
      this.hideExamPaperSelection();
    }

    // Always update exam type dropdown
    this.updateExamTypeDropdown();
  },

  // Show paper selection for A-level subjects
  async showExamPaperSelection(subjectId) {
    try {
      const paperContainer = document.getElementById('exam_paper_container');
      const paperSelect = document.getElementById('exam_paper');
      if (!paperContainer || !paperSelect) return;

      // Show paper dropdown container
      paperContainer.style.display = 'block';
      paperSelect.disabled = false;
      paperSelect.innerHTML = '<option value="">Loading papers...</option>';

      // Get papers for this subject
      const response = await window.SubjectsAPI.aLevel.getPapers(subjectId);

      if (response.success && response.data) {
        paperSelect.innerHTML = '<option value="">Select Paper</option>';

        response.data.forEach(paper => {
          const option = document.createElement('option');
          option.value = paper.id;
          option.textContent = paper.paper_name || `Paper ${paper.paper_number}`;
          option.dataset.paperNumber = paper.paper_number;
          paperSelect.appendChild(option);
        });

        console.log(`📄 Loaded ${response.data.length} papers for subject ${subjectId}`);
      } else {
        paperSelect.innerHTML = '<option value="">No papers available</option>';
        console.error('Failed to load papers:', response.message);
      }
    } catch (error) {
      const paperSelect = document.getElementById('exam_paper');
      if (paperSelect) {
        paperSelect.innerHTML = '<option value="">Error loading papers</option>';
      }
      console.error('Error loading papers:', error);
    }
  },

  // Hide paper selection for O-Level subjects
  hideExamPaperSelection() {
    const paperContainer = document.getElementById('exam_paper_container');
    const paperSelect = document.getElementById('exam_paper');
    if (paperContainer && paperSelect) {
      paperContainer.style.display = 'none';
      paperSelect.disabled = true;
      paperSelect.innerHTML = '<option value="">Select Subject First</option>';
    }
  },

  // Update terms dropdown when academic year is selected
  updateTermsDropdown() {
    const academicYearSelect = document.getElementById('exam_academic_year');
    const termSelect = document.getElementById('exam_term');

    if (!academicYearSelect || !termSelect) return;

    const selectedYear = academicYearSelect.value;

    if (!selectedYear) {
      // Reset and disable subsequent dropdowns
      termSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.TERM}</option>`;
      termSelect.disabled = true;
      this.resetLevelAndBelow();
      return;
    }

    // Enable and populate terms for the selected academic year
    termSelect.disabled = false;
    termSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DEFAULT.TERM}</option>`;

    // Get all terms from academic context and filter by selected academic year
    const academicContext = window.AcademicContext.getContext();
    const allTerms = academicContext.allTerms || [];
    const termsForYear = allTerms.filter(term => term.academic_year_id == selectedYear);

    if (Array.isArray(termsForYear) && termsForYear.length > 0) {
      termsForYear.forEach(term => {
        const option = document.createElement('option');
        option.value = term.id;
        option.textContent = term.name;
        termSelect.appendChild(option);
      });
    }

    // Reset subsequent dropdowns
    this.resetLevelAndBelow();
  },

  // Update level dropdown when term is selected
  updateLevelDropdown() {
    const termSelect = document.getElementById('exam_term');
    const levelSelect = document.getElementById('exam_level');

    if (!termSelect || !levelSelect) return;

    const selectedTerm = termSelect.value;

    if (!selectedTerm) {
      // Reset and disable subsequent dropdowns
      levelSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.EDUCATION_LEVEL}</option>`;
      levelSelect.disabled = true;
      this.resetClassAndBelow();
      return;
    }

    // Enable level dropdown and populate with database education levels
    levelSelect.disabled = false;
    levelSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DEFAULT.EDUCATION_LEVEL}</option>`;

    // Get education levels from loaded data
    const educationLevels = AssessmentManagementComponents.state.educationLevels?.data || [];
    if (Array.isArray(educationLevels) && educationLevels.length > 0) {
      educationLevels.forEach(level => {
        const option = document.createElement('option');
        option.value = level.code;
        option.textContent = level.name;
        levelSelect.appendChild(option);
      });
    }

    // Reset subsequent dropdowns
    this.resetClassAndBelow();
  },

  // Update class and subject dropdowns when level is selected
  updateClassAndSubjectDropdowns() {
    const levelSelect = document.getElementById('exam_level');
    const classSelect = document.getElementById('exam_class');
    const subjectSelect = document.getElementById('exam_subject');

    if (!levelSelect || !classSelect || !subjectSelect) return;

    const selectedLevel = levelSelect.value;

    if (!selectedLevel) {
      // Reset and disable subsequent dropdowns
      this.resetClassAndBelow();
      return;
    }

    // Enable and populate classes filtered by level
    classSelect.disabled = false;
    classSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DEFAULT.CLASS}</option>`;

    const classes = AssessmentManagementComponents.state.classes.data || AssessmentManagementComponents.state.classes;
    if (Array.isArray(classes)) {
      // Filter classes by education level
      const filteredClasses = classes.filter(classItem => {
        if (selectedLevel === 'o_level') {
          return classItem.education_level_code === 'o_level';
        } else if (selectedLevel === 'a_level') {
          return classItem.education_level_code === 'a_level';
        }
        return false;
      });

      filteredClasses.forEach(classItem => {
        const option = document.createElement('option');
        option.value = classItem.id;
        option.textContent = classItem.name;
        classSelect.appendChild(option);
      });
    }

    // Reset subject dropdown - it will be enabled when class is selected
    if (subjectSelect) {
      subjectSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.SUBJECT}</option>`;
      subjectSelect.disabled = true;
    }

    // Reset Exam Type dropdown
    this.resetExamTypeDropdown();
  },

  // Handle class selection change
  handleClassChange() {
    const classSelect = document.getElementById('exam_class');
    const subjectSelect = document.getElementById('exam_subject');

    if (!classSelect) return;

    const selectedClass = classSelect.value;

    if (!selectedClass) {
      // If class is unselected, disable and reset subject and exam type
      if (subjectSelect) {
        subjectSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.SUBJECT}</option>`;
        subjectSelect.disabled = true;
      }
      this.resetExamTypeDropdown();
    } else {
      // If class is selected, enable subject dropdown and update it
      if (subjectSelect) {
        subjectSelect.disabled = false;
      }
      this.updateSubjectsDropdown();
      // Still reset Exam Type until subject is selected
      this.resetExamTypeDropdown();
    }
  },

  // Update subjects dropdown based on selected level
  updateSubjectsDropdown() {
    const levelSelect = document.getElementById('exam_level');
    const subjectSelect = document.getElementById('exam_subject');

    if (!levelSelect || !subjectSelect) return;

    const selectedLevel = levelSelect.value;

    // Clear existing options
    subjectSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DEFAULT.SUBJECT}</option>`;

    if (!selectedLevel) {
      subjectSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.SUBJECT}</option>`;
      subjectSelect.disabled = true;
      return;
    }

    // Get subjects based on selected level from the proper database tables
    let subjects = [];
    if (selectedLevel === 'o_level') {
      // Get O-Level subjects from o_level_subjects table
      const oLevelSubjects = AssessmentManagementComponents.state.oLevelSubjects;
      subjects = oLevelSubjects?.data || oLevelSubjects || [];
    } else if (selectedLevel === 'a_level') {
      // Get A-Level subjects from a_level_subjects table
      const aLevelSubjects = AssessmentManagementComponents.state.aLevelSubjects;
      subjects = aLevelSubjects?.data || aLevelSubjects || [];
    }

    // Populate subjects for the selected level
    if (Array.isArray(subjects) && subjects.length > 0) {
      subjects.forEach(subject => {
        const option = document.createElement('option');
        option.value = subject.id;
        option.textContent = subject.name;
        option.dataset.level = selectedLevel;
        option.dataset.subjectType = subject.subject_type || '';
        subjectSelect.appendChild(option);
      });
    }
  },

  // Update Exam Type dropdown based on selected class and subject
  async updateExamTypeDropdown() {
    const classId = document.getElementById('exam_class')?.value;
    const subjectId = document.getElementById('exam_subject')?.value;
    const academicYearId = document.getElementById('exam_academic_year')?.value;
    const termId = document.getElementById('exam_term')?.value;
    const examTypeSelect = document.getElementById('exam_type');

    if (!examTypeSelect) return;

    // If not all required fields are selected, keep disabled
    if (!classId || !subjectId || !academicYearId || !termId) {
      examTypeSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.EXAM_TYPE}</option>`;
      examTypeSelect.disabled = true;
      return;
    }

    // Enable and clear existing options
    examTypeSelect.disabled = false;
    examTypeSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DEFAULT.EXAM_TYPE}</option>`;

    try {
      // Get configured exam types for this class
      const examTypesResult = await window.ClassExamTypesAPI.getAll({
        class_id: classId,
        academic_year_id: academicYearId,
        term_id: termId
      });

      if (examTypesResult.success && examTypesResult.data && examTypesResult.data.length > 0) {
        // Populate Exam Type dropdown with configured exam types
        examTypesResult.data.forEach(examType => {
          const option = document.createElement('option');
          option.value = examType.exam_type_id;
          option.textContent = `${examType.exam_type_name} (${examType.weight_percentage}%)`;
          option.dataset.weight = examType.weight_percentage;
          examTypeSelect.appendChild(option);
        });
      } else {
        // No exam types configured for this class
        const option = document.createElement('option');
        option.value = '';
        option.textContent = 'No exam types configured for this class';
        option.disabled = true;
        examTypeSelect.appendChild(option);
      }
    } catch (error) {
      const option = document.createElement('option');
      option.value = '';
      option.textContent = 'Error loading exam types';
      option.disabled = true;
      examTypeSelect.appendChild(option);
    }
  },

  // Reset methods for cascading dropdowns
  resetLevelAndBelow() {
    const levelSelect = document.getElementById('exam_level');
    if (levelSelect) {
      levelSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.EDUCATION_LEVEL}</option>`;
      levelSelect.disabled = true;
    }
    this.resetClassAndBelow();
  },

  resetClassAndBelow() {
    const classSelect = document.getElementById('exam_class');
    const subjectSelect = document.getElementById('exam_subject');

    if (classSelect) {
      classSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.CLASS}</option>`;
      classSelect.disabled = true;
    }

    if (subjectSelect) {
      subjectSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.SUBJECT}</option>`;
      subjectSelect.disabled = true;
    }

    this.resetExamTypeDropdown();
  },

  resetExamTypeDropdown() {
    const examTypeSelect = document.getElementById('exam_type');
    if (examTypeSelect) {
      examTypeSelect.innerHTML = `<option value="">${AssessmentManagementComponents.DROPDOWN_PLACEHOLDERS.DISABLED.EXAM_TYPE}</option>`;
      examTypeSelect.disabled = true;
    }
  },

  // Render empty state for exam marks
  renderEmptyExamState() {
    const container = document.getElementById('marks-table-body');
    if (!container) return;

    container.innerHTML = `
      <tr>
        <td colspan="3" class="px-6 py-12 text-center">
          <div class="text-gray-500">
            <i class="fas fa-users text-4xl mb-4"></i>
            <p class="text-lg font-medium">No students found</p>
            <p class="text-sm">No students are enrolled in this class for the selected term.</p>
          </div>
        </td>
      </tr>
    `;
  },



  // Navigate to academic year setup
  navigateToAcademicSetup() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('academic-year-setup');
    } else {
      window.location.href = '#academic-year-setup';
    }
  },

  // Hide paper selection for O-Level subjects
  hidePaperSelection() {
    const paperContainer = document.getElementById('exam_paper_container');
    const paperSelect = document.getElementById('exam_paper');
    if (paperContainer && paperSelect) {
      paperContainer.style.display = 'none';
      paperSelect.disabled = true;
      paperSelect.innerHTML = '<option value="">Select Subject First</option>';
    }
  },

  // Render academic context error
  renderAcademicContextError() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader('Enter Exam Marks', 'Input examination marks')}

        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-600')}
            </div>
            <div class="ml-4">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-red-900">Academic Context Required</h3>
              <p class="text-red-700 mt-2">
                You cannot enter exam marks without setting up the academic year and terms first.
              </p>
              <p class="text-red-700 mt-1">
                Please go to <strong>Academic Year → Setup</strong> to configure your academic year and terms before entering exam marks.
              </p>
              <div class="mt-4 text-center">
                ${SRDesignSystem.forms.button('setup-academic-exam-marks', 'Setup Academic Year & Terms', 'danger', {
                  icon: 'fas fa-calendar-plus',
                  onclick: 'EnterExamMarksComponent.navigateToAcademicSetup()'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Navigate to academic year setup
  navigateToAcademicSetup() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('academic-year-setup');
    }
  }
};


// Export components to global scope
window.EnterCAScoresComponent = EnterCAScoresComponent;
window.EnterExamMarksComponent = EnterExamMarksComponent;
window.AssessmentManagementComponents = AssessmentManagementComponents;
