const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');
const { BusinessValidation } = require('../utils/validation');
const { StudentValidation } = require('../utils/student-validation');
const { requireAcademicContext } = require('../middleware/validation');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get all students with optional filters
router.get('/', async (req, res) => {
  try {
    const {
      class_id,
      academic_year_id,
      term_id,
      status,
      level,
      academic_year_name,
      term_name
    } = req.query;

    // Build O-Level students query
    let oLevelQuery = `
      SELECT
        'o_level' as student_type,
        s.id, s.admission_number, s.first_name, s.middle_name, s.last_name,
        CONCAT(s.first_name, ' ', COALESCE(s.middle_name, ''), ' ', s.last_name) as student_name,
        s.gender, s.status, s.passport_photo, s.date_of_birth, s.enrollment_date,
        s.created_at, s.updated_at,
        s.current_class_id, s.current_academic_year_id, s.current_term_id,
        NULL as stream_type, NULL as registration_date,
        c.name as class_name,
        cl.name as class_level_name,
        el.name as education_level_name,
        st.name as stream_name,
        ay.name as academic_year_name,
        t.name as term_name
      FROM o_level_students s
      LEFT JOIN classes c ON s.current_class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams st ON s.stream_id = st.id
      LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
      LEFT JOIN terms t ON s.current_term_id = t.id
      WHERE 1=1
    `;

    // Build A-Level students query
    let aLevelQuery = `
      SELECT
        'a_level' as student_type,
        s.id, s.admission_number, s.first_name, s.middle_name, s.last_name,
        CONCAT(s.first_name, ' ', COALESCE(s.middle_name, ''), ' ', s.last_name) as student_name,
        s.gender, s.status, s.passport_photo, s.date_of_birth, s.registration_date as enrollment_date,
        s.created_at, s.updated_at,
        s.current_class_id, s.current_academic_year_id, s.current_term_id,
        NULL as stream_type, s.registration_date,
        c.name as class_name,
        cl.name as class_level_name,
        el.name as education_level_name,
        st.name as stream_name,
        ay.name as academic_year_name,
        t.name as term_name
      FROM a_level_students s
      LEFT JOIN classes c ON s.current_class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams st ON s.stream_id = st.id
      LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
      LEFT JOIN terms t ON s.current_term_id = t.id
      WHERE 1=1
    `;

    let params = [];
    let oLevelParams = [];
    let aLevelParams = [];

    // Apply filters to both queries
    if (class_id) {
      oLevelQuery += ' AND s.current_class_id = ?';
      aLevelQuery += ' AND s.current_class_id = ?';
      oLevelParams.push(class_id);
      aLevelParams.push(class_id);
    }

    if (academic_year_id) {
      oLevelQuery += ' AND s.current_academic_year_id = ?';
      aLevelQuery += ' AND s.current_academic_year_id = ?';
      oLevelParams.push(academic_year_id);
      aLevelParams.push(academic_year_id);
    }

    if (term_id) {
      oLevelQuery += ' AND s.current_term_id = ?';
      aLevelQuery += ' AND s.current_term_id = ?';
      oLevelParams.push(term_id);
      aLevelParams.push(term_id);
    }

    // Filter by academic year name
    if (academic_year_name) {
      oLevelQuery += ' AND ay.name = ?';
      aLevelQuery += ' AND ay.name = ?';
      oLevelParams.push(academic_year_name);
      aLevelParams.push(academic_year_name);
    }

    // Filter by term name
    if (term_name) {
      oLevelQuery += ' AND t.name = ?';
      aLevelQuery += ' AND t.name = ?';
      oLevelParams.push(term_name);
      aLevelParams.push(term_name);
    }

    // Filter by status (support multiple values)
    if (status) {
      if (status.includes(',')) {
        // Multiple status values
        const statusValues = status.split(',').map(s => s.trim());
        const placeholders = statusValues.map(() => '?').join(',');
        oLevelQuery += ` AND s.status IN (${placeholders})`;
        aLevelQuery += ` AND s.status IN (${placeholders})`;
        oLevelParams.push(...statusValues);
        aLevelParams.push(...statusValues);
      } else {
        // Single status value
        oLevelQuery += ' AND s.status = ?';
        aLevelQuery += ' AND s.status = ?';
        oLevelParams.push(status);
        aLevelParams.push(status);
      }
    }

    // Determine which query to execute based on level filter
    let finalQuery;
    if (level === 'o_level') {
      finalQuery = oLevelQuery + ' ORDER BY s.admission_number';
      params = oLevelParams;
    } else if (level === 'a_level') {
      finalQuery = aLevelQuery + ' ORDER BY s.admission_number';
      params = aLevelParams;
    } else {
      // Union both queries if no level specified - wrap in subquery for ORDER BY
      finalQuery = `
        SELECT * FROM (
          (${oLevelQuery})
          UNION ALL
          (${aLevelQuery})
        ) AS combined_students
        ORDER BY admission_number
      `;
      params = [...(oLevelParams || []), ...(aLevelParams || [])];
    }

    console.log('🔍 Executing students query with level:', level);
    console.log('📝 Final query:', finalQuery.substring(0, 200) + '...');
    console.log('📊 Query params:', params);

    const result = await executeQuery(finalQuery, params);

    if (!result.success) {
      console.error('❌ Database query failed:', result.error);
      throw new Error(result.error || 'Database query failed');
    }

    console.log('✅ Students query successful, returned', result.data?.length || 0, 'students');

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('❌ Get students error:', error);
    console.error('❌ Error stack:', error.stack);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve students'
    });
  }
});

// Get student by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { level } = req.query; // Optional level parameter to specify which table to search

    let student = null;

    // If level is specified, search only in that table
    if (level === 'o_level') {
      const oLevelQuery = `
        SELECT
          'o_level' as student_type,
          s.*,
          c.name as class_name,
          cl.name as class_level_name,
          el.name as education_level_name,
          st.name as stream_name,
          ay.name as academic_year_name,
          t.name as term_name
        FROM o_level_students s
        LEFT JOIN classes c ON s.current_class_id = c.id
        LEFT JOIN class_levels cl ON c.class_level_id = cl.id
        LEFT JOIN education_levels el ON cl.education_level_id = el.id
        LEFT JOIN streams st ON s.stream_id = st.id
        LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
        LEFT JOIN terms t ON s.current_term_id = t.id
        WHERE s.id = ?
      `;

      const result = await executeQuery(oLevelQuery, [id]);
      if (result.success && result.data.length > 0) {
        student = result.data[0];
      }
    } else if (level === 'a_level') {
      const aLevelQuery = `
        SELECT
          'a_level' as student_type,
          s.*,
          c.name as class_name,
          cl.name as class_level_name,
          el.name as education_level_name,
          st.name as stream_name,
          ay.name as academic_year_name,
          t.name as term_name
        FROM a_level_students s
        LEFT JOIN classes c ON s.current_class_id = c.id
        LEFT JOIN class_levels cl ON c.class_level_id = cl.id
        LEFT JOIN education_levels el ON cl.education_level_id = el.id
        LEFT JOIN streams st ON s.stream_id = st.id
        LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
        LEFT JOIN terms t ON s.current_term_id = t.id
        WHERE s.id = ?
      `;

      const result = await executeQuery(aLevelQuery, [id]);
      if (result.success && result.data.length > 0) {
        student = result.data[0];
      }
    } else {
      // Search in both tables if no level specified
      const oLevelQuery = `
        SELECT
          'o_level' as student_type,
          s.*,
          c.name as class_name,
          cl.name as class_level_name,
          el.name as education_level_name,
          st.name as stream_name,
          ay.name as academic_year_name,
          t.name as term_name
        FROM o_level_students s
        LEFT JOIN classes c ON s.current_class_id = c.id
        LEFT JOIN class_levels cl ON c.class_level_id = cl.id
        LEFT JOIN education_levels el ON cl.education_level_id = el.id
        LEFT JOIN streams st ON s.stream_id = st.id
        LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
        LEFT JOIN terms t ON s.current_term_id = t.id
        WHERE s.id = ?
      `;

      const oLevelResult = await executeQuery(oLevelQuery, [id]);
      if (oLevelResult.success && oLevelResult.data.length > 0) {
        student = oLevelResult.data[0];
      } else {
        const aLevelQuery = `
          SELECT
            'a_level' as student_type,
            s.*,
            c.name as class_name,
            cl.name as class_level_name,
            el.name as education_level_name,
            st.name as stream_name,
            ay.name as academic_year_name,
            t.name as term_name
          FROM a_level_students s
          LEFT JOIN classes c ON s.current_class_id = c.id
          LEFT JOIN class_levels cl ON c.class_level_id = cl.id
          LEFT JOIN education_levels el ON cl.education_level_id = el.id
          LEFT JOIN streams st ON s.stream_id = st.id
          LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
          LEFT JOIN terms t ON s.current_term_id = t.id
          WHERE s.id = ?
        `;

        const aLevelResult = await executeQuery(aLevelQuery, [id]);
        if (aLevelResult.success && aLevelResult.data.length > 0) {
          student = aLevelResult.data[0];
        }
      }
    }

    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    res.json({
      success: true,
      data: student
    });

  } catch (error) {
    console.error('Get student by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve student'
    });
  }
});

// Create new O-Level student
router.post('/o-level', async (req, res) => {
  try {
    const {
      admission_number, first_name, middle_name, last_name, gender, date_of_birth,
      passport_photo, current_class_id, current_academic_year_id, current_term_id
    } = req.body;

    // Validate required fields
    if (!admission_number || !first_name || !last_name || !gender) {
      return res.status(400).json({
        success: false,
        message: 'Admission number, first name, last name, and gender are required'
      });
    }

    // Check if admission number already exists in both O-Level and A-Level tables
    const checkOLevelQuery = 'SELECT id FROM o_level_students WHERE admission_number = ?';
    const checkALevelQuery = 'SELECT id FROM a_level_students WHERE admission_number = ?';

    const [oLevelResult, aLevelResult] = await Promise.all([
      executeQuery(checkOLevelQuery, [admission_number]),
      executeQuery(checkALevelQuery, [admission_number])
    ]);

    if (!oLevelResult.success || !aLevelResult.success) {
      throw new Error('Failed to check admission number uniqueness');
    }

    if (oLevelResult.data.length > 0 || aLevelResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Admission number already exists'
      });
    }

    // Insert new O-Level student
    const insertQuery = `
      INSERT INTO o_level_students (
        admission_number, first_name, middle_name, last_name, gender, date_of_birth,
        current_class_id, current_academic_year_id, current_term_id,
        status, enrollment_date, passport_photo
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const insertResult = await executeQuery(insertQuery, [
      admission_number, first_name, middle_name, last_name, gender, date_of_birth,
      current_class_id, current_academic_year_id, current_term_id,
      'active', new Date().toISOString().split('T')[0], passport_photo
    ]);

    if (!insertResult.success) {
      throw new Error(insertResult.error);
    }

    const studentId = insertResult.data.insertId;

    // Get the created student with class info
    const newStudentQuery = `
      SELECT
        'o_level' as student_type,
        s.*,
        c.name as class_name,
        cl.name as class_level_name,
        el.name as education_level_name,
        st.name as stream_name,
        ay.name as academic_year_name,
        t.name as term_name
      FROM o_level_students s
      LEFT JOIN classes c ON s.current_class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams st ON s.stream_id = st.id
      LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
      LEFT JOIN terms t ON s.current_term_id = t.id
      WHERE s.id = ?
    `;

    const newStudentResult = await executeQuery(newStudentQuery, [studentId]);

    res.status(201).json({
      success: true,
      message: 'O-Level student created successfully',
      data: newStudentResult.data[0]
    });

  } catch (error) {
    console.error('Create O-Level student error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create O-Level student'
    });
  }
});

// Create new A-Level student
router.post('/a-level', async (req, res) => {
  try {
    const {
      admission_number, first_name, middle_name, last_name, gender, date_of_birth,
      passport_photo, stream_id, current_class_id, current_academic_year_id, current_term_id
    } = req.body;

    // Validate required fields
    if (!admission_number || !first_name || !last_name || !gender || !stream_id) {
      return res.status(400).json({
        success: false,
        message: 'Admission number, first name, last name, gender, and stream are required'
      });
    }

    // Check if admission number already exists in both O-Level and A-Level tables
    const checkOLevelQuery = 'SELECT id FROM o_level_students WHERE admission_number = ?';
    const checkALevelQuery = 'SELECT id FROM a_level_students WHERE admission_number = ?';

    const [oLevelResult, aLevelResult] = await Promise.all([
      executeQuery(checkOLevelQuery, [admission_number]),
      executeQuery(checkALevelQuery, [admission_number])
    ]);

    if (!oLevelResult.success || !aLevelResult.success) {
      throw new Error('Failed to check admission number uniqueness');
    }

    if (oLevelResult.data.length > 0 || aLevelResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Admission number already exists'
      });
    }

    // Insert new A-Level student
    const insertQuery = `
      INSERT INTO a_level_students (
        admission_number, first_name, middle_name, last_name, gender, date_of_birth,
        current_class_id, stream_id, current_academic_year_id, current_term_id,
        status, registration_date, passport_photo
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const insertResult = await executeQuery(insertQuery, [
      admission_number, first_name, middle_name, last_name, gender, date_of_birth,
      current_class_id, stream_id, current_academic_year_id, current_term_id,
      'active', new Date().toISOString().split('T')[0], passport_photo
    ]);

    if (!insertResult.success) {
      throw new Error(insertResult.error);
    }

    const studentId = insertResult.data.insertId;

    // Get the created student with class info
    const newStudentQuery = `
      SELECT
        'a_level' as student_type,
        s.*,
        c.name as class_name,
        cl.name as class_level_name,
        el.name as education_level_name,
        st.name as stream_name,
        ay.name as academic_year_name,
        t.name as term_name
      FROM a_level_students s
      LEFT JOIN classes c ON s.current_class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams st ON s.stream_id = st.id
      LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
      LEFT JOIN terms t ON s.current_term_id = t.id
      WHERE s.id = ?
    `;

    const newStudentResult = await executeQuery(newStudentQuery, [studentId]);

    res.status(201).json({
      success: true,
      message: 'A-Level student created successfully',
      data: newStudentResult.data[0]
    });

  } catch (error) {
    console.error('Create A-Level student error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create A-Level student'
    });
  }
});

// Update O-Level student
router.put('/o-level/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      admission_number, first_name, middle_name, last_name, gender, date_of_birth,
      passport_photo, status, current_class_id, current_academic_year_id, current_term_id
    } = req.body;

    // Check if student exists
    const checkQuery = 'SELECT id FROM o_level_students WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'O-Level student not found'
      });
    }

    // Check if admission number already exists for other students
    const duplicateQuery = 'SELECT id FROM o_level_students WHERE admission_number = ? AND id != ?';
    const duplicateResult = await executeQuery(duplicateQuery, [admission_number, id]);

    if (!duplicateResult.success) {
      throw new Error(duplicateResult.error);
    }

    if (duplicateResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Admission number already exists'
      });
    }

    // Update student
    const updateQuery = `
      UPDATE o_level_students SET
        admission_number = ?, first_name = ?, middle_name = ?, last_name = ?,
        gender = ?, date_of_birth = ?, passport_photo = ?, status = ?,
        current_class_id = ?, current_academic_year_id = ?, current_term_id = ?,
        updated_at = NOW()
      WHERE id = ?
    `;

    const updateResult = await executeQuery(updateQuery, [
      admission_number, first_name, middle_name, last_name, gender, date_of_birth,
      passport_photo, status, current_class_id, current_academic_year_id, current_term_id, id
    ]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    // Get updated student
    const updatedStudentQuery = `
      SELECT
        'o_level' as student_type,
        s.*,
        c.name as class_name,
        cl.name as class_level_name,
        el.name as education_level_name,
        st.name as stream_name,
        ay.name as academic_year_name,
        t.name as term_name
      FROM o_level_students s
      LEFT JOIN classes c ON s.current_class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams st ON s.stream_id = st.id
      LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
      LEFT JOIN terms t ON s.current_term_id = t.id
      WHERE s.id = ?
    `;

    const updatedStudentResult = await executeQuery(updatedStudentQuery, [id]);

    res.json({
      success: true,
      message: 'O-Level student updated successfully',
      data: updatedStudentResult.data[0]
    });

  } catch (error) {
    console.error('Update O-Level student error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update O-Level student'
    });
  }
});

// Update A-Level student
router.put('/a-level/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      admission_number, first_name, middle_name, last_name, gender, date_of_birth,
      passport_photo, status, stream_id, current_class_id, current_academic_year_id, current_term_id
    } = req.body;

    // Check if student exists
    const checkQuery = 'SELECT id FROM a_level_students WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'A-Level student not found'
      });
    }

    // Check if admission number already exists for other students
    const duplicateQuery = 'SELECT id FROM a_level_students WHERE admission_number = ? AND id != ?';
    const duplicateResult = await executeQuery(duplicateQuery, [admission_number, id]);

    if (!duplicateResult.success) {
      throw new Error(duplicateResult.error);
    }

    if (duplicateResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Admission number already exists'
      });
    }

    // Update student
    const updateQuery = `
      UPDATE a_level_students SET
        admission_number = ?, first_name = ?, middle_name = ?, last_name = ?,
        gender = ?, date_of_birth = ?, passport_photo = ?, status = ?, stream_id = ?,
        current_class_id = ?, current_academic_year_id = ?, current_term_id = ?,
        updated_at = NOW()
      WHERE id = ?
    `;

    const updateResult = await executeQuery(updateQuery, [
      admission_number, first_name, middle_name, last_name, gender, date_of_birth,
      passport_photo, status, stream_id, current_class_id, current_academic_year_id, current_term_id, id
    ]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    // Get updated student
    const updatedStudentQuery = `
      SELECT
        'a_level' as student_type,
        s.*,
        c.name as class_name,
        cl.name as class_level_name,
        el.name as education_level_name,
        st.name as stream_name,
        ay.name as academic_year_name,
        t.name as term_name
      FROM a_level_students s
      LEFT JOIN classes c ON s.current_class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams st ON s.stream_id = st.id
      LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
      LEFT JOIN terms t ON s.current_term_id = t.id
      WHERE s.id = ?
    `;

    const updatedStudentResult = await executeQuery(updatedStudentQuery, [id]);

    res.json({
      success: true,
      message: 'A-Level student updated successfully',
      data: updatedStudentResult.data[0]
    });

  } catch (error) {
    console.error('Update A-Level student error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update A-Level student'
    });
  }
});

// Delete O-Level student
router.delete('/o-level/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if student exists
    const checkQuery = 'SELECT id FROM o_level_students WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'O-Level student not found'
      });
    }

    // Delete student (this will cascade to assessments due to foreign key constraints)
    const deleteQuery = 'DELETE FROM o_level_students WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'O-Level student deleted successfully'
    });

  } catch (error) {
    console.error('Delete O-Level student error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete O-Level student'
    });
  }
});

// Delete A-Level student
router.delete('/a-level/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if student exists
    const checkQuery = 'SELECT id FROM a_level_students WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'A-Level student not found'
      });
    }

    // Delete student (this will cascade to assessments due to foreign key constraints)
    const deleteQuery = 'DELETE FROM a_level_students WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'A-Level student deleted successfully'
    });

  } catch (error) {
    console.error('Delete A-Level student error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete A-Level student'
    });
  }
});

// Update student class assignment
router.post('/assign-class', async (req, res) => {
  try {
    const { student_id, class_id, academic_year_id, term_id, student_type } = req.body;

    // Validate required fields
    if (!student_id || !class_id || !academic_year_id || !student_type) {
      return res.status(400).json({
        success: false,
        message: 'Student ID, class ID, academic year ID, and student type are required'
      });
    }

    // Validate student type
    if (!['o_level', 'a_level'].includes(student_type)) {
      return res.status(400).json({
        success: false,
        message: 'Student type must be either o_level or a_level'
      });
    }

    // Check if class exists
    const classQuery = 'SELECT id FROM classes WHERE id = ?';
    const classResult = await executeQuery(classQuery, [class_id]);

    if (!classResult.success || classResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class not found'
      });
    }

    // Update student's class assignment
    const tableName = student_type === 'o_level' ? 'o_level_students' : 'a_level_students';
    const updateQuery = `
      UPDATE ${tableName} SET
        current_class_id = ?, current_academic_year_id = ?, current_term_id = ?, status = 'active'
      WHERE id = ?
    `;

    const updateResult = await executeQuery(updateQuery, [class_id, academic_year_id, term_id, student_id]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    if (updateResult.data.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    res.json({
      success: true,
      message: 'Student class assignment updated successfully',
      data: {
        student_id,
        class_id,
        academic_year_id,
        term_id
      }
    });

  } catch (error) {
    console.error('Assign class error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign student to class'
    });
  }
});

// Get student class assignments
router.get('/class-assignments', async (req, res) => {
  try {
    const { student_type, class_id, academic_year_id, term_id } = req.query;

    let oLevelQuery = `
      SELECT
        s.id, s.admission_number, s.first_name, s.middle_name, s.last_name,
        s.current_class_id, s.current_academic_year_id, s.current_term_id, s.status,
        c.name as class_name,
        ay.name as academic_year_name,
        t.name as term_name,
        'o_level' as student_type
      FROM o_level_students s
      LEFT JOIN classes c ON s.current_class_id = c.id
      LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
      LEFT JOIN terms t ON s.current_term_id = t.id
      WHERE s.status = 'active'
    `;

    let aLevelQuery = `
      SELECT
        s.id, s.admission_number, s.first_name, s.middle_name, s.last_name,
        s.current_class_id, s.current_academic_year_id, s.current_term_id, s.status,
        c.name as class_name,
        ay.name as academic_year_name,
        t.name as term_name,
        'a_level' as student_type
      FROM a_level_students s
      LEFT JOIN classes c ON s.current_class_id = c.id
      LEFT JOIN academic_years ay ON s.current_academic_year_id = ay.id
      LEFT JOIN terms t ON s.current_term_id = t.id
      WHERE s.status = 'active'
    `;

    let params = [];
    let queries = [];

    if (!student_type || student_type === 'o_level') {
      if (class_id) {
        oLevelQuery += ' AND s.current_class_id = ?';
        params.push(class_id);
      }
      if (academic_year_id) {
        oLevelQuery += ' AND s.current_academic_year_id = ?';
        params.push(academic_year_id);
      }
      if (term_id) {
        oLevelQuery += ' AND s.current_term_id = ?';
        params.push(term_id);
      }
      queries.push(oLevelQuery);
    }

    if (!student_type || student_type === 'a_level') {
      if (class_id) {
        aLevelQuery += ' AND s.current_class_id = ?';
        params.push(class_id);
      }
      if (academic_year_id) {
        aLevelQuery += ' AND s.current_academic_year_id = ?';
        params.push(academic_year_id);
      }
      if (term_id) {
        aLevelQuery += ' AND s.current_term_id = ?';
        params.push(term_id);
      }
      queries.push(aLevelQuery);
    }

    const finalQuery = queries.join(' UNION ALL ') + ' ORDER BY admission_number';

    const result = await executeQuery(finalQuery, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get class assignments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve class assignments'
    });
  }
});

// Update student status
router.put('/status/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { status, student_type } = req.body;

    // Validate required fields
    if (!status || !student_type) {
      return res.status(400).json({
        success: false,
        message: 'Status and student type are required'
      });
    }

    // Validate student type
    if (!['o_level', 'a_level'].includes(student_type)) {
      return res.status(400).json({
        success: false,
        message: 'Student type must be either o_level or a_level'
      });
    }

    // Validate status
    if (!['active', 'inactive', 'graduated', 'transferred'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status value'
      });
    }

    const tableName = student_type === 'o_level' ? 'o_level_students' : 'a_level_students';
    const updateQuery = `UPDATE ${tableName} SET status = ? WHERE id = ?`;
    const updateResult = await executeQuery(updateQuery, [status, id]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    if (updateResult.data.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    res.json({
      success: true,
      message: 'Student status updated successfully'
    });

  } catch (error) {
    console.error('Update student status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update student status'
    });
  }
});

// Get student statistics
router.get('/stats/overview', async (req, res) => {
  try {
    const statsQuery = `
      SELECT
        SUM(total_count) as total_students,
        SUM(active_count) as active_students,
        SUM(transferred_count) as transferred_students,
        SUM(graduated_count) as graduated_students,
        SUM(dropped_count) as dropped_students,
        SUM(suspended_count) as suspended_students,
        SUM(male_count) as male_students,
        SUM(female_count) as female_students
      FROM (
        SELECT
          COUNT(*) as total_count,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count,
          COUNT(CASE WHEN status = 'transferred' THEN 1 END) as transferred_count,
          COUNT(CASE WHEN status = 'graduated' THEN 1 END) as graduated_count,
          COUNT(CASE WHEN status = 'dropped' THEN 1 END) as dropped_count,
          COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_count,
          COUNT(CASE WHEN gender = 'Male' THEN 1 END) as male_count,
          COUNT(CASE WHEN gender = 'Female' THEN 1 END) as female_count
        FROM o_level_students
        UNION ALL
        SELECT
          COUNT(*) as total_count,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count,
          COUNT(CASE WHEN status = 'transferred' THEN 1 END) as transferred_count,
          COUNT(CASE WHEN status = 'graduated' THEN 1 END) as graduated_count,
          COUNT(CASE WHEN status = 'dropped' THEN 1 END) as dropped_count,
          COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_count,
          COUNT(CASE WHEN gender = 'Male' THEN 1 END) as male_count,
          COUNT(CASE WHEN gender = 'Female' THEN 1 END) as female_count
        FROM a_level_students
      ) combined_stats
    `;

    const result = await executeQuery(statsQuery);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get student stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve student statistics'
    });
  }
});

// Register new O-Level student
router.post('/register/o-level', requireAcademicContext(), async (req, res) => {
  try {
    // Extract fields from request body (same approach as A-Level registration)
    const {
      admission_number, first_name, middle_name, last_name, gender, date_of_birth,
      current_class_id, stream_id, current_academic_year_id, current_term_id
    } = req.body;

    console.log('📝 Registering new O-Level student:', { admission_number, first_name, last_name });

    // Comprehensive validation using StudentValidation utility
    const validationResult = await StudentValidation.validateStudentRegistration(req.body, 'o_level');

    if (!validationResult.isValid) {
      // If there's an admission number error, use the specific message
      const admissionError = validationResult.errors.find(error =>
        error.includes('Admission number') && error.includes('already registered')
      );

      return res.status(400).json({
        success: false,
        message: admissionError || validationResult.errors[0] || 'Validation failed',
        errors: validationResult.errors
      });
    }

    // Validate stream requirements for O-Level students
    if (current_class_id) {
      // Get class information to determine if streams are required
      const classInfoQuery = `
        SELECT c.id, c.name, c.class_level_id,
               cl.code as class_level_code, cl.streams_optional,
               el.code as education_level_code
        FROM classes c
        JOIN class_levels cl ON c.class_level_id = cl.id
        JOIN education_levels el ON cl.education_level_id = el.id
        WHERE c.id = ? AND c.is_active = TRUE
      `;

      const classInfoResult = await executeQuery(classInfoQuery, [current_class_id]);

      if (!classInfoResult.success || classInfoResult.data.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Invalid class selection'
        });
      }

      const classInfo = classInfoResult.data[0];

      // Check if this is an O-Level class
      if (classInfo.education_level_code === 'o_level') {
        // Get available streams for this class in current academic context
        const streamsQuery = `
          SELECT s.id, s.name, s.stream_type
          FROM streams s
          JOIN stream_classes sc ON s.id = sc.stream_id
          WHERE sc.class_level_id = ?
            AND s.stream_type = 'o_level'
        `;

        const streamsResult = await executeQuery(streamsQuery, [classInfo.class_level_id]);

        if (streamsResult.success && streamsResult.data.length > 0) {
          // Streams are available for this class, so stream_id is required
          if (!stream_id) {
            return res.status(400).json({
              success: false,
              message: 'Stream selection is required for this class as it has available streams'
            });
          }

          // Validate that the selected stream is valid for this class
          const validStream = streamsResult.data.find(s => s.id == stream_id);
          if (!validStream) {
            return res.status(400).json({
              success: false,
              message: 'Invalid stream selection for this class'
            });
          }
        }
        // If no streams are available, stream_id can be null (handled automatically)
      } else {
        return res.status(400).json({
          success: false,
          message: 'Please select an O-Level class (S.1 - S.4) for O-Level student registration'
        });
      }
    }



    // Handle optional fields - convert undefined/empty to null (same approach as A-Level)
    const processedMiddleName = middle_name || null;
    const processedDateOfBirth = date_of_birth || null;

    // Handle stream_id - convert empty string or invalid values to NULL
    const processedStreamId = (stream_id && stream_id !== '' && stream_id !== 'null' && stream_id !== 'undefined') ? stream_id : null;

    // Prepare the exact values that will be sent to database
    const insertValues = [
      admission_number, first_name, processedMiddleName, last_name, gender, processedDateOfBirth,
      current_class_id, processedStreamId, current_academic_year_id, current_term_id,
      'active', new Date().toISOString().split('T')[0], req.body.passport_photo
    ];



    // Insert new O-Level student
    const insertStudentQuery = `
      INSERT INTO o_level_students (
        admission_number, first_name, middle_name, last_name, gender, date_of_birth,
        current_class_id, stream_id, current_academic_year_id, current_term_id,
        status, enrollment_date, passport_photo
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery(insertStudentQuery, insertValues);

    if (!result.success) {
      console.error('❌ DATABASE INSERT FAILED:');
      console.error('❌ Error:', result.error);
      console.error('❌ SQL Query:', insertStudentQuery);
      console.error('❌ Values:', insertValues);
      throw new Error(result.error);
    }

    const studentId = result.data.insertId;

    // Store student subjects in o_level_student_subjects table
    const selectedSubjects = req.body.selected_subjects;
    if (selectedSubjects && Array.isArray(selectedSubjects) && selectedSubjects.length > 0) {
      // Get class level for subject assignments
      const classLevelQuery = 'SELECT class_level_id FROM classes WHERE id = ?';
      const classLevelResult = await executeQuery(classLevelQuery, [current_class_id]);

      if (!classLevelResult.success || classLevelResult.data.length === 0) {
        throw new Error('Failed to get class level information');
      }

      const classLevelId = classLevelResult.data[0].class_level_id;

      // Insert subject assignments
      for (const subjectId of selectedSubjects) {
        const subjectQuery = `
          INSERT INTO o_level_student_subjects (student_id, subject_id, class_level_id)
          VALUES (?, ?, ?)
        `;
        const subjectResult = await executeQuery(subjectQuery, [studentId, subjectId, classLevelId]);

        if (!subjectResult.success) {
          console.error('Failed to assign O-Level subject:', subjectId, subjectResult.error);
        }
      }

      console.log('✅ O-Level student subjects assigned:', selectedSubjects.length, 'subjects');
    } else {
      console.warn('⚠️ No subjects provided for O-Level student registration');
    }

    // Note: Enrollment tracking is now handled directly in the o_level_students table
    // No separate enrollment record needed
    console.log('✅ O-Level student created with ID:', studentId);

    res.status(201).json({
      success: true,
      message: 'Student registered successfully',
      data: {
        id: studentId,
        admission_number,
        first_name,
        middle_name,
        last_name,
        gender,
        date_of_birth,
        current_class_id,
        stream_id: processedStreamId,
        current_academic_year_id,
        current_term_id,
        subjects: selectedSubjects || [],
        status: 'active'
      }
    });

  } catch (error) {
    console.error('❌ O-Level student registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to register O-Level student',
      error: error.message
    });
  }
});

// Register new A-Level student
router.post('/register/a-level', requireAcademicContext(), async (req, res) => {
  try {
    const {
      admission_number, first_name, middle_name, last_name, gender, date_of_birth,
      current_class_id, stream_id, current_academic_year_id, current_term_id,
      principal_subject_1_id, principal_subject_2_id, principal_subject_3_id,
      subsidiary_subject_1_id, subsidiary_subject_2_id
    } = req.body;

    console.log('📝 Registering new A-Level student:', { admission_number, first_name, last_name });

    // Handle optional fields - convert undefined to null
    const processedMiddleName = middle_name || null;
    const processedDateOfBirth = date_of_birth || null;

    // Comprehensive validation using StudentValidation utility
    const validationResult = await StudentValidation.validateStudentRegistration(req.body, 'a_level');

    if (!validationResult.isValid) {
      // If there's an admission number error, use the specific message
      const admissionError = validationResult.errors.find(error =>
        error.includes('Admission number') && error.includes('already registered')
      );

      return res.status(400).json({
        success: false,
        message: admissionError || validationResult.errors[0] || 'Validation failed',
        errors: validationResult.errors
      });
    }

    // Validate stream - A-Level streams (Sciences/Arts) are permanent and always available
    const streamValidationQuery = `
      SELECT s.id, s.name, s.stream_type
      FROM streams s
      WHERE s.id = ? AND s.stream_type = 'a_level'
    `;
    const streamValidationResult = await executeQuery(streamValidationQuery, [stream_id]);

    if (!streamValidationResult.success || streamValidationResult.data.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid stream selection. Please select a valid A-Level stream (Arts or Sciences)'
      });
    }



    // Insert new A-Level student (only basic info, subjects stored separately)
    const insertStudentQuery = `
      INSERT INTO a_level_students (
        admission_number, first_name, middle_name, last_name, gender, date_of_birth,
        current_class_id, stream_id, current_academic_year_id, current_term_id,
        status, registration_date, passport_photo
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery(insertStudentQuery, [
      admission_number, first_name, processedMiddleName, last_name, gender, processedDateOfBirth,
      current_class_id, stream_id, current_academic_year_id, current_term_id,
      'active', new Date().toISOString().split('T')[0], req.body.passport_photo
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    const studentId = result.data.insertId;
    console.log('✅ A-Level student created with ID:', studentId);

    // Store student subjects in a_level_student_subjects table
    const subjects = [];
    if (principal_subject_1_id) subjects.push(principal_subject_1_id);
    if (principal_subject_2_id) subjects.push(principal_subject_2_id);
    if (principal_subject_3_id) subjects.push(principal_subject_3_id);
    if (subsidiary_subject_1_id) subjects.push(subsidiary_subject_1_id); // General Paper
    if (subsidiary_subject_2_id) subjects.push(subsidiary_subject_2_id); // SMATH or SICT

    // Get class level for subject assignments
    const classLevelQuery = 'SELECT class_level_id FROM classes WHERE id = ?';
    const classLevelResult = await executeQuery(classLevelQuery, [current_class_id]);

    if (!classLevelResult.success || classLevelResult.data.length === 0) {
      throw new Error('Failed to get class level information');
    }

    const classLevelId = classLevelResult.data[0].class_level_id;

    // Validate subjects before assignment
    if (subjects.length > 0) {
      const subjectValidationQuery = `
        SELECT s.id, s.name, s.subject_type
        FROM a_level_subjects s
        WHERE s.id IN (${subjects.map(() => '?').join(',')}) AND s.is_active = TRUE
      `;

      const subjectValidationResult = await executeQuery(subjectValidationQuery, subjects);

      if (!subjectValidationResult.success) {
        throw new Error('Failed to validate subjects');
      }

      if (subjectValidationResult.data.length !== subjects.length) {
        // Rollback student creation
        await executeQuery('DELETE FROM a_level_students WHERE id = ?', [studentId]);
        return res.status(400).json({
          success: false,
          message: 'Some selected subjects are invalid or inactive'
        });
      }
    }

    // Insert subject assignments
    for (const subjectId of subjects) {
      const subjectQuery = `
        INSERT INTO a_level_student_subjects (student_id, subject_id, class_level_id)
        VALUES (?, ?, ?)
      `;
      const subjectResult = await executeQuery(subjectQuery, [studentId, subjectId, classLevelId]);

      if (!subjectResult.success) {
        console.error('Failed to assign subject:', subjectId, subjectResult.error);
        // Continue with other subjects rather than failing completely
      }
    }

    console.log('✅ A-Level student subjects assigned:', subjects.length, 'subjects');

    // UACE points will be calculated dynamically from subject grades
    // No need to store them separately - they're computed on-demand

    // Note: Enrollment tracking is now handled directly in the a_level_students table
    // No separate enrollment record needed

    res.status(201).json({
      success: true,
      message: 'Student registered successfully',
      data: {
        id: studentId,
        admission_number,
        first_name,
        middle_name,
        last_name,
        gender,
        date_of_birth,
        current_class_id,
        current_academic_year_id,
        current_term_id,
        stream_id,
        subjects: subjects,
        status: 'active'
      }
    });

  } catch (error) {
    console.error('❌ A-Level student registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to register A-Level student',
      error: error.message
    });
  }
});

// Get registration form data (classes, academic years, terms)
router.get('/registration-form-data', async (req, res) => {
  try {
    console.log('📋 Fetching registration form data...');

    // Get class levels first
    const classLevelsQuery = `
      SELECT cl.id, cl.code, cl.name, cl.display_name, cl.sort_order, cl.streams_optional,
             el.code as education_level_code, el.name as education_level_name
      FROM class_levels cl
      JOIN education_levels el ON cl.education_level_id = el.id
      WHERE cl.is_active = 1
      ORDER BY cl.sort_order
    `;

    // Get streams
    const streamsQuery = `
      SELECT id, name, display_name, stream_type, academic_year_id
      FROM streams
      WHERE is_active = 1
      ORDER BY stream_type, name
    `;

    // Get classes with class level information
    const classesQuery = `
      SELECT
        c.id,
        c.name as class_name,
        c.class_level_id,
        cl.name as class_level_name,
        cl.display_name as class_level_display_name,
        el.code as education_level_code,
        el.name as education_level_name
      FROM classes c
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      WHERE c.is_active = 1
      ORDER BY cl.sort_order, c.name
    `;

    // Get academic years
    const academicYearsQuery = `
      SELECT id, name, start_date, end_date, is_active
      FROM academic_years
      ORDER BY name DESC
    `;

    // Get terms
    const termsQuery = `
      SELECT id, name, number, start_date, end_date, is_active, academic_year_id
      FROM terms
      ORDER BY academic_year_id, number
    `;

    // Execute all queries
    const [classLevelsResult, streamsResult, classesResult, academicYearsResult, termsResult] = await Promise.all([
      executeQuery(classLevelsQuery),
      executeQuery(streamsQuery),
      executeQuery(classesQuery),
      executeQuery(academicYearsQuery),
      executeQuery(termsQuery)
    ]);

    // Check if all queries succeeded
    if (!classLevelsResult.success || !streamsResult.success || !classesResult.success || !academicYearsResult.success || !termsResult.success) {
      console.error('❌ One or more queries failed:', {
        classLevels: classLevelsResult.success,
        streams: streamsResult.success,
        classes: classesResult.success,
        academicYears: academicYearsResult.success,
        terms: termsResult.success
      });
      throw new Error('Failed to fetch form data');
    }

    console.log('✅ Registration form data fetched successfully:', {
      classLevels: classLevelsResult.data.length,
      streams: streamsResult.data.length,
      classes: classesResult.data.length,
      academicYears: academicYearsResult.data.length,
      terms: termsResult.data.length
    });

    res.json({
      success: true,
      data: {
        class_levels: classLevelsResult.data,
        streams: streamsResult.data,
        classes: classesResult.data,
        academic_years: academicYearsResult.data,
        terms: termsResult.data
      }
    });

  } catch (error) {
    console.error('❌ Get registration form data error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve registration form data',
      error: error.message
    });
  }
});

// Get student subjects
router.get('/:student_type/:student_id/subjects', async (req, res) => {
  try {
    const { student_type, student_id } = req.params;

    // Validate student type
    if (!['o_level', 'a_level'].includes(student_type)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid student type'
      });
    }

    const subjectTable = student_type === 'o_level' ? 'o_level_subjects' : 'a_level_subjects';
    const studentSubjectTable = student_type === 'o_level' ? 'o_level_student_subjects' : 'a_level_student_subjects';

    const query = `
      SELECT
        ss.id as assignment_id,
        s.id as subject_id,
        s.name,
        s.short_name,
        s.subject_type,
        s.uneb_code,
        ss.class_level_id,

        cl.name as class_level_name
      FROM ${studentSubjectTable} ss
      JOIN ${subjectTable} s ON ss.subject_id = s.id
      JOIN class_levels cl ON ss.class_level_id = cl.id
      WHERE ss.student_id = ?
      ORDER BY s.subject_type, s.name
    `;

    const result = await executeQuery(query, [student_id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get student subjects error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve student subjects'
    });
  }
});

// Update student subjects
router.put('/:student_type/:student_id/subjects', async (req, res) => {
  try {
    const { student_type, student_id } = req.params;
    const { subject_ids, class_level_id } = req.body;

    // Validate student type
    if (!['o_level', 'a_level'].includes(student_type)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid student type'
      });
    }

    if (!Array.isArray(subject_ids) || !class_level_id) {
      return res.status(400).json({
        success: false,
        message: 'Subject IDs array and class level ID are required'
      });
    }

    // Verify student exists and get their current class information
    const studentTable = student_type === 'o_level' ? 'o_level_students' : 'a_level_students';
    const studentQuery = `
      SELECT s.id, s.current_class_id, c.class_level_id as current_class_level_id
      FROM ${studentTable} s
      JOIN classes c ON s.current_class_id = c.id
      WHERE s.id = ?
    `;

    const studentResult = await executeQuery(studentQuery, [student_id]);

    if (!studentResult.success || studentResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    // Validate that the provided class_level_id matches the student's current class level
    if (studentResult.data[0].current_class_level_id != class_level_id) {
      return res.status(400).json({
        success: false,
        message: 'Class level ID does not match student\'s current class'
      });
    }

    // Validate subjects are appropriate for this student type and class level
    if (subject_ids.length > 0) {
      const subjectTable = student_type === 'o_level' ? 'o_level_subjects' : 'a_level_subjects';

      let subjectValidationQuery;
      let queryParams;

      if (student_type === 'o_level') {
        // O-Level subjects need class-level validation
        subjectValidationQuery = `
          SELECT s.id, s.name, s.subject_type, sc.subject_status
          FROM ${subjectTable} s
          JOIN o_level_subject_classes sc ON s.id = sc.subject_id
          WHERE s.id IN (${subject_ids.map(() => '?').join(',')})
          AND sc.class_level_id = ? AND s.is_active = TRUE
        `;
        queryParams = [...subject_ids, class_level_id];
      } else {
        // A-Level subjects are available to all A-Level classes (S.5 and S.6)
        subjectValidationQuery = `
          SELECT s.id, s.name, s.subject_type, 'elective' as subject_status
          FROM ${subjectTable} s
          WHERE s.id IN (${subject_ids.map(() => '?').join(',')})
          AND s.is_active = TRUE
        `;
        queryParams = subject_ids;
      }

      const subjectValidationResult = await executeQuery(subjectValidationQuery, queryParams);

      if (!subjectValidationResult.success) {
        throw new Error('Failed to validate subjects');
      }

      if (subjectValidationResult.data.length !== subject_ids.length) {
        return res.status(400).json({
          success: false,
          message: 'Some selected subjects are not available for this class level'
        });
      }
    }

    const studentSubjectTable = student_type === 'o_level' ? 'o_level_student_subjects' : 'a_level_student_subjects';

    // Start transaction
    await executeQuery('START TRANSACTION');

    try {
      // Get current subject assignments for this student and class level
      const currentSubjectsQuery = `
        SELECT subject_id FROM ${studentSubjectTable}
        WHERE student_id = ? AND class_level_id = ?
      `;

      const currentSubjectsResult = await executeQuery(currentSubjectsQuery, [student_id, class_level_id]);
      const currentSubjectIds = currentSubjectsResult.success ?
        currentSubjectsResult.data.map(row => row.subject_id) : [];

      console.log(`� Current subjects for student ${student_id}:`, currentSubjectIds);
      console.log(`📋 New subjects for student ${student_id}:`, subject_ids);

      // Find subjects to remove (in current but not in new)
      const subjectsToRemove = currentSubjectIds.filter(id => !subject_ids.includes(id));

      // Find subjects to add (in new but not in current)
      const subjectsToAdd = subject_ids.filter(id => !currentSubjectIds.includes(id));

      console.log(`🗑️ Subjects to remove:`, subjectsToRemove);
      console.log(`➕ Subjects to add:`, subjectsToAdd);

      // Remove subjects that are no longer selected
      if (subjectsToRemove.length > 0) {
        const removeQuery = `
          DELETE FROM ${studentSubjectTable}
          WHERE student_id = ? AND class_level_id = ? AND subject_id IN (${subjectsToRemove.map(() => '?').join(',')})
        `;

        const removeResult = await executeQuery(removeQuery, [student_id, class_level_id, ...subjectsToRemove]);
        console.log(`🗑️ Removed ${removeResult.data?.affectedRows || 0} subject assignments`);
      }

      // Add new subjects
      if (subjectsToAdd.length > 0) {
        for (const subjectId of subjectsToAdd) {
          const insertQuery = `
            INSERT INTO ${studentSubjectTable} (student_id, subject_id, class_level_id)
            VALUES (?, ?, ?)
          `;

          const insertResult = await executeQuery(insertQuery, [student_id, subjectId, class_level_id]);
          if (!insertResult.success) {
            throw new Error(`Failed to insert subject ${subjectId}: ${insertResult.error}`);
          }
        }
        console.log(`✅ Added ${subjectsToAdd.length} new subject assignments`);
      }

      if (subjectsToRemove.length === 0 && subjectsToAdd.length === 0) {
        console.log(`ℹ️ No changes needed for student ${student_id} subjects`);
      }

      // Commit transaction
      await executeQuery('COMMIT');

      console.log(`✅ Successfully updated subjects for ${student_type} student ${student_id}`);

      res.json({
        success: true,
        message: 'Student subjects updated successfully',
        data: {
          student_id: parseInt(student_id),
          updated_subjects: subject_ids.length,
          class_level_id: parseInt(class_level_id),
          subject_ids: subject_ids
        }
      });

    } catch (transactionError) {
      // Rollback transaction on error
      console.error('Transaction error during subject update:', transactionError);
      await executeQuery('ROLLBACK');
      throw transactionError;
    }

  } catch (error) {
    console.error('Update student subjects error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update student subjects',
      error: error.message
    });
  }
});

module.exports = router;
