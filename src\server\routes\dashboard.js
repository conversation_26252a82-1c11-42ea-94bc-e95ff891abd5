const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get dashboard statistics
router.get('/stats', async (req, res) => {
  try {
    const { academic_year_id, term_id } = req.query;
    
    // Build academic context filter
    let academicFilter = '';
    let academicParams = [];
    
    if (academic_year_id) {
      academicFilter += ' AND academic_year_id = ?';
      academicParams.push(academic_year_id);
    }
    
    if (term_id) {
      academicFilter += ' AND term_id = ?';
      academicParams.push(term_id);
    }

    // Get basic counts
    const basicStatsQuery = `
      SELECT
        (SELECT COUNT(*) FROM o_level_students WHERE status = 'active') +
        (SELECT COUNT(*) FROM a_level_students WHERE status = 'active') as total_students,
        (SELECT COUNT(*) FROM teachers) as total_teachers,
        (SELECT COUNT(*) FROM classes) as total_classes,
        (SELECT COUNT(*) FROM o_level_subjects) + (SELECT COUNT(*) FROM a_level_subjects) as total_subjects,
        (SELECT COUNT(*) FROM academic_years) as total_academic_years,
        (SELECT COUNT(*) FROM terms) as total_terms
    `;
    
    const basicStatsResult = await executeQuery(basicStatsQuery);
    
    if (!basicStatsResult.success) {
      throw new Error('Failed to get basic statistics');
    }

    // Get student enrollment statistics
    const enrollmentStatsQuery = `
      SELECT
        (SELECT COUNT(*) FROM o_level_students WHERE status = 'active' AND current_class_id IS NOT NULL) +
        (SELECT COUNT(*) FROM a_level_students WHERE status = 'active' AND current_class_id IS NOT NULL) as total_enrollments,
        (SELECT COUNT(*) FROM o_level_students WHERE status = 'active' AND current_class_id IS NOT NULL) +
        (SELECT COUNT(*) FROM a_level_students WHERE status = 'active' AND current_class_id IS NOT NULL) as enrolled_students,
        (SELECT COUNT(DISTINCT current_class_id) FROM o_level_students WHERE status = 'active' AND current_class_id IS NOT NULL) +
        (SELECT COUNT(DISTINCT current_class_id) FROM a_level_students WHERE status = 'active' AND current_class_id IS NOT NULL) as classes_with_students
    `;
    
    const enrollmentStatsResult = await executeQuery(enrollmentStatsQuery, academicParams);

    // Get O-Level assessment statistics
    const oLevelAssessmentStatsQuery = `
      SELECT
        COUNT(*) as total_assessments,
        COUNT(DISTINCT student_id) as students_assessed,
        COUNT(DISTINCT subject_id) as subjects_assessed,
        COALESCE(AVG(competency_score), 0) as average_competency_score,
        COUNT(CASE WHEN competency_score >= 2.5 THEN 1 END) as excellent_assessments,
        COUNT(CASE WHEN competency_score >= 2.0 AND competency_score < 2.5 THEN 1 END) as good_assessments,
        COUNT(CASE WHEN competency_score >= 1.5 AND competency_score < 2.0 THEN 1 END) as satisfactory_assessments,
        COUNT(CASE WHEN competency_score < 1.5 THEN 1 END) as needs_improvement_assessments
      FROM o_level_subject_continuous_assessments_scores
      WHERE 1=1 ${academicFilter}
    `;

    // Get A-Level assessment statistics (using new paper-based system)
    const aLevelAssessmentStatsQuery = `
      SELECT
        COUNT(*) as total_assessments,
        COUNT(DISTINCT student_id) as students_assessed,
        COUNT(DISTINCT subject_paper_id) as subjects_assessed,
        COALESCE(AVG(competency_score), 0) as average_competency_score,
        COUNT(CASE WHEN competency_score >= 2.5 THEN 1 END) as excellent_assessments,
        COUNT(CASE WHEN competency_score >= 2.0 AND competency_score < 2.5 THEN 1 END) as good_assessments,
        COUNT(CASE WHEN competency_score >= 1.5 AND competency_score < 2.0 THEN 1 END) as satisfactory_assessments,
        COUNT(CASE WHEN competency_score < 1.5 THEN 1 END) as needs_improvement_assessments
      FROM a_level_paper_continuous_assessments_scores
      WHERE 1=1 ${academicFilter}
    `;

    const [oLevelAssessmentResult, aLevelAssessmentResult] = await Promise.all([
      executeQuery(oLevelAssessmentStatsQuery, academicParams),
      executeQuery(aLevelAssessmentStatsQuery, academicParams)
    ]);

    // Keep assessment statistics separate by level
    const oLevelStats = oLevelAssessmentResult.success ? oLevelAssessmentResult.data[0] : {
      total_assessments: 0,
      students_assessed: 0,
      subjects_assessed: 0,
      average_competency_score: 0,
      excellent_assessments: 0,
      good_assessments: 0,
      satisfactory_assessments: 0,
      needs_improvement_assessments: 0
    };

    const aLevelStats = aLevelAssessmentResult.success ? aLevelAssessmentResult.data[0] : {
      total_assessments: 0,
      students_assessed: 0,
      subjects_assessed: 0,
      average_competency_score: 0,
      excellent_assessments: 0,
      good_assessments: 0,
      satisfactory_assessments: 0,
      needs_improvement_assessments: 0
    };

    // Get gender distribution
    const genderStatsQuery = `
      SELECT
        SUM(male_count) as male_students,
        SUM(female_count) as female_students
      FROM (
        SELECT
          COUNT(CASE WHEN gender = 'Male' THEN 1 END) as male_count,
          COUNT(CASE WHEN gender = 'Female' THEN 1 END) as female_count
        FROM o_level_students
        WHERE status = 'active'
        UNION ALL
        SELECT
          COUNT(CASE WHEN gender = 'Male' THEN 1 END) as male_count,
          COUNT(CASE WHEN gender = 'Female' THEN 1 END) as female_count
        FROM a_level_students
        WHERE status = 'active'
      ) combined_stats
    `;
    
    const genderStatsResult = await executeQuery(genderStatsQuery);

    // Get class enrollment breakdown
    const classStatsQuery = `
      SELECT
        c.name as class_name,
        cl.name as level_name,
        COALESCE(student_counts.student_count, 0) as student_count
      FROM classes c
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN (
        SELECT current_class_id, COUNT(*) as student_count
        FROM o_level_students
        WHERE status = 'active' AND current_class_id IS NOT NULL
        GROUP BY current_class_id
        UNION ALL
        SELECT current_class_id, COUNT(*) as student_count
        FROM a_level_students
        WHERE status = 'active' AND current_class_id IS NOT NULL
        GROUP BY current_class_id
      ) student_counts ON c.id = student_counts.current_class_id
      GROUP BY c.id, c.name, cl.name
      ORDER BY cl.sort_order, c.name
    `;

    const classStatsResult = await executeQuery(classStatsQuery, academicFilter ? academicParams : []);

    // Get current academic context
    const academicContextQuery = `
      SELECT
        (SELECT JSON_OBJECT('id', id, 'name', name, 'start_date', start_date, 'end_date', end_date)
         FROM academic_years WHERE is_active = TRUE LIMIT 1) as current_academic_year,
        (SELECT JSON_OBJECT('id', id, 'name', name, 'number', number, 'start_date', start_date, 'end_date', end_date)
         FROM terms WHERE is_active = TRUE LIMIT 1) as current_term
    `;
    
    const academicContextResult = await executeQuery(academicContextQuery);

    // Compile all statistics
    const dashboardStats = {
      overview: basicStatsResult.data[0],
      enrollments: enrollmentStatsResult.success ? enrollmentStatsResult.data[0] : {
        total_enrollments: 0,
        enrolled_students: 0,
        classes_with_students: 0
      },
      assessments: {
        o_level: oLevelStats,
        a_level: aLevelStats
      },
      gender_distribution: genderStatsResult.success ? genderStatsResult.data[0] : {
        male_students: 0,
        female_students: 0
      },
      class_breakdown: classStatsResult.success ? classStatsResult.data : [],
      academic_context: academicContextResult.success ? academicContextResult.data[0] : {
        current_academic_year: null,
        current_term: null
      }
    };

    res.json({
      success: true,
      data: dashboardStats
    });

  } catch (error) {
    console.error('Get dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve dashboard statistics'
    });
  }
});

// Get performance trends
router.get('/performance-trends', async (req, res) => {
  try {
    const { academic_year_id, subject_id, class_id } = req.query;
    
    let whereClause = 'WHERE 1=1';
    let params = [];
    
    if (academic_year_id) {
      whereClause += ' AND ca.academic_year_id = ?';
      params.push(academic_year_id);
    }
    
    if (subject_id) {
      whereClause += ' AND ca.subject_id = ?';
      params.push(subject_id);
    }
    
    if (class_id) {
      whereClause += ' AND se.class_id = ?';
      params.push(class_id);
    }

    // Get O-Level trends
    const oLevelTrendsQuery = `
      SELECT
        t.name as term_name,
        t.number as term_number,
        COUNT(ca.id) as total_assessments,
        AVG(ca.competency_score) as average_score,
        COUNT(CASE WHEN ca.competency_score >= 2.5 THEN 1 END) as excellent_count,
        COUNT(CASE WHEN ca.competency_score >= 2.0 AND ca.competency_score < 2.5 THEN 1 END) as good_count,
        COUNT(CASE WHEN ca.competency_score >= 1.5 AND ca.competency_score < 2.0 THEN 1 END) as satisfactory_count,
        COUNT(CASE WHEN ca.competency_score < 1.5 THEN 1 END) as needs_improvement_count,
        'o_level' as level_type
      FROM o_level_subject_continuous_assessments_scores ca
      JOIN terms t ON ca.term_id = t.id
      JOIN o_level_students s ON ca.student_id = s.id
      ${whereClause} AND s.status = 'active'
      GROUP BY t.id, t.name, t.number
      ORDER BY t.number
    `;

    // Get A-Level trends (using new paper-based system)
    const aLevelTrendsQuery = `
      SELECT
        t.name as term_name,
        t.number as term_number,
        COUNT(ca.id) as total_assessments,
        AVG(ca.competency_score) as average_score,
        COUNT(CASE WHEN ca.competency_score >= 2.5 THEN 1 END) as excellent_count,
        COUNT(CASE WHEN ca.competency_score >= 2.0 AND ca.competency_score < 2.5 THEN 1 END) as good_count,
        COUNT(CASE WHEN ca.competency_score >= 1.5 AND ca.competency_score < 2.0 THEN 1 END) as satisfactory_count,
        COUNT(CASE WHEN ca.competency_score < 1.5 THEN 1 END) as needs_improvement_count,
        'a_level' as education_level_code
      FROM a_level_paper_continuous_assessments_scores ca
      JOIN terms t ON ca.term_id = t.id
      JOIN a_level_students s ON ca.student_id = s.id
      ${whereClause} AND s.status = 'active'
      GROUP BY t.id, t.name, t.number
      ORDER BY t.number
    `;

    const [oLevelResult, aLevelResult] = await Promise.all([
      executeQuery(oLevelTrendsQuery, params),
      executeQuery(aLevelTrendsQuery, params)
    ]);

    const trendsData = {
      o_level: oLevelResult.success ? oLevelResult.data : [],
      a_level: aLevelResult.success ? aLevelResult.data : []
    };

    res.json({
      success: true,
      data: trendsData
    });

  } catch (error) {
    console.error('Get performance trends error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve performance trends'
    });
  }
});

// Get subject performance summary
router.get('/subject-performance', async (req, res) => {
  try {
    const { academic_year_id, term_id, class_id } = req.query;
    
    let whereClause = 'WHERE 1=1';
    let params = [];
    
    if (academic_year_id) {
      whereClause += ' AND ca.academic_year_id = ?';
      params.push(academic_year_id);
    }
    
    if (term_id) {
      whereClause += ' AND ca.term_id = ?';
      params.push(term_id);
    }
    
    if (class_id) {
      whereClause += ' AND se.class_id = ?';
      params.push(class_id);
    }

    // Get O-Level subject performance
    const oLevelSubjectQuery = `
      SELECT
        s.name as subject_name,
        s.short_name as subject_short_name,
        COUNT(ca.id) as total_assessments,
        COUNT(DISTINCT ca.student_id) as students_assessed,
        AVG(ca.competency_score) as average_score,
        COUNT(CASE WHEN ca.competency_score >= 2.5 THEN 1 END) as excellent_count,
        COUNT(CASE WHEN ca.competency_score >= 2.0 AND ca.competency_score < 2.5 THEN 1 END) as good_count,
        COUNT(CASE WHEN ca.competency_score >= 1.5 AND ca.competency_score < 2.0 THEN 1 END) as satisfactory_count,
        COUNT(CASE WHEN ca.competency_score < 1.5 THEN 1 END) as needs_improvement_count,
        'o_level' as level_type
      FROM o_level_subject_continuous_assessments_scores ca
      JOIN o_level_subjects s ON ca.subject_id = s.id
      JOIN o_level_students st ON ca.student_id = st.id
      ${whereClause} AND st.status = 'active'
      GROUP BY s.id, s.name, s.short_name
      ORDER BY s.name
    `;

    // Get A-Level subject performance (using new paper-based system)
    const aLevelSubjectQuery = `
      SELECT
        s.name as subject_name,
        s.short_name as subject_short_name,
        COUNT(ca.id) as total_assessments,
        COUNT(DISTINCT ca.student_id) as students_assessed,
        AVG(ca.competency_score) as average_score,
        COUNT(CASE WHEN ca.competency_score >= 2.5 THEN 1 END) as excellent_count,
        COUNT(CASE WHEN ca.competency_score >= 2.0 AND ca.competency_score < 2.5 THEN 1 END) as good_count,
        COUNT(CASE WHEN ca.competency_score >= 1.5 AND ca.competency_score < 2.0 THEN 1 END) as satisfactory_count,
        COUNT(CASE WHEN ca.competency_score < 1.5 THEN 1 END) as needs_improvement_count,
        'a_level' as education_level_code
      FROM a_level_paper_continuous_assessments_scores ca
      JOIN a_level_subject_papers sp ON ca.subject_paper_id = sp.id
      JOIN a_level_subjects s ON sp.subject_id = s.id
      JOIN a_level_students st ON ca.student_id = st.id
      ${whereClause} AND st.status = 'active'
      GROUP BY s.id, s.name, s.short_name
      ORDER BY s.name
    `;

    const [oLevelSubjectResult, aLevelSubjectResult] = await Promise.all([
      executeQuery(oLevelSubjectQuery, params),
      executeQuery(aLevelSubjectQuery, params)
    ]);

    const subjectPerformanceData = {
      o_level: oLevelSubjectResult.success ? oLevelSubjectResult.data : [],
      a_level: aLevelSubjectResult.success ? aLevelSubjectResult.data : []
    };

    res.json({
      success: true,
      data: subjectPerformanceData
    });

  } catch (error) {
    console.error('Get subject performance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve subject performance'
    });
  }
});

module.exports = router;
