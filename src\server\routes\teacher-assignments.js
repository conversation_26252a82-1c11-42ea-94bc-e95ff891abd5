const express = require('express');
const router = express.Router();
const { executeQuery } = require('../../database/connection');

// Get all teacher assignments with optional filters
router.get('/', async (req, res) => {
  try {
    const { teacher_id, subject_level } = req.query;

    let query = `
      SELECT
        ts.id, ts.teacher_id, ts.subject_id, ts.subject_level, ts.academic_year_id,
        ts.assigned_date, ts.created_at, ts.updated_at,
        t.first_name, t.middle_name, t.last_name, t.initials, t.teacher_type,
        ay.name as academic_year_name,
        CASE
          WHEN ts.subject_level = 'o_level' THEN os.name
          WHEN ts.subject_level = 'a_level' THEN als.name
        END as subject_name,
        CASE
          WHEN ts.subject_level = 'o_level' THEN os.short_name
          WHEN ts.subject_level = 'a_level' THEN als.short_name
        END as subject_short_name,
        CASE
          WHEN ts.subject_level = 'a_level' THEN als.subject_type
          ELSE NULL
        END as subject_type
      FROM teacher_subjects ts
      INNER JOIN teachers t ON ts.teacher_id = t.id
      JOIN academic_years ay ON ts.academic_year_id = ay.id
      LEFT JOIN o_level_subjects os ON ts.subject_id = os.id AND ts.subject_level = 'o_level'
      LEFT JOIN a_level_subjects als ON ts.subject_id = als.id AND ts.subject_level = 'a_level'
      WHERE 1=1
    `;

    let params = [];

    if (teacher_id) {
      query += ' AND ts.teacher_id = ?';
      params.push(teacher_id);
    }

    if (subject_level) {
      query += ' AND ts.subject_level = ?';
      params.push(subject_level);
    }



    query += ' ORDER BY t.last_name, t.first_name, subject_name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get teacher assignments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve teacher assignments'
    });
  }
});

// Get teacher assignment by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT
        ts.id, ts.teacher_id, ts.subject_id, ts.subject_level,
        ts.assigned_date, ts.created_at, ts.updated_at,
        t.first_name, t.middle_name, t.last_name, t.initials, t.teacher_type,
        CASE 
          WHEN ts.subject_level = 'o_level' THEN os.name
          WHEN ts.subject_level = 'a_level' THEN als.name
        END as subject_name,
        CASE 
          WHEN ts.subject_level = 'o_level' THEN os.short_name
          WHEN ts.subject_level = 'a_level' THEN als.short_name
        END as subject_short_name,
        CASE 
          WHEN ts.subject_level = 'a_level' THEN als.subject_type
          ELSE NULL
        END as subject_type
      FROM teacher_subjects ts
      INNER JOIN teachers t ON ts.teacher_id = t.id
      LEFT JOIN o_level_subjects os ON ts.subject_id = os.id AND ts.subject_level = 'o_level'
      LEFT JOIN a_level_subjects als ON ts.subject_id = als.id AND ts.subject_level = 'a_level'
      WHERE ts.id = ?
    `;

    const result = await executeQuery(query, [id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher assignment not found'
      });
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get teacher assignment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve teacher assignment'
    });
  }
});

// Create new teacher assignment
router.post('/', async (req, res) => {
  try {
    const { teacher_id, subject_id, subject_level, academic_year_id, assigned_date } = req.body;

    // Validate required fields
    if (!teacher_id || !subject_id || !subject_level || !academic_year_id) {
      return res.status(400).json({
        success: false,
        message: 'Teacher ID, subject ID, subject level, and academic year ID are required'
      });
    }

    // Validate subject level
    if (!['o_level', 'a_level'].includes(subject_level)) {
      return res.status(400).json({
        success: false,
        message: 'Subject level must be either o_level or a_level'
      });
    }

    // Check if teacher exists
    const teacherCheck = await executeQuery('SELECT id FROM teachers WHERE id = ?', [teacher_id]);
    if (!teacherCheck.success || teacherCheck.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    // Business Logic Validation: Verify that the subject exists in the appropriate table
    const subjectTable = subject_level === 'o_level' ? 'o_level_subjects' : 'a_level_subjects';
    const subjectCheck = await executeQuery(`SELECT id FROM ${subjectTable} WHERE id = ?`, [subject_id]);

    if (!subjectCheck.success || subjectCheck.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: `Subject not found in ${subject_level.replace('_', '-')} subjects`
      });
    }

    // Business Logic Validation: Check if assignment already exists for this academic year
    const existingCheck = await executeQuery(
      'SELECT id FROM teacher_subjects WHERE teacher_id = ? AND subject_id = ? AND subject_level = ? AND academic_year_id = ?',
      [teacher_id, subject_id, subject_level, academic_year_id]
    );

    if (existingCheck.success && existingCheck.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Teacher is already assigned to this subject for this academic year'
      });
    }

    // Insert new assignment
    const insertQuery = `
      INSERT INTO teacher_subjects (teacher_id, subject_id, subject_level, academic_year_id, assigned_date)
      VALUES (?, ?, ?, ?, ?)
    `;

    const insertResult = await executeQuery(insertQuery, [
      teacher_id, subject_id, subject_level, academic_year_id, assigned_date || new Date().toISOString().split('T')[0]
    ]);

    if (!insertResult.success) {
      throw new Error(insertResult.error);
    }

    // Get the created assignment
    const newAssignmentQuery = `
      SELECT
        ts.id, ts.teacher_id, ts.subject_id, ts.subject_level,
        ts.assigned_date, ts.created_at, ts.updated_at,
        t.first_name, t.middle_name, t.last_name, t.initials, t.teacher_type,
        CASE 
          WHEN ts.subject_level = 'o_level' THEN os.name
          WHEN ts.subject_level = 'a_level' THEN als.name
        END as subject_name,
        CASE 
          WHEN ts.subject_level = 'o_level' THEN os.short_name
          WHEN ts.subject_level = 'a_level' THEN als.short_name
        END as subject_short_name,
        CASE 
          WHEN ts.subject_level = 'a_level' THEN als.subject_type
          ELSE NULL
        END as subject_type
      FROM teacher_subjects ts
      INNER JOIN teachers t ON ts.teacher_id = t.id
      LEFT JOIN o_level_subjects os ON ts.subject_id = os.id AND ts.subject_level = 'o_level'
      LEFT JOIN a_level_subjects als ON ts.subject_id = als.id AND ts.subject_level = 'a_level'
      WHERE ts.id = ?
    `;

    const newAssignmentResult = await executeQuery(newAssignmentQuery, [insertResult.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'Teacher assignment created successfully',
      data: newAssignmentResult.data[0]
    });

  } catch (error) {
    console.error('Create teacher assignment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create teacher assignment'
    });
  }
});

// Bulk create teacher assignments
router.post('/bulk', async (req, res) => {
  try {
    const { assignments } = req.body;

    if (!assignments || !Array.isArray(assignments) || assignments.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Assignments array is required'
      });
    }

    const results = [];
    const errors = [];

    for (let i = 0; i < assignments.length; i++) {
      const assignment = assignments[i];
      const { teacher_id, subject_id, subject_level, assigned_date } = assignment;

      try {
        // Validate required fields
        if (!teacher_id || !subject_id || !subject_level) {
          errors.push({
            index: i,
            assignment,
            error: 'Teacher ID, subject ID, and subject level are required'
          });
          continue;
        }

        // Check if assignment already exists
        const existingCheck = await executeQuery(
          'SELECT id FROM teacher_subjects WHERE teacher_id = ? AND subject_id = ? AND subject_level = ?',
          [teacher_id, subject_id, subject_level]
        );

        if (existingCheck.success && existingCheck.data.length > 0) {
          errors.push({
            index: i,
            assignment,
            error: 'Teacher is already assigned to this subject'
          });
          continue;
        }

        // Insert assignment
        const insertQuery = `
          INSERT INTO teacher_subjects (teacher_id, subject_id, subject_level, assigned_date)
          VALUES (?, ?, ?, ?)
        `;

        const insertResult = await executeQuery(insertQuery, [
          teacher_id, subject_id, subject_level, assigned_date || new Date().toISOString().split('T')[0]
        ]);

        if (insertResult.success) {
          results.push({
            index: i,
            assignment_id: insertResult.data.insertId,
            assignment
          });
        } else {
          errors.push({
            index: i,
            assignment,
            error: insertResult.error
          });
        }

      } catch (error) {
        errors.push({
          index: i,
          assignment,
          error: error.message
        });
      }
    }

    res.status(201).json({
      success: true,
      message: `Processed ${assignments.length} assignments: ${results.length} successful, ${errors.length} failed`,
      data: {
        successful: results,
        failed: errors,
        summary: {
          total: assignments.length,
          successful: results.length,
          failed: errors.length
        }
      }
    });

  } catch (error) {
    console.error('Bulk create teacher assignments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create teacher assignments'
    });
  }
});

// Update teacher assignment
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { teacher_id, subject_id, subject_level, assigned_date } = req.body;

    // Check if assignment exists
    const checkQuery = 'SELECT id FROM teacher_subjects WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher assignment not found'
      });
    }

    // Validate required fields
    if (!teacher_id || !subject_id || !subject_level) {
      return res.status(400).json({
        success: false,
        message: 'Teacher ID, subject ID, and subject level are required'
      });
    }

    // Validate subject level
    if (!['o_level', 'a_level'].includes(subject_level)) {
      return res.status(400).json({
        success: false,
        message: 'Subject level must be either o_level or a_level'
      });
    }

    // Check if teacher exists
    const teacherCheck = await executeQuery('SELECT id FROM teachers WHERE id = ?', [teacher_id]);
    if (!teacherCheck.success || teacherCheck.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    // Check if subject exists
    const subjectTable = subject_level === 'o_level' ? 'o_level_subjects' : 'a_level_subjects';
    const subjectCheck = await executeQuery(`SELECT id FROM ${subjectTable} WHERE id = ?`, [subject_id]);
    if (!subjectCheck.success || subjectCheck.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Subject not found'
      });
    }

    // Check for duplicate assignment (excluding current one)
    const duplicateCheck = await executeQuery(
      'SELECT id FROM teacher_subjects WHERE teacher_id = ? AND subject_id = ? AND subject_level = ? AND id != ?',
      [teacher_id, subject_id, subject_level, id]
    );

    if (duplicateCheck.success && duplicateCheck.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Teacher is already assigned to this subject'
      });
    }

    // Update assignment
    const updateQuery = `
      UPDATE teacher_subjects SET
        teacher_id = ?, subject_id = ?, subject_level = ?, assigned_date = ?
      WHERE id = ?
    `;

    const updateResult = await executeQuery(updateQuery, [
      teacher_id, subject_id, subject_level, assigned_date, id
    ]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    // Get updated assignment
    const updatedAssignmentQuery = `
      SELECT
        ts.id, ts.teacher_id, ts.subject_id, ts.subject_level,
        ts.assigned_date, ts.created_at, ts.updated_at,
        t.first_name, t.middle_name, t.last_name, t.initials, t.teacher_type,
        CASE
          WHEN ts.subject_level = 'o_level' THEN os.name
          WHEN ts.subject_level = 'a_level' THEN als.name
        END as subject_name,
        CASE
          WHEN ts.subject_level = 'o_level' THEN os.short_name
          WHEN ts.subject_level = 'a_level' THEN als.short_name
        END as subject_short_name,
        CASE
          WHEN ts.subject_level = 'a_level' THEN als.subject_type
          ELSE NULL
        END as subject_type
      FROM teacher_subjects ts
      INNER JOIN teachers t ON ts.teacher_id = t.id
      LEFT JOIN o_level_subjects os ON ts.subject_id = os.id AND ts.subject_level = 'o_level'
      LEFT JOIN a_level_subjects als ON ts.subject_id = als.id AND ts.subject_level = 'a_level'
      WHERE ts.id = ?
    `;

    const updatedResult = await executeQuery(updatedAssignmentQuery, [id]);

    res.json({
      success: true,
      message: 'Teacher assignment updated successfully',
      data: updatedResult.data[0]
    });

  } catch (error) {
    console.error('Update teacher assignment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update teacher assignment'
    });
  }
});

// Delete teacher assignment
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if assignment exists
    const checkQuery = 'SELECT id FROM teacher_subjects WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher assignment not found'
      });
    }

    // Delete assignment
    const deleteQuery = 'DELETE FROM teacher_subjects WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'Teacher assignment deleted successfully'
    });

  } catch (error) {
    console.error('Delete teacher assignment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete teacher assignment'
    });
  }
});


// Get assignment form data (teachers and subjects for dropdowns)
router.get('/form-data', async (req, res) => {
  try {
    // Get active teachers
    const teachersQuery = `
      SELECT id, first_name, middle_name, last_name, initials, teacher_type
      FROM teachers
      WHERE employment_status = 'active'
      ORDER BY last_name, first_name
    `;

    // Get O-Level subjects
    const oLevelSubjectsQuery = `
      SELECT id, name, short_name, 'o_level' as level, subject_type
      FROM o_level_subjects
      WHERE is_active = TRUE
      ORDER BY name
    `;

    // Get A-Level subjects
    const aLevelSubjectsQuery = `
      SELECT id, name, short_name, 'a_level' as level, subject_type
      FROM a_level_subjects
      WHERE is_active = TRUE
      ORDER BY name
    `;

    const [teachersResult, oLevelResult, aLevelResult] = await Promise.all([
      executeQuery(teachersQuery),
      executeQuery(oLevelSubjectsQuery),
      executeQuery(aLevelSubjectsQuery)
    ]);

    if (!teachersResult.success || !oLevelResult.success || !aLevelResult.success) {
      throw new Error('Failed to fetch form data');
    }

    // Combine subjects
    const allSubjects = [
      ...(oLevelResult.data || []),
      ...(aLevelResult.data || [])
    ];

    res.json({
      success: true,
      data: {
        teachers: teachersResult.data || [],
        subjects: allSubjects,
        subject_levels: [
          { value: 'o_level', label: 'O-Level' },
          { value: 'a_level', label: 'A-Level' }
        ]
      }
    });

  } catch (error) {
    console.error('Get assignment form data error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve assignment form data'
    });
  }
});

// Update teacher assignment
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { teacher_id, subject_id, subject_level, assigned_date } = req.body;

    // Check if assignment exists
    const checkQuery = 'SELECT id FROM teacher_subjects WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher assignment not found'
      });
    }

    // Validate required fields
    if (!teacher_id || !subject_id || !subject_level) {
      return res.status(400).json({
        success: false,
        message: 'Teacher ID, subject ID, and subject level are required'
      });
    }

    // Validate subject level
    if (!['o_level', 'a_level'].includes(subject_level)) {
      return res.status(400).json({
        success: false,
        message: 'Subject level must be either o_level or a_level'
      });
    }

    // Check if teacher exists
    const teacherCheck = await executeQuery('SELECT id FROM teachers WHERE id = ?', [teacher_id]);
    if (!teacherCheck.success || teacherCheck.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    // Check if subject exists
    const subjectTable = subject_level === 'o_level' ? 'o_level_subjects' : 'a_level_subjects';
    const subjectCheck = await executeQuery(`SELECT id FROM ${subjectTable} WHERE id = ?`, [subject_id]);
    if (!subjectCheck.success || subjectCheck.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Subject not found'
      });
    }

    // Check for duplicate assignment (excluding current one)
    const duplicateCheck = await executeQuery(
      'SELECT id FROM teacher_subjects WHERE teacher_id = ? AND subject_id = ? AND subject_level = ? AND id != ?',
      [teacher_id, subject_id, subject_level, id]
    );

    if (duplicateCheck.success && duplicateCheck.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Teacher is already assigned to this subject'
      });
    }

    // Update assignment
    const updateQuery = `
      UPDATE teacher_subjects SET
        teacher_id = ?, subject_id = ?, subject_level = ?, assigned_date = ?
      WHERE id = ?
    `;

    const updateResult = await executeQuery(updateQuery, [
      teacher_id, subject_id, subject_level, assigned_date, id
    ]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    // Get updated assignment
    const updatedAssignmentQuery = `
      SELECT
        ts.id, ts.teacher_id, ts.subject_id, ts.subject_level,
        ts.assigned_date, ts.created_at, ts.updated_at,
        t.first_name, t.middle_name, t.last_name, t.initials, t.teacher_type,
        CASE
          WHEN ts.subject_level = 'o_level' THEN os.name
          WHEN ts.subject_level = 'a_level' THEN als.name
        END as subject_name,
        CASE
          WHEN ts.subject_level = 'o_level' THEN os.short_name
          WHEN ts.subject_level = 'a_level' THEN als.short_name
        END as subject_short_name,
        CASE
          WHEN ts.subject_level = 'a_level' THEN als.subject_type
          ELSE NULL
        END as subject_type
      FROM teacher_subjects ts
      INNER JOIN teachers t ON ts.teacher_id = t.id
      LEFT JOIN o_level_subjects os ON ts.subject_id = os.id AND ts.subject_level = 'o_level'
      LEFT JOIN a_level_subjects als ON ts.subject_id = als.id AND ts.subject_level = 'a_level'
      WHERE ts.id = ?
    `;

    const updatedResult = await executeQuery(updatedAssignmentQuery, [id]);

    res.json({
      success: true,
      message: 'Teacher assignment updated successfully',
      data: updatedResult.data[0]
    });

  } catch (error) {
    console.error('Update teacher assignment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update teacher assignment'
    });
  }
});

// Delete teacher assignment
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if assignment exists
    const checkQuery = 'SELECT id FROM teacher_subjects WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher assignment not found'
      });
    }

    // Delete assignment
    const deleteQuery = 'DELETE FROM teacher_subjects WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'Teacher assignment deleted successfully'
    });

  } catch (error) {
    console.error('Delete teacher assignment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete teacher assignment'
    });
  }
});


module.exports = router;
