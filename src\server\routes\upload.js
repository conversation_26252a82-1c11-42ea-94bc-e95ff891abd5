const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Ensure upload directories exist
const uploadDir = path.join(__dirname, '../../assets/images/uploads');
const directories = {
  school: path.join(uploadDir, 'school'),
  teachers: path.join(uploadDir, 'teachers'),
  'o-level-students': path.join(uploadDir, 'o-level-students'),
  'a-level-students': path.join(uploadDir, 'a-level-students'),
  'system-users': path.join(uploadDir, 'system-users')
};

// Create all directories if they don't exist
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

Object.values(directories).forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Configure multer for dynamic uploads
const createStorage = (uploadType) => {
  return multer.diskStorage({
    destination: function (req, file, cb) {
      const dir = directories[uploadType];
      if (!dir) {
        return cb(new Error(`Invalid upload type: ${uploadType}`));
      }
      cb(null, dir);
    },
    filename: function (req, file, cb) {
      // Generate unique filename with timestamp
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      const extension = path.extname(file.originalname);
      cb(null, `${uploadType}-${uniqueSuffix}${extension}`);
    }
  });
};

// File filter for JPG and PNG images only
const imageFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Only JPG and PNG image files are allowed!'), false);
  }
};

// Create upload middleware for different types
const createUploadMiddleware = (uploadType) => {
  return multer({
    storage: createStorage(uploadType),
    fileFilter: imageFilter,
    limits: {
      fileSize: 2 * 1024 * 1024 // 2MB limit
    }
  });
};

// Generic upload endpoint
const handleUpload = (uploadType, fieldName = 'image') => {
  return (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'No file uploaded'
        });
      }

      // Generate the web-accessible path
      const filePath = `/assets/images/uploads/${uploadType}/${req.file.filename}`;

      res.json({
        success: true,
        message: `${uploadType} image uploaded successfully`,
        filePath: filePath,
        originalName: req.file.originalname,
        size: req.file.size
      });

    } catch (error) {
      console.error(`${uploadType} upload error:`, error);
      res.status(500).json({
        success: false,
        message: `Failed to upload ${uploadType} image`,
        error: error.message
      });
    }
  };
};

// Upload endpoints for different types (based on actual database schema)
router.post('/school-logo', authenticateToken, createUploadMiddleware('school').single('logo'), handleUpload('school'));
router.post('/teacher-photo', authenticateToken, createUploadMiddleware('teachers').single('photo'), handleUpload('teachers'));
router.post('/o-level-student-photo', authenticateToken, createUploadMiddleware('o-level-students').single('photo'), handleUpload('o-level-students'));
router.post('/a-level-student-photo', authenticateToken, createUploadMiddleware('a-level-students').single('photo'), handleUpload('a-level-students'));
router.post('/system-user-photo', authenticateToken, createUploadMiddleware('system-users').single('photo'), handleUpload('system-users'));

// Error handling middleware for multer
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'File too large. Maximum size is 2MB.'
      });
    }
  }
  
  if (error.message === 'Only JPG and PNG image files are allowed!') {
    return res.status(400).json({
      success: false,
      message: 'Only JPG and PNG image files are allowed.'
    });
  }

  res.status(500).json({
    success: false,
    message: 'Upload failed',
    error: error.message
  });
});

module.exports = router;
