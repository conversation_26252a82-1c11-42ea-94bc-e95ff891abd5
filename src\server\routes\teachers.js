const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');
const { requireAcademicContext } = require('../middleware/validation');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get all teachers with optional filters
router.get('/', async (req, res) => {
  try {
    const { teacher_type, employment_status } = req.query;

    let query = `
      SELECT
        t.id, t.first_name, t.middle_name, t.last_name, t.initials, t.gender,
        t.teacher_type, t.employment_status, t.joining_date, t.passport_photo,
        t.created_at, t.updated_at,
        GROUP_CONCAT(
          CONCAT(
            ts.subject_id, ':', ts.subject_level, ':',
            CASE
              WHEN ts.subject_level = 'o_level' THEN os.name
              WHEN ts.subject_level = 'a_level' THEN als.name
            END
          ) SEPARATOR '|'
        ) as assigned_subjects
      FROM teachers t
      LEFT JOIN teacher_subjects ts ON t.id = ts.teacher_id
      LEFT JOIN o_level_subjects os ON ts.subject_id = os.id AND ts.subject_level = 'o_level'
      LEFT JOIN a_level_subjects als ON ts.subject_id = als.id AND ts.subject_level = 'a_level'
      WHERE 1=1
    `;

    let params = [];

    if (teacher_type) {
      query += ' AND t.teacher_type = ?';
      params.push(teacher_type);
    }

    if (employment_status) {
      query += ' AND t.employment_status = ?';
      params.push(employment_status);
    }



    query += ' GROUP BY t.id ORDER BY t.last_name, t.first_name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get teachers error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve teachers'
    });
  }
});

// Get teacher by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT t.*
      FROM teachers t
      WHERE t.id = ?
    `;

    const result = await executeQuery(query, [id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get teacher error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve teacher'
    });
  }
});

// Create new teacher
router.post('/', requireAcademicContext(), async (req, res) => {
  try {
    const {
      first_name, middle_name, last_name, initials, gender,
      teacher_type, joining_date, employment_status, passport_photo,
      subject_assignments, assigned_class_id
    } = req.body;

    // Validate required fields
    if (!first_name || !last_name || !gender || !teacher_type) {
      return res.status(400).json({
        success: false,
        message: 'First name, last name, gender, and teacher type are required'
      });
    }

    // Insert new teacher
    const insertQuery = `
      INSERT INTO teachers (
        first_name, middle_name, last_name, initials, gender,
        teacher_type, joining_date, employment_status, passport_photo
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const insertParams = [
      first_name, middle_name, last_name, initials, gender,
      teacher_type, joining_date, employment_status || 'active',
      (passport_photo && typeof passport_photo === 'string' && passport_photo.trim() !== '') ? passport_photo : null
    ];



    const insertResult = await executeQuery(insertQuery, insertParams);

    if (!insertResult.success) {
      throw new Error(insertResult.error);
    }

    const teacherId = insertResult.data.insertId;

    // Handle subject assignments if provided
    if (subject_assignments && Array.isArray(subject_assignments) && subject_assignments.length > 0) {
      for (const assignment of subject_assignments) {
        const { subject_id, subject_level } = assignment;

        // Validate assignment data
        if (!subject_id || !subject_level || !['o_level', 'a_level'].includes(subject_level)) {
          console.warn(`Invalid subject assignment for teacher ${teacherId}:`, assignment);
          continue;
        }

        // Check if subject exists
        const subjectTable = subject_level === 'o_level' ? 'o_level_subjects' : 'a_level_subjects';
        const subjectCheck = await executeQuery(`SELECT id FROM ${subjectTable} WHERE id = ?`, [subject_id]);

        if (!subjectCheck.success || subjectCheck.data.length === 0) {
          console.warn(`Subject ${subject_id} not found for level ${subject_level}`);
          continue;
        }

        // Insert subject assignment
        const assignmentQuery = `
          INSERT INTO teacher_subjects (teacher_id, subject_id, subject_level)
          VALUES (?, ?, ?)
        `;

        const assignmentResult = await executeQuery(assignmentQuery, [
          teacherId, subject_id, subject_level
        ]);

        if (!assignmentResult.success) {
          console.error(`Failed to assign subject ${subject_id} to teacher ${teacherId}:`, assignmentResult.error);
        }
      }
    }

    // Handle class assignment for Class Teachers
    if (teacher_type === 'Class Teacher' && assigned_class_id) {
      try {
        // Business Logic Validation 1: Check if class already has a Class Teacher
        const classTeacherCheck = await executeQuery(
          'SELECT t.first_name, t.last_name FROM class_teacher_assignments tca JOIN teachers t ON tca.teacher_id = t.id WHERE tca.class_id = ?',
          [assigned_class_id]
        );

        if (classTeacherCheck.success && classTeacherCheck.data.length > 0) {
          const existingTeacher = classTeacherCheck.data[0];
          return res.status(409).json({
            success: false,
            message: `This class already has a Class Teacher assigned: ${existingTeacher.first_name} ${existingTeacher.last_name}`,
            error: 'CLASS_TEACHER_ALREADY_EXISTS'
          });
        }

        // Business Logic Validation 2: Check if this teacher is already a Class Teacher for another class
        const teacherClassCheck = await executeQuery(
          'SELECT c.name as class_name FROM class_teacher_assignments tca JOIN classes c ON tca.class_id = c.id WHERE tca.teacher_id = ?',
          [teacherId]
        );

        if (teacherClassCheck.success && teacherClassCheck.data.length > 0) {
          const existingClass = teacherClassCheck.data[0];
          return res.status(409).json({
            success: false,
            message: `This teacher is already assigned as Class Teacher for ${existingClass.class_name}. A teacher can only be Class Teacher for one class.`,
            error: 'TEACHER_ALREADY_CLASS_TEACHER'
          });
        }

        // Insert class assignment
        const classAssignmentQuery = `
          INSERT INTO class_teacher_assignments (teacher_id, class_id)
          VALUES (?, ?)
        `;

        const classAssignmentResult = await executeQuery(classAssignmentQuery, [
          teacherId, assigned_class_id
        ]);

        if (!classAssignmentResult.success) {
          console.error(`Failed to assign class ${assigned_class_id} to teacher ${teacherId}:`, classAssignmentResult.error);
          return res.status(500).json({
            success: false,
            message: 'Failed to assign class to teacher',
            error: 'CLASS_ASSIGNMENT_FAILED'
          });
        }
      } catch (error) {
        console.error('Class assignment error:', error);
        // Don't fail the entire teacher creation if class assignment fails
      }
    }

    // Get the created teacher with assignments
    const newTeacherQuery = `
      SELECT
        t.*,
        GROUP_CONCAT(
          CONCAT(
            ts.subject_id, ':', ts.subject_level, ':',
            CASE
              WHEN ts.subject_level = 'o_level' THEN os.name
              WHEN ts.subject_level = 'a_level' THEN als.name
            END
          ) SEPARATOR '|'
        ) as assigned_subjects
      FROM teachers t
      LEFT JOIN teacher_subjects ts ON t.id = ts.teacher_id
      LEFT JOIN o_level_subjects os ON ts.subject_id = os.id AND ts.subject_level = 'o_level'
      LEFT JOIN a_level_subjects als ON ts.subject_id = als.id AND ts.subject_level = 'a_level'
      WHERE t.id = ?
      GROUP BY t.id
    `;

    const newTeacherResult = await executeQuery(newTeacherQuery, [teacherId]);

    res.status(201).json({
      success: true,
      message: 'Teacher created successfully',
      data: newTeacherResult.data[0]
    });

  } catch (error) {
    console.error('Create teacher error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create teacher'
    });
  }
});

// Update teacher
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      first_name, middle_name, last_name, initials, gender,
      teacher_type, joining_date, employment_status, passport_photo
    } = req.body;

    // Check if teacher exists and get current data
    const checkQuery = 'SELECT id, teacher_type FROM teachers WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    const currentTeacher = checkResult.data[0];

    // Validate required fields
    if (!first_name || !last_name || !gender || !teacher_type) {
      return res.status(400).json({
        success: false,
        message: 'First name, last name, gender, and teacher type are required'
      });
    }

    // Business Logic Validation: Handle teacher type changes
    if (currentTeacher.teacher_type !== teacher_type) {
      // Check if teacher has class assignments
      const classAssignmentCheck = await executeQuery(
        'SELECT c.name as class_name FROM class_teacher_assignments tca JOIN classes c ON tca.class_id = c.id WHERE tca.teacher_id = ?',
        [id]
      );

      if (classAssignmentCheck.success && classAssignmentCheck.data.length > 0) {
        const assignedClasses = classAssignmentCheck.data.map(row => row.class_name).join(', ');

        if (teacher_type !== 'Class Teacher') {
          // Changing from Class Teacher to Subject Teacher, but has class assignments
          return res.status(409).json({
            success: false,
            message: `Cannot change teacher type from Class Teacher to ${teacher_type}. This teacher is currently assigned to class(es): ${assignedClasses}. Please remove class assignments first.`,
            error: 'TEACHER_HAS_CLASS_ASSIGNMENTS'
          });
        }
      }
    }

    // Update teacher
    const updateQuery = `
      UPDATE teachers SET
        first_name = ?, middle_name = ?, last_name = ?, initials = ?, gender = ?,
        teacher_type = ?, joining_date = ?, employment_status = ?, passport_photo = ?
      WHERE id = ?
    `;

    const updateResult = await executeQuery(updateQuery, [
      first_name, middle_name, last_name, initials, gender,
      teacher_type, joining_date, employment_status,
      (passport_photo && typeof passport_photo === 'string' && passport_photo.trim() !== '') ? passport_photo : null,
      id
    ]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    // Get updated teacher
    const updatedTeacherQuery = 'SELECT * FROM teachers WHERE id = ?';
    const updatedTeacherResult = await executeQuery(updatedTeacherQuery, [id]);

    res.json({
      success: true,
      message: 'Teacher updated successfully',
      data: updatedTeacherResult.data[0]
    });

  } catch (error) {
    console.error('Update teacher error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update teacher'
    });
  }
});

// Delete teacher
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if teacher exists
    const checkQuery = 'SELECT id FROM teachers WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    // Note: Assignment checking not implemented in current schema
    // Teachers can be deleted without assignment validation

    // Delete teacher
    const deleteQuery = 'DELETE FROM teachers WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'Teacher deleted successfully'
    });

  } catch (error) {
    console.error('Delete teacher error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete teacher'
    });
  }
});

// Get teacher statistics
router.get('/stats/overview', async (req, res) => {
  try {
    const statsQuery = `
      SELECT
        COUNT(*) as total_teachers,
        COUNT(CASE WHEN teacher_type = 'Class Teacher' THEN 1 END) as class_teachers,
        COUNT(CASE WHEN teacher_type = 'Subject Teacher' THEN 1 END) as subject_teachers,
        COUNT(CASE WHEN employment_status = 'active' THEN 1 END) as active_teachers,
        COUNT(CASE WHEN employment_status = 'inactive' THEN 1 END) as inactive_teachers
      FROM teachers
    `;

    const result = await executeQuery(statsQuery);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get teacher stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve teacher statistics'
    });
  }
});

// Get registration form data (for dropdowns and form setup)
router.get('/registration-form-data', async (req, res) => {
  try {
    // Get O-Level and A-Level subjects for teacher specialization
    const oLevelSubjectsQuery = `
      SELECT id, name, short_name, 'o_level' as level, subject_type
      FROM o_level_subjects
      WHERE is_active = TRUE
      ORDER BY name
    `;

    const aLevelSubjectsQuery = `
      SELECT id, name, short_name, 'a_level' as level, subject_type
      FROM a_level_subjects
      WHERE is_active = TRUE
      ORDER BY name
    `;

    // Get available classes for Class Teacher assignment
    const classesQuery = `
      SELECT
        c.id, c.name, cl.name as class_level_name, s.name as stream_name,
        c.is_active
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN streams s ON c.stream_id = s.id
      WHERE c.is_active = TRUE
      ORDER BY cl.sort_order, s.name
    `;

    const [oLevelResult, aLevelResult, classesResult] = await Promise.all([
      executeQuery(oLevelSubjectsQuery),
      executeQuery(aLevelSubjectsQuery),
      executeQuery(classesQuery)
    ]);

    if (!oLevelResult.success || !aLevelResult.success || !classesResult.success) {
      throw new Error('Failed to fetch form data');
    }

    // Combine subjects
    const allSubjects = [
      ...(oLevelResult.data || []),
      ...(aLevelResult.data || [])
    ];

    // Prepare classes data
    const classes = (classesResult.data || []).map(cls => ({
      id: cls.id,
      name: cls.name,
      stream_name: cls.stream_name,
      is_active: cls.is_active
    }));

    // Prepare form data
    const formData = {
      subjects: allSubjects,
      classes: classes,
      teacher_types: [
        { value: 'Class Teacher', label: 'Class Teacher' },
        { value: 'Subject Teacher', label: 'Subject Teacher' }
      ],
      employment_statuses: [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' }
      ],
      qualifications: [
        { value: 'certificate', label: 'Certificate' },
        { value: 'diploma', label: 'Diploma' },
        { value: 'degree', label: 'Bachelor\'s Degree' },
        { value: 'masters', label: 'Master\'s Degree' },
        { value: 'phd', label: 'PhD' }
      ]
    };

    res.json({
      success: true,
      data: formData
    });

  } catch (error) {
    console.error('Get registration form data error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve registration form data'
    });
  }
});

// Get teacher assignments (both class and subject assignments)
router.get('/:id/assignments', async (req, res) => {
  try {
    const { id } = req.params;

    // Get class assignments for Class Teachers
    const classAssignmentsQuery = `
      SELECT
        tca.id, tca.class_id, tca.academic_year_id, tca.assigned_date,
        c.name as class_name, cl.name as class_level_name, cl.display_name as class_level_display,
        s.name as stream_name, ay.name as academic_year_name
      FROM class_teacher_assignments tca
      JOIN classes c ON tca.class_id = c.id
      JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN streams s ON c.stream_id = s.id
      JOIN academic_years ay ON tca.academic_year_id = ay.id
      WHERE tca.teacher_id = ?
      ORDER BY ay.name DESC, cl.sort_order
    `;

    // Get subject assignments
    const subjectAssignmentsQuery = `
      SELECT
        ts.id, ts.subject_id, ts.subject_level, ts.academic_year_id, ts.assigned_date,
        CASE
          WHEN ts.subject_level = 'o_level' THEN os.name
          WHEN ts.subject_level = 'a_level' THEN als.name
        END as subject_name,
        CASE
          WHEN ts.subject_level = 'o_level' THEN os.short_name
          WHEN ts.subject_level = 'a_level' THEN als.short_name
        END as subject_short_name,
        ay.name as academic_year_name
      FROM teacher_subjects ts
      LEFT JOIN o_level_subjects os ON ts.subject_id = os.id AND ts.subject_level = 'o_level'
      LEFT JOIN a_level_subjects als ON ts.subject_id = als.id AND ts.subject_level = 'a_level'
      JOIN academic_years ay ON ts.academic_year_id = ay.id
      WHERE ts.teacher_id = ?
      ORDER BY ay.name DESC, subject_name
    `;

    const [classResult, subjectResult] = await Promise.all([
      executeQuery(classAssignmentsQuery, [id]),
      executeQuery(subjectAssignmentsQuery, [id])
    ]);

    if (!classResult.success || !subjectResult.success) {
      throw new Error('Failed to retrieve assignments');
    }

    res.json({
      success: true,
      data: {
        class_assignments: classResult.data,
        subject_assignments: subjectResult.data
      }
    });

  } catch (error) {
    console.error('Get teacher assignments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve teacher assignments'
    });
  }
});

// Assign Class Teacher to a class
router.post('/:id/assign-class', async (req, res) => {
  try {
    const { id } = req.params;
    const { class_id } = req.body;

    // Validate required fields
    if (!class_id) {
      return res.status(400).json({
        success: false,
        message: 'Class ID is required'
      });
    }

    // Check if teacher exists and is a Class Teacher
    const teacherCheck = await executeQuery(
      'SELECT teacher_type FROM teachers WHERE id = ?',
      [id]
    );

    if (!teacherCheck.success || teacherCheck.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    if (teacherCheck.data[0].teacher_type !== 'Class Teacher') {
      return res.status(400).json({
        success: false,
        message: 'Only Class Teachers can be assigned to classes'
      });
    }

    // Business Logic Validation 1: Check if teacher already has a class assignment
    const existingAssignmentCheck = await executeQuery(
      'SELECT c.name as class_name FROM class_teacher_assignments tca JOIN classes c ON tca.class_id = c.id WHERE tca.teacher_id = ?',
      [id]
    );

    if (existingAssignmentCheck.success && existingAssignmentCheck.data.length > 0) {
      const existingClass = existingAssignmentCheck.data[0];
      return res.status(409).json({
        success: false,
        message: `This teacher is already assigned as Class Teacher for ${existingClass.class_name}. A teacher can only be Class Teacher for one class.`,
        error: 'TEACHER_ALREADY_CLASS_TEACHER'
      });
    }

    // Business Logic Validation 2: Check if class already has a Class Teacher
    const classTeacherCheck = await executeQuery(
      'SELECT t.first_name, t.last_name FROM class_teacher_assignments tca JOIN teachers t ON tca.teacher_id = t.id WHERE tca.class_id = ?',
      [class_id]
    );

    if (classTeacherCheck.success && classTeacherCheck.data.length > 0) {
      const existingTeacher = classTeacherCheck.data[0];
      return res.status(409).json({
        success: false,
        message: `This class already has a Class Teacher assigned: ${existingTeacher.first_name} ${existingTeacher.last_name}`,
        error: 'CLASS_TEACHER_ALREADY_EXISTS'
      });
    }

    // Insert class assignment
    const assignQuery = `
      INSERT INTO class_teacher_assignments (teacher_id, class_id)
      VALUES (?, ?)
    `;

    const result = await executeQuery(assignQuery, [id, class_id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      message: 'Class Teacher assigned successfully',
      data: { assignment_id: result.data.insertId }
    });

  } catch (error) {
    console.error('Assign class teacher error:', error);
    res.status(500).json({
      success: false,
      message: error.message.includes('Class Teacher can only be assigned')
        ? error.message
        : 'Failed to assign Class Teacher'
    });
  }
});

// Remove Class Teacher assignment
router.delete('/:id/class-assignment/:assignment_id', async (req, res) => {
  try {
    const { id, assignment_id } = req.params;

    // Delete the assignment
    const deleteQuery = `
      DELETE FROM class_teacher_assignments
      WHERE id = ? AND teacher_id = ?
    `;

    const result = await executeQuery(deleteQuery, [assignment_id, id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class assignment not found'
      });
    }

    res.json({
      success: true,
      message: 'Class assignment removed successfully'
    });

  } catch (error) {
    console.error('Remove class assignment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove class assignment'
    });
  }
});

module.exports = router;
