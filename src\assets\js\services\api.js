// SmartReport API Service
// Centralized API service using environment configuration

// Use global Config 

class APIService {
  constructor() {
    this.baseURL = window.SRConfig.getApiUrl();
    this.timeout = window.SRConfig.get('api.timeout', 30000);
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
  }

  // Generic request method
  async request(endpoint, options = {}) {
    const url = window.SRConfig.getApiUrl(endpoint);

    // Get authentication token from localStorage
    const token = localStorage.getItem('smartreport_token');
    const authHeaders = token ? { 'Authorization': `Bearer ${token}` } : {};

    const config = {
      method: 'GET',
      headers: { ...this.defaultHeaders, ...authHeaders, ...options.headers },
      ...options
    };

    // Add timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);
    config.signal = controller.signal;

    try {
      if (window.SRConfig && window.SRConfig.get('development.debugMode')) {
        console.log(`API Request: ${config.method} ${url}`, config);
      }

      const response = await fetch(url, config);
      clearTimeout(timeoutId);

      const data = await response.json();

      if (!response.ok) {
        // Extract error message from server response if available
        const errorMessage = data.message || `HTTP error! status: ${response.status}`;
        const error = new Error(errorMessage);
        error.status = response.status;
        error.serverResponse = data;
        throw error;
      }

      if (window.SRConfig && window.SRConfig.get('development.debugMode')) {
        console.log(`API Response: ${config.method} ${url}`, data);
      }

      return data;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      
      console.error(`API Error: ${config.method} ${url}`, error);
      throw error;
    }
  }

  // GET request
  async get(endpoint, params = {}) {
    let url = endpoint;

    // Add query parameters if provided
    if (Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams();
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
          searchParams.append(key, params[key]);
        }
      });
      url += '?' + searchParams.toString();
    }

    return this.request(url);
  }

  // POST request
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  // PUT request
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  // PATCH request
  async patch(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data)
    });
  }

  // DELETE request
  async delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE'
    });
  }

  // File upload
  async uploadFile(endpoint, file, additionalData = {}) {
    const formData = new FormData();
    formData.append('file', file);
    
    Object.keys(additionalData).forEach(key => {
      formData.append(key, additionalData[key]);
    });

    return this.request(endpoint, {
      method: 'POST',
      headers: {}, // Let browser set Content-Type for FormData
      body: formData
    });
  }

  // Bulk operations
  async bulkOperation(endpoint, operation, data) {
    return this.post(endpoint, {
      operation,
      data
    });
  }
}

// Create singleton instance
const API = new APIService();

// Specific API methods for SR entities
const StudentsAPI = {
  // Combined methods that work with both O-Level and A-Level students
  getAll: (params = {}) => API.get('/students', params),
  getById: (id, level = null) => {
    if (level) {
      return API.get(`/students/${id}?level=${level}`);
    }
    return API.get(`/students/${id}`);
  },

  // O-Level specific methods
  oLevel: {
    getAll: (params = {}) => API.get('/students', { ...params, level: 'o_level' }),
    getById: (id) => API.get(`/students/${id}?level=o_level`),
    create: (data) => API.post('/students/o-level', data),
    update: (id, data) => API.put(`/students/o-level/${id}`, data),
    delete: (id) => API.delete(`/students/o-level/${id}`),
    uploadPhoto: (id, file) => API.uploadFile(`/students/o-level/${id}/photo`, file)
  },

  // A-Level specific methods
  aLevel: {
    getAll: (params = {}) => API.get('/students', { ...params, level: 'a_level' }),
    getById: (id) => API.get(`/students/${id}?level=a_level`),
    create: (data) => API.post('/students/a-level', data),
    update: (id, data) => API.put(`/students/a-level/${id}`, data),
    delete: (id) => API.delete(`/students/a-level/${id}`),
    uploadPhoto: (id, file) => API.uploadFile(`/students/a-level/${id}/photo`, file)
  },

  // Convenience methods for registration forms
  registerOLevel: (data) => API.post('/students/register/o-level', data),
  registerALevel: (data) => API.post('/students/register/a-level', data),
  getRegistrationFormData: () => API.get('/students/registration-form-data'),

  // Subject management methods
  getStudentSubjects: (studentType, studentId) => API.get(`/students/${studentType}/${studentId}/subjects`),
  updateStudentSubjects: (studentType, studentId, data) => API.put(`/students/${studentType}/${studentId}/subjects`, data),


};

const EnrollmentsAPI = {
  getAll: (params = {}) => API.get('/enrollments', params),
  getById: (id) => API.get(`/enrollments/${id}`),
  getByClass: (classId, params = {}) => API.get('/enrollments', { ...params, class_id: classId }),
  create: (data) => API.post('/enrollments', data),
  update: (id, data) => API.put(`/enrollments/${id}`, data),
  updateStatus: (id, data) => API.put(`/enrollments/${id}/status`, data),
  promote: (id, data) => API.post(`/enrollments/${id}/promote`, data),
  fixConsistency: (data) => API.post('/enrollments/fix-consistency', data),
  getStatistics: (params = {}) => API.get('/enrollments/statistics', params),
  getStudentHistory: (studentId, studentType) => API.get(`/enrollments/student/${studentId}/${studentType}/history`),
  getAcademicStudentsForClass: (classId, params = {}) => API.get(`/enrollments/class/${classId}/academic-students`, params),
  getCounts: (params = {}) => API.get('/enrollments/counts', params),
  bulkEnroll: (data) => API.post('/enrollments/bulk', data),
  bulkPromote: (data) => API.post('/enrollments/bulk-promote', data),
  changeStream: (data) => API.put('/enrollments/change-stream', data)
};

const TeachersAPI = {
  getAll: (params = {}) => API.get('/teachers', params),
  getById: (id) => API.get(`/teachers/${id}`),
  create: (data) => API.post('/teachers', data),
  update: (id, data) => API.put(`/teachers/${id}`, data),
  delete: (id) => API.delete(`/teachers/${id}`),
  uploadPhoto: (id, file) => API.uploadFile(`/teachers/${id}/photo`, file)
};

const SubjectsAPI = {
  // O-Level subjects
  oLevel: {
    getAll: (params = {}) => API.get('/academic/subjects/o-level', params),
    getById: (id) => API.get(`/academic/subjects/o-level/${id}`),
    create: (data) => API.post('/academic/subjects/o-level', data),
    update: (id, data) => API.put(`/academic/subjects/o-level/${id}`, data),
    delete: (id) => API.delete(`/academic/subjects/o-level/${id}`),
    toggleStatus: (id) => API.patch(`/academic/subjects/o-level/${id}/toggle-status`)
  },
  // A-Level subjects
  aLevel: {
    getAll: (params = {}) => API.get('/academic/subjects/a-level', params),
    getById: (id) => API.get(`/academic/subjects/a-level/${id}`),
    create: (data) => API.post('/academic/subjects/a-level', data),
    update: (id, data) => API.put(`/academic/subjects/a-level/${id}`, data),
    delete: (id) => API.delete(`/academic/subjects/a-level/${id}`),
    toggleStatus: (id) => API.patch(`/academic/subjects/a-level/${id}/toggle-status`),

    // Subject papers endpoints
    getPapers: (subjectId) => API.get(`/academic/subjects/a-level/${subjectId}/papers`),
    getAllPapers: (params = {}) => API.get('/academic/subject-papers/a-level', params)
  },

};

const ClassesAPI = {
  getAll: (params = {}) => API.get('/academic/classes', params),
  getById: (id) => API.get(`/academic/classes/${id}`),
  create: (data) => API.post('/academic/classes', data),
  update: (id, data) => API.put(`/academic/classes/${id}`, data),
  delete: (id) => API.delete(`/academic/classes/${id}`)
};

const StreamsAPI = {
  getAll: (params = {}) => API.get('/academic/streams', params),
  getById: (id) => API.get(`/academic/streams/${id}`),
  getForClass: (classId) => {
    return API.get(`/academic/streams/class/${classId}`);
  },
  create: (data) => API.post('/academic/streams', data),
  update: (id, data) => API.put(`/academic/streams/${id}`, data),
  delete: (id) => API.delete(`/academic/streams/${id}`),


  // Stream assignment management
  getAssignments: (params = {}) => API.get('/academic/streams/assignments', params),
  assignToClass: (data) => API.post('/academic/streams/assign', data),
  assignMultipleToClass: (data) => API.post('/academic/streams/assign-multiple', data),
  removeAssignment: (assignmentId) => API.delete(`/academic/streams/assign/${assignmentId}`),

  // Student management in streams
  getStudentsInStream: (streamId, params = {}) => API.get(`/academic/streams/${streamId}/students`, params),
  transferStudents: (data) => API.post('/academic/streams/transfer-students', data)
  // Note: Consolidation now happens automatically
};



const AssignmentsAPI = {
  getAll: (params = {}) => API.get('/teacher-assignments', params),
  getById: (id) => API.get(`/teacher-assignments/${id}`),
  create: (data) => API.post('/teacher-assignments', data),
  bulkCreate: (assignments) => API.post('/teacher-assignments/bulk', { assignments }),
  update: (id, data) => API.put(`/teacher-assignments/${id}`, data),
  delete: (id) => API.delete(`/teacher-assignments/${id}`),

};

const AssessmentsAPI = {
  // O-Level continuous assessments
  oLevel: {
    getAll: (params = {}) => API.get('/assessments/continuous/o-level', params),
    getById: (id) => API.get(`/assessments/continuous/o-level/${id}`),
    create: (data) => API.post('/assessments/continuous/o-level', data),
    update: (id, data) => API.put(`/assessments/continuous/o-level/${id}`, data),
    delete: (id) => API.delete(`/assessments/continuous/o-level/${id}`)
  },
  // CA Summary endpoints
  caSummary: {
    oLevel: {
      getAll: (params = {}) => API.get('/assessments/ca-summary/o-level', params),
      createOrUpdate: (data) => API.post('/assessments/ca-summary/o-level', data)
    },
    aLevel: {
      principal: {
        getAll: (params = {}) => API.get('/assessments/ca-summary/a-level/principal', params),
        createOrUpdate: (data) => API.post('/assessments/ca-summary/a-level/principal', data)
      },
      subsidiary: {
        getAll: (params = {}) => API.get('/assessments/ca-summary/a-level/subsidiary', params),
        createOrUpdate: (data) => API.post('/assessments/ca-summary/a-level/subsidiary', data)
      }
    }
  },
  // A-Level continuous assessments - unified paper-based structure
  aLevel: {
    // Main methods using unified endpoint
    getAll: (params = {}) => API.get('/assessments/continuous/a-level', params),
    create: (data) => API.post('/assessments/continuous/a-level', data),
    update: (id, data) => API.put(`/assessments/continuous/a-level/${id}`, data),
    delete: (id) => API.delete(`/assessments/continuous/a-level/${id}`),

    // Principal subjects (legacy compatibility - filters by subject_type)
    principal: {
      getAll: (params = {}) => API.get('/assessments/continuous/a-level/principal', params),
      getById: (id) => API.get(`/assessments/continuous/a-level/principal/${id}`),
      create: (data) => API.post('/assessments/continuous/a-level', data), // Use unified endpoint
      update: (id, data) => API.put(`/assessments/continuous/a-level/${id}`, data), // Use unified endpoint
      delete: (id) => API.delete(`/assessments/continuous/a-level/${id}`) // Use unified endpoint
    },
    // Subsidiary subjects (legacy compatibility - filters by subject_type)
    subsidiary: {
      getAll: (params = {}) => API.get('/assessments/continuous/a-level/subsidiary', params),
      getById: (id) => API.get(`/assessments/continuous/a-level/subsidiary/${id}`),
      create: (data) => API.post('/assessments/continuous/a-level', data), // Use unified endpoint
      update: (id, data) => API.put(`/assessments/continuous/a-level/${id}`, data), // Use unified endpoint
      delete: (id) => API.delete(`/assessments/continuous/a-level/${id}`) // Use unified endpoint
    },


  },
  // Examinations
  examinations: {
    oLevel: {
      getAll: (params = {}) => API.get('/assessments/examinations/o-level', params)
    },
    aLevel: {
      getAll: (params = {}) => API.get('/assessments/examinations/a-level', params)
    }
  },
  // Statistics
  getStatistics: (params = {}) => API.get('/assessments/statistics', params)
};

const CAScoresAPI = {
  getAll: (params = {}) => API.get('/ca-scores', params),
  getStudents: (params) => API.get('/ca-scores/students', params),
  create: (scores) => API.post('/ca-scores', { scores }),
  update: (id, data) => API.put(`/ca-scores/${id}`, data),
  delete: (id) => API.delete(`/ca-scores/${id}`)
};

const ExamMarksAPI = {
  getAll: (params = {}) => API.get('/exam-marks', params),
  getStudents: (params) => API.get('/exam-marks/students', params),
  create: (grades) => API.post('/exam-marks', { grades }),
  update: (id, data) => API.put(`/exam-marks/${id}`, data),
  delete: (id) => API.delete(`/exam-marks/${id}`)
};

const GradeBoundariesAPI = {
  // O-Level grade boundaries
  oLevel: {
    getAll: (params = {}) => API.get('/academic/grade-boundaries/o-level', params),
    create: (boundary) => API.post('/academic/grade-boundaries/o-level', boundary),
    update: (boundaries) => API.put('/academic/grade-boundaries/o-level', { boundaries }),
    delete: (id) => API.delete(`/academic/grade-boundaries/o-level/${id}`),
    getHistory: () => API.get('/academic/grade-boundaries/o-level/history')
  },
  // A-Level Paper grade boundaries (UACE system)
  aLevelPaper: {
    getAll: (params = {}) => API.get('/academic/grade-boundaries/a-level-paper', params),
    create: (boundary) => API.post('/academic/grade-boundaries/a-level-paper', boundary),
    update: (boundaries) => {
      // Support both single boundary update and bulk update
      if (Array.isArray(boundaries)) {
        // Bulk update
        return API.put('/academic/grade-boundaries/a-level-paper', { boundaries });
      } else {
        // Single boundary update (legacy support)
        const id = arguments[0];
        const boundary = arguments[1];
        return API.put(`/academic/grade-boundaries/a-level-paper/${id}`, boundary);
      }
    },
    delete: (id) => API.delete(`/academic/grade-boundaries/a-level-paper/${id}`)
  },

};

const GradingScaleAPI = {
  // O-Level competency-based grading scale
  oLevel: {
    getAll: (params = {}) => API.get('/academic/grading-scale/o-level', params),
    getById: (id) => API.get(`/academic/grading-scale/o-level/${id}`),
    update: (id, data) => API.put(`/academic/grading-scale/o-level/${id}`, data),
    create: (data) => API.post('/academic/grading-scale/o-level', data),
    delete: (id) => API.delete(`/academic/grading-scale/o-level/${id}`),
    getHistory: () => API.get('/academic/grading-scale/o-level/history')
  },
  // A-Level Principal grading scale
  aLevelPrincipal: {
    getAll: (params = {}) => API.get('/academic/grading-scale/a-level-principal', params),
    getById: (id) => API.get(`/academic/grading-scale/a-level-principal/${id}`),
    update: (id, data) => API.put(`/academic/grading-scale/a-level-principal/${id}`, data),
    create: (data) => API.post('/academic/grading-scale/a-level-principal', data),
    delete: (id) => API.delete(`/academic/grading-scale/a-level-principal/${id}`)
  },
  // A-Level Subsidiary grading scale
  aLevelSubsidiary: {
    getAll: (params = {}) => API.get('/academic/grading-scale/a-level-subsidiary', params),
    getById: (id) => API.get(`/academic/grading-scale/a-level-subsidiary/${id}`),
    update: (id, data) => API.put(`/academic/grading-scale/a-level-subsidiary/${id}`, data)
  }
};



const AcademicYearsAPI = {
  getAll: (params = {}) => API.get('/academic/years', params),
  getById: (id) => API.get(`/academic/years/${id}`),
  create: (data) => API.post('/academic/years', data),
  update: (id, data) => API.put(`/academic/years/${id}`, data),
  delete: (id) => API.delete(`/academic/years/${id}`),
  setActive: (id) => API.patch(`/academic/years/${id}/set-active`)
};

const TermsAPI = {
  getAll: (params = {}) => API.get('/academic/terms', params),
  getById: (id) => API.get(`/academic/terms/${id}`),
  getByAcademicYear: (academicYearId) => API.get(`/academic/terms/year/${academicYearId}`),
  create: (data) => API.post('/academic/terms', data),
  update: (id, data) => API.put(`/academic/terms/${id}`, data),
  delete: (id) => API.delete(`/academic/terms/${id}`),
  activate: (id) => API.patch(`/academic/terms/${id}/activate`),
  setActive: (id) => API.patch(`/academic/terms/${id}/set-active`)
};

const LevelsAPI = {
  getAll: (params = {}) => API.get('/academic/levels', params)
};

const ClassLevelsAPI = {
  getAll: (params = {}) => API.get('/academic/class-levels', params)
};

const ReportsAPI = {
  generateReportCards: (params) => API.post('/reports/report-cards', params),
  getReportCard: (studentId, params) => API.get(`/reports/report-cards/${studentId}`, params),
  exportData: (type, params) => API.get(`/reports/export/${type}`, params)
};

const DashboardAPI = {
  getStats: (params = {}) => API.get('/dashboard/stats', params),
  getTrends: (params = {}) => API.get('/dashboard/trends', params),
  getSubjectPerformance: (params = {}) => API.get('/dashboard/subject-performance', params)
};

const AcademicAPI = {
  getCurrentContext: () => API.get('/academic/current-context'),
  getYearOptions: () => API.get('/academic/year-options'),
  getClasses: (params = {}) => API.get('/academic/classes', params),

  // Term examinations
  termExaminations: {
    getAll: (params = {}) => API.get('/academic/term-examinations', params),
    create: (data) => API.post('/academic/term-examinations', data)
  }
};

const SchoolSettingsAPI = {
  getAll: (params = {}) => API.get('/settings/school', params),
  getByCategory: (category) => API.get(`/settings/school/category/${category}`),
  getByKey: (key) => API.get(`/settings/school/key/${key}`),
  update: (key, value) => API.put(`/settings/school/key/${key}`, { value }),
  updateMultiple: (settings) => API.put('/settings/school/bulk', { settings }),
  getCategories: () => API.get('/settings/school/categories')
};

const PageDataAPI = {
  getRegistrationFormData: (type) => API.get(`/${type}/registration-form-data`),
  getReportsFormData: (params = {}) => API.get('/reports/form-data', params)
};

const AuthAPI = {
  login: (credentials) => API.post('/auth/login', credentials),
  logout: () => API.post('/auth/logout'),
  validateToken: (token) => API.post('/auth/validate', { token }),
  refreshToken: () => API.post('/auth/refresh')
};

const CAConfigurationAPI = {
  getAll: (params = {}) => API.get('/ca-configuration', params),
  getById: (id) => API.get(`/ca-configuration/${id}`),
  create: (data) => API.post('/ca-configuration', data),
  update: (id, data) => API.put(`/ca-configuration/${id}`, data),
  delete: (id) => API.delete(`/ca-configuration/${id}`)
};

const ExamTypesAPI = {
  getAll: (params = {}) => API.get('/academic/exam-types', params),
  getById: (id) => API.get(`/academic/exam-types/${id}`),
  create: (data) => API.post('/academic/exam-types', data),
  update: (id, data) => API.put(`/academic/exam-types/${id}`, data),
  delete: (id) => API.delete(`/academic/exam-types/${id}`)
};

const ClassExamTypesAPI = {
  getAll: (params = {}) => API.get('/academic/class-exam-types', params),
  create: (data) => API.post('/academic/class-exam-types', data),
  update: (id, data) => API.put(`/academic/class-exam-types/${id}`, data),
  delete: (id) => API.delete(`/academic/class-exam-types/${id}`),
  validateWeights: (classId, params = {}) => API.get(`/academic/class-exam-weights/validate/${classId}`, params)
};

const DatabaseAPI = {
  // Export database (create backup)
  exportDatabase: () => API.get('/database/export'),

  // Import database (restore from backup)
  importDatabase: (formData) => API.uploadFile('/database/import', formData),

  // Get backup history
  getBackupHistory: () => API.get('/database/backups'),

  // Download specific backup
  downloadBackup: (backupId) => API.get(`/database/backups/${backupId}/download`)
};

const SystemUsersAPI = {
  getAll: (params = {}) => API.get('/system-users', params),
  getById: (id) => API.get(`/system-users/${id}`),
  create: (data) => API.post('/system-users', data),
  update: (id, data) => API.put(`/system-users/${id}`, data),
  delete: (id) => API.delete(`/system-users/${id}`),
  updatePassword: (id, data) => API.put(`/system-users/${id}/password`, data),
  resetPassword: (id, data) => API.put(`/system-users/${id}/reset-password`, data)
};

// All API services available globally
window.API = API;
window.StudentsAPI = StudentsAPI;
window.EnrollmentsAPI = EnrollmentsAPI;
window.TeachersAPI = TeachersAPI;
window.ClassesAPI = ClassesAPI;
window.StreamsAPI = StreamsAPI;
window.SubjectsAPI = SubjectsAPI;

window.AssessmentsAPI = AssessmentsAPI;
window.CAScoresAPI = CAScoresAPI;
window.ExamMarksAPI = ExamMarksAPI;
window.AssignmentsAPI = AssignmentsAPI;
window.GradeBoundariesAPI = GradeBoundariesAPI;
window.GradingScaleAPI = GradingScaleAPI;

window.AcademicYearsAPI = AcademicYearsAPI;
window.TermsAPI = TermsAPI;
window.LevelsAPI = LevelsAPI;
window.ClassLevelsAPI = ClassLevelsAPI;
window.ReportsAPI = ReportsAPI;
window.DashboardAPI = DashboardAPI;
window.AcademicAPI = AcademicAPI;
window.SchoolSettingsAPI = SchoolSettingsAPI;
window.PageDataAPI = PageDataAPI;
window.AuthAPI = AuthAPI;
window.CAConfigurationAPI = CAConfigurationAPI;
window.ExamTypesAPI = ExamTypesAPI;
window.ClassExamTypesAPI = ClassExamTypesAPI;
window.SystemUsersAPI = SystemUsersAPI;
window.DatabaseAPI = DatabaseAPI;

console.log('✅ All API services loaded and available globally');
