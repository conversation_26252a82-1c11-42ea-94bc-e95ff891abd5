// SmartReport - Import/Export Data Management Component
// Database backup and restore functionality

// Uses global API services: window.DatabaseAPI
// Uses global config: window.SRConfig
// Uses environment configuration: ../config/environment.js

const ImportExportDataComponents = {
  // Component state
  state: {
    loading: false,
    lastBackup: null,
    backupHistory: []
  },

  // Initialize component
  async init() {
    await this.loadBackupHistory();
  },





  // Load backup history
  async loadBackupHistory() {
    try {
      this.state.loading = true;
      console.log('🔄 Loading backup history...');

      // Use the API service (to be implemented)
      if (window.DatabaseAPI && window.DatabaseAPI.getBackupHistory) {
        const result = await window.DatabaseAPI.getBackupHistory();
        if (result.success && result.data) {
          this.state.backupHistory = result.data;
          this.state.lastBackup = result.data[0] || null;
        }
      }

      if (window.SRConfig && window.SRConfig.get('development.debugMode')) {
        console.log('✅ Backup history loaded:', this.state.backupHistory);
      }

    } catch (error) {
      console.error('❌ Failed to load backup history:', error);
      this.state.backupHistory = [];
      this.state.lastBackup = null;
    } finally {
      this.state.loading = false;
    }
  }
};

// Import/Export Data Management Component
const ImportExportDataManagementComponent = {
  // Render import/export interface
  render() {
    return `
      <div class="space-y-6">
        ${this.renderPageHeader()}
        
        <!-- Database Export Section -->
        ${this.renderExportSection()}
        
        <!-- Database Import Section -->
        ${this.renderImportSection()}
        
        <!-- Backup History -->
        ${this.renderBackupHistory()}
        
        <!-- Confirmation Modals -->
        ${this.renderModals()}
      </div>
    `;
  },

  // Render page header
  renderPageHeader() {
    return `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
        <div>
          <h1 class="${SRDesignSystem.responsive.text['2xl']} font-bold text-gray-900">Data Import/Export</h1>
          <p class="text-gray-600 mt-1">Backup and restore your school database</p>
        </div>
      </div>
    `;
  },

  // Render export section
  renderExportSection() {
    const lastBackup = ImportExportDataComponents.state.lastBackup;
    
    return `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">Export Database</h3>
            <p class="${SRDesignSystem.responsive.text.sm} text-gray-600">Create a backup of your current database</p>
          </div>
          <div class="flex items-center space-x-2">
            ${SRDesignSystem.components.icon('fas fa-download', 'xl', 'blue-500')}
          </div>
        </div>

        <div class="space-y-4">
          ${lastBackup ? `
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div class="flex items-center">
                ${SRDesignSystem.components.icon('fas fa-info-circle', 'base', 'blue-500')}
                <div class="${SRDesignSystem.responsive.text.sm} ml-2">
                  <p class="text-blue-800 font-medium">Last backup: ${this.formatDate(lastBackup.created_at)}</p>
                  <p class="text-blue-600">Size: ${this.formatFileSize(lastBackup.file_size || 0)}</p>
                </div>
              </div>
            </div>
          ` : `
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                <p class="text-sm text-yellow-800">No previous backups found. It's recommended to create a backup regularly.</p>
              </div>
            </div>
          `}

          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-600">
              <p>• Exports all tables and data</p>
              <p>• Includes school settings and configurations</p>
              <p>• Safe to download and store externally</p>
            </div>
            <div class="flex items-center space-x-3">
              ${SRDesignSystem.forms.button('export-database', 'Export Database', 'primary', {
                icon: 'fas fa-download',
                onclick: 'ImportExportDataManagementComponent.showExportConfirmation()'
              })}
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Render import section
  renderImportSection() {
    return `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">Import Database</h3>
            <p class="${SRDesignSystem.responsive.text.sm} text-gray-600">Replace current database with uploaded backup</p>
          </div>
          <div class="flex items-center space-x-2">
            ${SRDesignSystem.components.icon('fas fa-upload', 'xl', 'red-500')}
          </div>
        </div>

        <div class="space-y-4">
          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex items-start">
              <div class="mr-2 mt-0.5">
                ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', 'base', 'red-500')}
              </div>
              <div class="${SRDesignSystem.responsive.text.sm}">
                <p class="text-red-800 font-medium">⚠️ Warning: This action is irreversible!</p>
                <p class="text-red-600 mt-1">Importing will completely replace your current database. All existing data will be lost.</p>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            ${SRDesignSystem.forms.fileUpload('database_file', 'Select Database File', {
              accept: '.sql',
              required: true,
              helpText: 'Upload a valid MySQL database dump file (.sql)'
            })}

            <div class="flex items-center justify-between">
              <div class="${SRDesignSystem.responsive.text.sm} text-gray-600">
                <p>• Only upload trusted MySQL database dump files</p>
                <p>• Ensure the file is from the same system version</p>
                <p>• Create a backup before importing</p>
              </div>
              <div class="flex items-center space-x-3">
                ${SRDesignSystem.forms.button('import-database', 'Import Database', 'danger', {
                  icon: 'fas fa-upload',
                  onclick: 'ImportExportDataManagementComponent.showImportConfirmation()'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Render backup history
  renderBackupHistory() {
    const backups = ImportExportDataComponents.state.backupHistory;
    
    return `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
        <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900 mb-6">Backup History</h3>
        
        ${backups.length > 0 ? `
          <div class="overflow-x-auto max-h-96 overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="px-6 py-3 text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Date</th>
                  <th class="px-6 py-3 text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Size</th>
                  <th class="px-6 py-3 text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Created By</th>
                  <th class="px-6 py-3 text-right ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                ${backups.map(backup => {
                  const actions = [
                    {
                      label: 'Download',
                      icon: 'fas fa-download',
                      onclick: `ImportExportDataManagementComponent.downloadBackup('${backup.id}')`,
                      color: 'blue'
                    }
                  ];

                  return `
                    <tr class="hover:bg-gray-50">
                      <td class="px-6 py-4 whitespace-nowrap ${SRDesignSystem.responsive.text.sm} text-gray-900">
                        ${this.formatDate(backup.created_at)}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap ${SRDesignSystem.responsive.text.sm} text-gray-500">
                        ${this.formatFileSize(backup.file_size || 0)}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap ${SRDesignSystem.responsive.text.sm} text-gray-500">
                        ${backup.created_by || 'System'}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-right">
                        ${SRDesignSystem.tables.generateKebabMenu(`backup-${backup.id}`, actions, backup)}
                      </td>
                    </tr>
                  `;
                }).join('')}
              </tbody>
            </table>
          </div>
        ` : `
          <div class="text-center py-8">
            <div class="text-gray-400 mb-4">
              ${SRDesignSystem.components.icon('fas fa-database', '4xl', 'gray-300')}
            </div>
            <p class="text-gray-500">No backup history available</p>
            <p class="${SRDesignSystem.responsive.text.sm} text-gray-400 mt-1">Create your first backup to see history here</p>
          </div>
        `}
      </div>
    `;
  },

  // Render confirmation modals
  renderModals() {
    return `
      <!-- Export Confirmation Modal -->
      ${SRDesignSystem.modals.confirm(
        'export-confirmation-modal',
        'Export Database',
        'Are you sure you want to export the database? This will create a backup file that you can download.',
        'ImportExportDataManagementComponent.exportDatabase()'
      )}

      <!-- Import Confirmation Modal -->
      ${SRDesignSystem.modals.confirm(
        'import-confirmation-modal',
        'Import Database - WARNING',
        'This will COMPLETELY REPLACE your current database with the uploaded file. All existing data will be permanently lost. This action cannot be undone. Are you absolutely sure?',
        'ImportExportDataManagementComponent.importDatabase()'
      )}
    `;
  },

  // Initialize component
  async init() {
    console.log('🔧 Initializing Import/Export Data Component...');

    // Reset component state
    this.resetComponentState();

    // Load initial data first
    await ImportExportDataComponents.init();

    // Initialize event listeners directly
    this.initializeEventListeners();
  },

  // Reset component state
  resetComponentState() {
    // Reset any component-specific state if needed
    console.log('🔄 Import/Export Data Component state reset');
  },

  // Cleanup component
  cleanup() {
    console.log('🧹 Cleaning up Import/Export Data Component...');
    this.resetComponentState();

    // Hide any open modals
    const modals = ['export-confirmation-modal', 'import-confirmation-modal'];
    modals.forEach(modalId => {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.classList.add('hidden');
      }
    });

    // Reset file input
    const fileInput = document.getElementById('database_file');
    if (fileInput) {
      fileInput.value = '';
    }

    console.log('✅ Import/Export Data Component cleanup completed');
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Add custom file upload handler for database file
    const databaseFileInput = document.getElementById('database_file');
    if (databaseFileInput) {
      databaseFileInput.addEventListener('change', (event) => {
        this.handleDatabaseFileUpload(event.target);
      });
    }

    console.log('✅ Event listeners initialized for Import/Export Data');
  },

  // Show export confirmation
  showExportConfirmation() {
    SRDesignSystem.modals.show('export-confirmation-modal');
  },

  // Show import confirmation
  showImportConfirmation() {
    const fileInput = document.getElementById('database_file');
    if (!fileInput || !fileInput.files || !fileInput.files[0]) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Please select a database file first', 'warning');
      } else {
        alert('Please select a database file first');
      }
      return;
    }

    SRDesignSystem.modals.show('import-confirmation-modal');
  },

  // Handle database file upload validation
  handleDatabaseFileUpload(input) {
    if (!input.files || !input.files[0]) return;

    const file = input.files[0];
    const allowedExtensions = ['.sql'];
    const fileName = file.name.toLowerCase();
    const isValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));

    if (!isValidExtension) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Please select a valid MySQL database dump file (.sql)', 'error');
      } else {
        alert('Please select a valid MySQL database dump file (.sql)');
      }
      input.value = '';
      return;
    }

    // Validate file size (max 100MB for database files)
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Database file is too large. Maximum size is 100MB', 'error');
      } else {
        alert('Database file is too large. Maximum size is 100MB');
      }
      input.value = '';
      return;
    }

    // Update file label
    if (window.SRDesignSystem && window.SRDesignSystem.forms && window.SRDesignSystem.forms.updateFileLabel) {
      window.SRDesignSystem.forms.updateFileLabel('database_file');
    }
  },

  // Export database
  async exportDatabase() {
    try {
      // Set loading state
      SRDesignSystem.forms.setButtonLoading('export-database', true);

      console.log('🔄 Starting database export...');

      // Use the API service (to be implemented)
      if (!window.DatabaseAPI || !window.DatabaseAPI.exportDatabase) {
        throw new Error('Database API not available');
      }

      const result = await window.DatabaseAPI.exportDatabase();

      if (result.success) {
        // Trigger download
        if (result.downloadUrl) {
          const link = document.createElement('a');
          link.href = result.downloadUrl;
          link.download = result.filename || `database_backup_${new Date().toISOString().split('T')[0]}.sql`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }

        // Show success notification
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Database exported successfully!', 'success');
        } else {
          alert('Database exported successfully!');
        }

        // Reload backup history
        await ImportExportDataComponents.loadBackupHistory();

        // Re-render the component to show updated history
        const container = document.querySelector('[data-component="import-export-data"]');
        if (container) {
          container.innerHTML = this.render();
          this.initializeEventListeners();
        }

      } else {
        throw new Error(result.message || 'Export failed');
      }

    } catch (error) {
      console.error('Export database error:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to export database: ' + error.message, 'error');
      } else {
        alert('Failed to export database: ' + error.message);
      }
    } finally {
      // Remove loading state
      SRDesignSystem.forms.setButtonLoading('export-database', false);
    }
  },

  // Import database
  async importDatabase() {
    try {
      const fileInput = document.getElementById('database_file');
      if (!fileInput || !fileInput.files || !fileInput.files[0]) {
        throw new Error('No file selected');
      }

      // Set loading state
      SRDesignSystem.forms.setButtonLoading('import-database', true);

      console.log('🔄 Starting database import...');

      const file = fileInput.files[0];
      const formData = new FormData();
      formData.append('database_file', file);

      // Use the API service (to be implemented)
      if (!window.DatabaseAPI || !window.DatabaseAPI.importDatabase) {
        throw new Error('Database API not available');
      }

      const result = await window.DatabaseAPI.importDatabase(formData);

      if (result.success) {
        // Show success notification
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Database imported successfully! The page will reload.', 'success');
        } else {
          alert('Database imported successfully! The page will reload.');
        }

        // Clear the file input
        fileInput.value = '';

        // Reload the page after a short delay to reflect the new database
        setTimeout(() => {
          window.location.reload();
        }, 2000);

      } else {
        throw new Error(result.message || 'Import failed');
      }

    } catch (error) {
      console.error('Import database error:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to import database: ' + error.message, 'error');
      } else {
        alert('Failed to import database: ' + error.message);
      }
    } finally {
      // Remove loading state
      SRDesignSystem.forms.setButtonLoading('import-database', false);
    }
  },

  // Download backup
  async downloadBackup(backupId) {
    try {
      console.log('🔄 Downloading backup:', backupId);

      // Use the API service (to be implemented)
      if (!window.DatabaseAPI || !window.DatabaseAPI.downloadBackup) {
        throw new Error('Database API not available');
      }

      const result = await window.DatabaseAPI.downloadBackup(backupId);

      if (result.success && result.downloadUrl) {
        // Trigger download
        const link = document.createElement('a');
        link.href = result.downloadUrl;
        link.download = result.filename || `backup_${backupId}.sql`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Show success notification
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Backup downloaded successfully!', 'success');
        }
      } else {
        throw new Error(result.message || 'Download failed');
      }

    } catch (error) {
      console.error('Download backup error:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to download backup: ' + error.message, 'error');
      } else {
        alert('Failed to download backup: ' + error.message);
      }
    }
  },

  // Utility: Format date
  formatDate(dateString) {
    if (!dateString) return 'Unknown';
    try {
      const date = new Date(dateString);
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return 'Invalid Date';
    }
  },

  // Utility: Format file size
  formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // Generate dropdown action menu for backup actions
  generateBackupActionDropdown(backupId, actions) {
    const dropdownId = `dropdown-backup-${backupId}`;

    return `
      <div class="relative inline-block text-left">
        <button type="button"
                onclick="ImportExportDataManagementComponent.toggleDropdown('${dropdownId}')"
                class="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors duration-150"
                title="More actions">
          <i class="fas fa-ellipsis-v text-sm"></i>
        </button>

        <div id="${dropdownId}"
             class="hidden fixed z-[9999] w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1"
             style="transform: translate(-100%, -100%);">
          ${actions.map(action => `
            <button onclick="ImportExportDataManagementComponent.closeDropdown('${dropdownId}'); ${action.onclick}"
                    class="flex items-center w-full px-4 py-3 text-sm text-left hover:bg-gray-50 transition-colors duration-150 ${action.color || 'text-gray-700'}">
              <i class="${action.icon} w-4 mr-3"></i>
              ${action.label}
            </button>
          `).join('')}
        </div>
      </div>
    `;
  },

  // Toggle dropdown visibility
  toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (!dropdown) return;

    // Close all other dropdowns first
    this.closeAllDropdowns();

    // Toggle current dropdown
    dropdown.classList.toggle('hidden');

    // Position dropdown above the button if dropdown is now open
    if (!dropdown.classList.contains('hidden')) {
      const button = dropdown.previousElementSibling;
      if (button) {
        const buttonRect = button.getBoundingClientRect();
        dropdown.style.left = `${buttonRect.right - 192}px`; // 192px = w-48 (12rem * 16px)
        dropdown.style.top = `${buttonRect.top - dropdown.offsetHeight - 4}px`; // 4px gap
      }

      setTimeout(() => {
        document.addEventListener('click', this.handleOutsideClick);
      }, 10);
    }
  },

  // Close specific dropdown
  closeDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (dropdown) {
      dropdown.classList.add('hidden');
    }
    document.removeEventListener('click', this.handleOutsideClick);
  },

  // Close all dropdowns
  closeAllDropdowns() {
    const dropdowns = document.querySelectorAll('[id^="dropdown-backup-"]');
    dropdowns.forEach(dropdown => {
      dropdown.classList.add('hidden');
    });
    document.removeEventListener('click', this.handleOutsideClick);
  },

  // Handle clicks outside dropdown
  handleOutsideClick(event) {
    const isDropdownButton = event.target.closest('[onclick*="toggleDropdown"]');
    const isDropdownContent = event.target.closest('[id^="dropdown-backup-"]');

    if (!isDropdownButton && !isDropdownContent) {
      ImportExportDataManagementComponent.closeAllDropdowns();
    }
  }
};

// Export components to global scope
window.ImportExportDataComponents = ImportExportDataComponents;
window.ImportExportDataManagementComponent = ImportExportDataManagementComponent;
