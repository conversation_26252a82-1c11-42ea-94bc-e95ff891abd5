const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// =============================================
// EXAM GRADES ROUTES - Two-Tier Assessment System
// =============================================

// Get students for exam grade entry
router.get('/students', async (req, res) => {
  try {
    const { class_id, subject_id, exam_type_id, academic_year_id, term_id } = req.query;

    // Validate required parameters
    if (!class_id || !subject_id || !exam_type_id || !academic_year_id || !term_id) {
      return res.status(400).json({
        success: false,
        message: 'Class ID, subject ID, exam type ID, academic year ID, and term ID are required'
      });
    }

    // First, determine if this is O-Level or A-Level based on class
    const classLevelQuery = `
      SELECT el.name as level_name
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      WHERE c.id = ?
    `;
    
    const classLevelResult = await executeQuery(classLevelQuery, [class_id]);
    
    if (!classLevelResult.success || classLevelResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class not found'
      });
    }

    const levelName = classLevelResult.data[0].level_name;
    const isOLevel = levelName === 'O-Level';

    let studentsQuery;
    let examMarksQuery;
    let caQuery;

    if (isOLevel) {
      // O-Level students query
      studentsQuery = `
        SELECT DISTINCT
          s.id, s.admission_number, s.first_name, s.last_name, s.passport_photo as photo_url,
          s.current_class_id as class_id, s.stream_id,
          c.name as class_name, st.name as stream_name
        FROM o_level_students s
        JOIN classes c ON s.current_class_id = c.id
        LEFT JOIN streams st ON s.stream_id = st.id
        JOIN o_level_student_subjects ss ON s.id = ss.student_id
        WHERE s.current_class_id = ?
          AND ss.subject_id = ?
          AND s.current_academic_year_id = ?
          AND s.current_term_id = ?
          AND s.status IN ('active', 'suspended')
        ORDER BY s.first_name, s.last_name
      `;

      // Check for existing exam marks
      examMarksQuery = `
        SELECT em.student_id, em.marks_obtained
        FROM o_level_student_exam_marks em
        JOIN o_level_term_examinations e ON em.examination_id = e.id
        WHERE e.class_id = ? AND e.subject_id = ? AND e.exam_type_id = ?
          AND e.academic_year_id = ? AND e.term_id = ?
      `;

      // Get CA averages
      caQuery = `
        SELECT student_id, AVG(competency_score) as ca_average
        FROM o_level_subject_continuous_assessments_scores
        WHERE subject_id = ? AND academic_year_id = ? AND term_id = ?
        GROUP BY student_id
      `;
    } else {
      // A-Level students query - need to check both principal and subsidiary
      const subjectTypeQuery = `
        SELECT subject_type FROM a_level_subjects WHERE id = ?
      `;
      
      const subjectTypeResult = await executeQuery(subjectTypeQuery, [subject_id]);
      
      if (!subjectTypeResult.success || subjectTypeResult.data.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Subject not found'
        });
      }

      const subjectType = subjectTypeResult.data[0].subject_type;
      const isSubsidiary = subjectType === 'subsidiary';

      studentsQuery = `
        SELECT DISTINCT
          s.id, s.admission_number, s.first_name, s.last_name, s.passport_photo as photo_url,
          s.current_class_id as class_id, s.stream_id,
          c.name as class_name, st.name as stream_name
        FROM a_level_students s
        JOIN classes c ON s.current_class_id = c.id
        LEFT JOIN streams st ON s.stream_id = st.id
        JOIN a_level_student_subjects ss ON s.id = ss.student_id
        WHERE s.current_class_id = ?
          AND ss.subject_id = ?
          AND s.current_academic_year_id = ?
          AND s.current_term_id = ?
          AND s.status IN ('active', 'suspended')
        ORDER BY s.first_name, s.last_name
      `;

      // For A-Level, we need to use paper-based queries since the new schema uses subject_paper_id
      // We'll need to get all papers for the subject and aggregate the data
      examMarksQuery = `
        SELECT em.student_id, em.marks_obtained
        FROM a_level_student_paper_exam_marks em
        JOIN a_level_paper_examinations e ON em.examination_id = e.id
        JOIN a_level_subject_papers sp ON e.subject_paper_id = sp.id
        WHERE e.class_id = ? AND sp.subject_id = ? AND e.exam_type_id = ?
          AND e.academic_year_id = ? AND e.term_id = ?
      `;

      // Get CA averages - now using unified paper-based table
      caQuery = `
        SELECT ca.student_id, AVG(ca.competency_score) as ca_average
        FROM a_level_paper_continuous_assessments_scores ca
        JOIN a_level_subject_papers sp ON ca.subject_paper_id = sp.id
        JOIN a_level_subjects sub ON sp.subject_id = sub.id
        WHERE sp.subject_id = ? AND ca.academic_year_id = ? AND ca.term_id = ?
          AND sub.subject_type = ?
        GROUP BY ca.student_id
      `;
    }

    // Execute all queries
    let studentsResult, examMarksResult, caResult;

    if (isOLevel) {
      [studentsResult, examMarksResult, caResult] = await Promise.all([
        executeQuery(studentsQuery, [class_id, subject_id, academic_year_id, term_id]),
        executeQuery(examMarksQuery, [class_id, subject_id, exam_type_id, academic_year_id, term_id]),
        executeQuery(caQuery, [subject_id, academic_year_id, term_id])
      ]);
    } else {
      // For A-Level, we need to pass the subject_type for CA query
      const subjectType = subjectTypeResult.data[0].subject_type;
      [studentsResult, examMarksResult, caResult] = await Promise.all([
        executeQuery(studentsQuery, [class_id, subject_id, academic_year_id, term_id]),
        executeQuery(examMarksQuery, [class_id, subject_id, exam_type_id, academic_year_id, term_id]),
        executeQuery(caQuery, [subject_id, academic_year_id, term_id, subjectType])
      ]);
    }

    if (!studentsResult.success) {
      throw new Error(studentsResult.error);
    }

    // Create lookup maps for existing data
    const examMarksMap = {};
    if (examMarksResult.success) {
      examMarksResult.data.forEach(mark => {
        examMarksMap[mark.student_id] = {
          marks_obtained: mark.marks_obtained,
          percentage: mark.percentage
        };
      });
    }

    const caAverageMap = {};
    if (caResult.success) {
      caResult.data.forEach(ca => {
        caAverageMap[ca.student_id] = ca.ca_average;
      });
    }

    // Combine student data with existing marks and CA averages
    const studentsWithData = studentsResult.data.map(student => ({
      ...student,
      current_exam_mark: examMarksMap[student.id]?.marks_obtained || null,
      current_exam_percentage: examMarksMap[student.id]?.percentage || null,
      ca_average: caAverageMap[student.id] || null
    }));

    res.json({
      success: true,
      data: studentsWithData,
      message: `${studentsWithData.length} students retrieved for exam grade entry`
    });

  } catch (error) {
    console.error('Get students for exam grades error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve students for exam grade entry',
      error: error.message
    });
  }
});

// Get exam grades with filters
router.get('/', async (req, res) => {
  try {
    const { class_id, subject_id, exam_type_id, academic_year_id, term_id } = req.query;

    // This query needs to handle both O-Level and A-Level examinations from separate tables
    // We'll use UNION to combine results from both level-specific tables
    let query = `
      SELECT
        em.*,
        'o_level' as subject_level,
        os.admission_number,
        os.first_name,
        os.last_name,
        c.name as class_name,
        osub.name as subject_name,
        et.name as exam_type_name,
        ay.name as academic_year_name,
        t.name as term_name
      FROM o_level_student_exam_marks em
      JOIN o_level_term_examinations e ON em.examination_id = e.id
      JOIN o_level_students os ON em.student_id = os.id
      JOIN classes c ON e.class_id = c.id
      JOIN o_level_subjects osub ON e.subject_id = osub.id
      JOIN exam_types et ON e.exam_type_id = et.id
      JOIN academic_years ay ON e.academic_year_id = ay.id
      JOIN terms t ON e.term_id = t.id
      WHERE 1=1
    `;

    // Add O-Level specific filters
    if (class_id) {
      query += ' AND e.class_id = ?';
    }
    if (subject_id) {
      query += ' AND e.subject_id = ?';
    }
    if (exam_type_id) {
      query += ' AND e.exam_type_id = ?';
    }
    if (academic_year_id) {
      query += ' AND e.academic_year_id = ?';
    }
    if (term_id) {
      query += ' AND e.term_id = ?';
    }

    // Add UNION for A-Level examinations
    query += `
      UNION ALL
      SELECT
        em.*,
        'a_level' as subject_level,
        as_student.admission_number,
        as_student.first_name,
        as_student.last_name,
        c.name as class_name,
        CONCAT(asub.name, ' - ', sp.paper_name) as subject_name,
        et.name as exam_type_name,
        ay.name as academic_year_name,
        t.name as term_name
      FROM a_level_student_paper_exam_marks em
      JOIN a_level_paper_examinations e ON em.examination_id = e.id
      JOIN a_level_students as_student ON em.student_id = as_student.id
      JOIN classes c ON e.class_id = c.id
      JOIN a_level_subject_papers sp ON e.subject_paper_id = sp.id
      JOIN a_level_subjects asub ON sp.subject_id = asub.id
      JOIN exam_types et ON e.exam_type_id = et.id
      JOIN academic_years ay ON e.academic_year_id = ay.id
      JOIN terms t ON e.term_id = t.id
      WHERE 1=1
    `;

    // Add A-Level specific filters (same logic but for A-Level table)
    if (class_id) {
      query += ' AND e.class_id = ?';
    }
    if (subject_id) {
      query += ' AND sp.subject_id = ?';
    }
    if (exam_type_id) {
      query += ' AND e.exam_type_id = ?';
    }
    if (academic_year_id) {
      query += ' AND e.academic_year_id = ?';
    }
    if (term_id) {
      query += ' AND e.term_id = ?';
    }

    // Build parameters array - we need to duplicate parameters for both UNION queries
    const params = [];

    // Parameters for O-Level query
    if (class_id) params.push(class_id);
    if (subject_id) params.push(subject_id);
    if (exam_type_id) params.push(exam_type_id);
    if (academic_year_id) params.push(academic_year_id);
    if (term_id) params.push(term_id);

    // Parameters for A-Level query (same values)
    if (class_id) params.push(class_id);
    if (subject_id) params.push(subject_id);
    if (exam_type_id) params.push(exam_type_id);
    if (academic_year_id) params.push(academic_year_id);
    if (term_id) params.push(term_id);

    query += ' ORDER BY first_name, last_name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data,
      message: `${result.data.length} exam grades retrieved`
    });

  } catch (error) {
    console.error('Get exam grades error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve exam grades',
      error: error.message
    });
  }
});

// Create/Update exam grades using Two-Tier Assessment System
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { grades } = req.body;

    if (!grades || !Array.isArray(grades) || grades.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Grades array is required and must not be empty'
      });
    }

    // Validate each grade entry
    for (const grade of grades) {
      const { student_id, examination_id, marks_obtained } = grade;

      if (!student_id || !examination_id) {
        return res.status(400).json({
          success: false,
          message: 'Student ID and examination ID must be provided for each grade entry'
        });
      }

      if (marks_obtained !== null && (isNaN(marks_obtained) || marks_obtained < 0 || marks_obtained > 100)) {
        return res.status(400).json({
          success: false,
          message: 'Marks obtained must be between 0 and 100'
        });
      }
    }

    // Get examination details - need to check both O-Level and A-Level tables
    let examDetailsQuery = `
      SELECT 'o_level' as subject_level, class_id, subject_id, exam_type_id, academic_year_id, term_id
      FROM o_level_term_examinations
      WHERE id = ?
      UNION ALL
      SELECT 'a_level' as subject_level, class_id, sp.subject_id, exam_type_id, academic_year_id, term_id
      FROM a_level_paper_examinations e
      JOIN a_level_subject_papers sp ON e.subject_paper_id = sp.id
      WHERE e.id = ?
    `;

    const examDetailsResult = await executeQuery(examDetailsQuery, [grades[0].examination_id, grades[0].examination_id]);

    if (!examDetailsResult.success || examDetailsResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Examination not found'
      });
    }

    let successCount = 0;
    const errors = [];
    const examDetails = examDetailsResult.data[0];
    const subjectLevel = examDetails.subject_level;

    // Process each grade
    for (const grade of grades) {
      try {
        const {
          student_id, examination_id, marks_obtained
        } = grade;

        // Validate marks_obtained (must be integer 0-100)
        if (marks_obtained !== null && (!Number.isInteger(marks_obtained) || marks_obtained < 0 || marks_obtained > 100)) {
          errors.push(`Invalid marks for student ${student_id}: must be integer between 0-100`);
          continue;
        }

        // Use the appropriate table based on subject level
        let tableName, checkQuery, insertQuery, updateQuery;

        if (subjectLevel === 'o_level') {
          tableName = 'o_level_student_exam_marks';
        } else {
          tableName = 'a_level_student_paper_exam_marks';
        }

        checkQuery = `
          SELECT id FROM ${tableName}
          WHERE student_id = ? AND examination_id = ?
        `;

        insertQuery = `
          INSERT INTO ${tableName} (
            student_id, examination_id, marks_obtained
          ) VALUES (?, ?, ?)
        `;

        updateQuery = `
          UPDATE ${tableName} SET
            marks_obtained = ?, updated_at = NOW()
          WHERE student_id = ? AND examination_id = ?
        `;

        // Check if record exists
        const checkResult = await executeQuery(checkQuery, [student_id, examination_id]);

        if (!checkResult.success) {
          errors.push(`Failed to check existing record for student ${student_id}: ${checkResult.error}`);
          continue;
        }

        let result;
        if (checkResult.data.length > 0) {
          // Update existing record
          result = await executeQuery(updateQuery, [
            marks_obtained,
            student_id, examination_id
          ]);
        } else {
          // Insert new record
          result = await executeQuery(insertQuery, [
            student_id, examination_id, marks_obtained
          ]);
        }

        if (result.success) {
          successCount++;
        } else {
          errors.push(`Failed to save grade for student ${student_id}: ${result.error}`);
        }

      } catch (error) {
        errors.push(`Error processing grade for student ${grade.student_id}: ${error.message}`);
      }
    }

    res.json({
      success: true,
      message: `${successCount} exam grades saved successfully`,
      data: {
        saved: successCount,
        total: grades.length,
        errors: errors
      }
    });

  } catch (error) {
    console.error('Create exam grades error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save exam grades',
      error: error.message
    });
  }
});

module.exports = router;
