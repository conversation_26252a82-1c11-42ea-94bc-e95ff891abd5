@tailwind base;
@tailwind components;
@tailwind utilities;

/*
 * SmartReport Tailwind CSS - Production Build
 * Aligned with SRDesignSystem for consistency
 * Desktop-focused design system
 */

@layer base {
  /* Base styles matching SRDesignSystem */
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900;
  }

  /* Custom scrollbar matching design system */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

/* Custom component styles matching SRDesignSystem */
@layer components {
  /* Button styles matching SRDesignSystem.forms.button */
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }

  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }

  .btn-green {
    @apply btn bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }

  .btn-danger {
    @apply btn bg-error-600 text-white hover:bg-error-700 focus:ring-error-500;
  }

  .btn-blue {
    @apply btn bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-indigo {
    @apply btn bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500;
  }

  .btn-ghost {
    @apply btn bg-transparent text-gray-600 hover:bg-gray-100 focus:ring-gray-500;
  }
  
  /* Card styles matching SRDesignSystem */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-gray-200;
  }

  /* Form styles matching SRDesignSystem.forms */
  .form-input {
    @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200;
  }

  .form-select {
    @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }

  .form-field {
    @apply mb-4;
  }
  
  .table-auto {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
}

/* Custom utility styles */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  .gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }
  
  .gradient-secondary {
    background: linear-gradient(135deg, #64748b 0%, #334155 100%);
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.slide-down {
  animation: slideDown 0.3s ease-out;
}

/* Loading spinner */
.spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
}

/* Notification styles */
.notification {
  @apply fixed top-4 right-4 z-50 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden;
}

.notification-success {
  @apply border-l-4 border-green-400;
}

.notification-error {
  @apply border-l-4 border-red-400;
}

.notification-warning {
  @apply border-l-4 border-yellow-400;
}

.notification-info {
  @apply border-l-4 border-blue-400;
}

/* Modal styles */
.modal-overlay {
  @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50;
}

.modal-container {
  @apply relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white;
}

/* Sidebar styles */
.sidebar {
  @apply fixed left-0 top-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out z-40;
}

.sidebar.collapsed {
  @apply -translate-x-full;
}

/* Responsive table */
.table-responsive {
  @apply overflow-x-auto shadow ring-1 ring-black ring-opacity-5 md:rounded-lg;
}

/* Status badges */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
  @apply badge bg-green-100 text-green-800;
}

.badge-error {
  @apply badge bg-red-100 text-red-800;
}

.badge-warning {
  @apply badge bg-yellow-100 text-yellow-800;
}

.badge-info {
  @apply badge bg-blue-100 text-blue-800;
}

.badge-secondary {
  @apply badge bg-gray-100 text-gray-800;
}
