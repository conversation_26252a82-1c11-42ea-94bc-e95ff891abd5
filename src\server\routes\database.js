const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const { executeQuery, pool } = require('../../database/connection');
const { authenticateToken } = require('./auth');
const multer = require('multer');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Configure multer for database file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../temp/database-uploads');
    // Ensure directory exists
    require('fs').mkdirSync(uploadDir, { recursive: true });
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Generate unique filename with timestamp
    const timestamp = Date.now();
    const originalName = file.originalname;
    const extension = path.extname(originalName);
    const baseName = path.basename(originalName, extension);
    cb(null, `${baseName}_${timestamp}${extension}`);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB limit
  },
  fileFilter: function (req, file, cb) {
    // Accept SQL database dump files
    const allowedExtensions = ['.sql'];
    const fileExtension = path.extname(file.originalname).toLowerCase();

    if (allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only .sql files are allowed for MySQL database imports.'));
    }
  }
});

// Helper function to generate SQL dump
async function generateSQLDump() {
  const dbName = process.env.DB_NAME || 'smartreport_db';
  let sqlDump = `-- SmartReport Database Backup\n`;
  sqlDump += `-- Generated on: ${new Date().toISOString()}\n`;
  sqlDump += `-- Database: ${dbName}\n\n`;

  sqlDump += `SET FOREIGN_KEY_CHECKS = 0;\n\n`;

  try {
    // Get all tables
    console.log('🔄 Getting table list...');
    const tablesResult = await executeQuery('SHOW TABLES');
    if (!tablesResult.success) {
      throw new Error('Failed to get table list: ' + tablesResult.error);
    }

    const tables = tablesResult.data.map(row => Object.values(row)[0]);
    console.log('✅ Found tables:', tables);

    for (const table of tables) {
      console.log(`🔄 Exporting table: ${table}`);

      // Get table structure
      try {
        const createTableResult = await executeQuery(`SHOW CREATE TABLE \`${table}\``);
        if (createTableResult.success && createTableResult.data.length > 0) {
          sqlDump += `-- Table structure for \`${table}\`\n`;
          sqlDump += `DROP TABLE IF EXISTS \`${table}\`;\n`;
          sqlDump += createTableResult.data[0]['Create Table'] + ';\n\n';
        } else {
          console.warn(`Failed to get structure for table ${table}`);
          continue;
        }
      } catch (structureError) {
        console.warn(`Error getting structure for table ${table}:`, structureError.message);
        continue;
      }

      // Get table data
      try {
        const dataResult = await executeQuery(`SELECT * FROM \`${table}\``, []);
        if (dataResult.success && dataResult.data.length > 0) {
          console.log(`✅ Found ${dataResult.data.length} rows in table ${table}`);
          sqlDump += `-- Data for table \`${table}\`\n`;

          // Get column names
          const columnsResult = await executeQuery(`SHOW COLUMNS FROM \`${table}\``, []);
          if (!columnsResult.success) {
            console.warn(`Failed to get columns for table ${table}`);
            continue;
          }

          const columns = columnsResult.data.map(col => col.Field);

          sqlDump += `INSERT INTO \`${table}\` (\`${columns.join('`, `')}\`) VALUES\n`;

          const values = dataResult.data.map(row => {
            const rowValues = columns.map(col => {
              const value = row[col];
              if (value === null || value === undefined) return 'NULL';
              if (typeof value === 'string') {
                // Escape single quotes and backslashes properly
                const escaped = value.replace(/\\/g, '\\\\').replace(/'/g, "\\'");
                return `'${escaped}'`;
              }
              if (value instanceof Date) {
                return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
              }
              if (typeof value === 'boolean') {
                return value ? '1' : '0';
              }
              if (typeof value === 'number') {
                return isNaN(value) ? 'NULL' : value;
              }
              // For any other type, convert to string and escape
              return `'${String(value).replace(/\\/g, '\\\\').replace(/'/g, "\\'")}'`;
            });
            return `(${rowValues.join(', ')})`;
          });

          sqlDump += values.join(',\n') + ';\n\n';
        } else {
          console.log(`✅ Table ${table} is empty`);
        }
      } catch (dataError) {
        console.warn(`Error getting data for table ${table}:`, dataError.message);
        continue;
      }
    }

    sqlDump += `SET FOREIGN_KEY_CHECKS = 1;\n`;
    return sqlDump;

  } catch (error) {
    console.error('Error generating SQL dump:', error);
    throw error;
  }
}

// Export database (create backup)
router.get('/export', async (req, res) => {
  try {
    console.log('🔄 Starting database export...');

    // Create backup filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFilename = `smartreport_backup_${timestamp}.sql`;
    const backupDir = path.join(__dirname, '../../backups');
    const backupPath = path.join(backupDir, backupFilename);

    // Ensure backup directory exists
    try {
      await fs.mkdir(backupDir, { recursive: true });
      console.log('✅ Backup directory created/verified');
    } catch (error) {
      console.error('Failed to create backup directory:', error);
      throw new Error('Failed to create backup directory');
    }

    // Generate SQL dump
    console.log('🔄 Generating database dump...');
    let sqlDump;
    try {
      sqlDump = await generateSQLDump();
      console.log('✅ SQL dump generated, size:', sqlDump.length, 'characters');
    } catch (dumpError) {
      console.error('Failed to generate SQL dump:', dumpError);
      throw new Error('Failed to generate database dump: ' + dumpError.message);
    }

    // Write dump to file
    try {
      await fs.writeFile(backupPath, sqlDump, 'utf8');
      console.log('✅ Database dump written to file:', backupPath);
    } catch (writeError) {
      console.error('Failed to write dump to file:', writeError);
      throw new Error('Failed to write backup file');
    }

    // Get file stats
    const stats = await fs.stat(backupPath);
    console.log('✅ Backup file stats:', { size: stats.size, path: backupPath });

    // Record backup in database (optional - for backup history)
    try {
      // First check if backup_history table exists
      const tableCheckResult = await executeQuery("SHOW TABLES LIKE 'backup_history'");
      if (tableCheckResult.success && tableCheckResult.data.length > 0) {
        const insertQuery = `
          INSERT INTO backup_history (filename, file_path, file_size, created_at)
          VALUES (?, ?, ?, NOW())
        `;
        await executeQuery(insertQuery, [
          backupFilename,
          backupPath,
          stats.size
        ]);
        console.log('✅ Backup recorded in history');
      } else {
        console.log('ℹ️ backup_history table does not exist, skipping history recording');
      }
    } catch (dbError) {
      console.warn('Failed to record backup in history:', dbError.message);
      // Continue with export even if history recording fails
    }

    console.log('✅ Database export completed, starting download:', backupFilename);

    // Set proper headers for file download
    res.setHeader('Content-Type', 'application/sql');
    res.setHeader('Content-Disposition', `attachment; filename="${backupFilename}"`);
    res.setHeader('Content-Length', stats.size);

    // Send file for download
    res.download(backupPath, backupFilename, (err) => {
      if (err) {
        console.error('Download error:', err);
        if (!res.headersSent) {
          res.status(500).json({
            success: false,
            message: 'Failed to download backup file'
          });
        }
      } else {
        console.log('✅ File download completed successfully');
      }
    });

  } catch (error) {
    console.error('Database export error:', error);
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        message: 'Failed to export database: ' + error.message
      });
    }
  }
});

// Helper function to execute SQL statements from file
async function executeSQLFile(filePath) {
  try {
    const sqlContent = await fs.readFile(filePath, 'utf8');

    // Split SQL content into individual statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`🔄 Executing ${statements.length} SQL statements...`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        try {
          await executeQuery(statement, []);
        } catch (error) {
          console.warn(`Warning: Failed to execute statement ${i + 1}:`, error.message);
          // Continue with other statements
        }
      }
    }

    console.log('✅ SQL file execution completed');
    return true;
  } catch (error) {
    console.error('Error executing SQL file:', error);
    throw error;
  }
}

// Import database (restore from backup)
router.post('/import', upload.single('database_file'), async (req, res) => {
  try {
    console.log('🔄 Starting database import...');

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No database file provided'
      });
    }

    const uploadedFilePath = req.file.path;

    // Create backup of current database before importing
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const preImportBackupFilename = `pre_import_backup_${timestamp}.sql`;
    const backupDir = path.join(__dirname, '../../backups');
    const preImportBackupPath = path.join(backupDir, preImportBackupFilename);

    // Ensure backup directory exists
    await fs.mkdir(backupDir, { recursive: true });

    // Backup current database
    try {
      console.log('🔄 Creating pre-import backup...');
      const sqlDump = await generateSQLDump();
      await fs.writeFile(preImportBackupPath, sqlDump, 'utf8');
      console.log('✅ Pre-import backup created:', preImportBackupFilename);
    } catch (backupError) {
      console.warn('Failed to create pre-import backup:', backupError.message);
    }

    // Import the database
    try {
      console.log('🔄 Importing database...');
      await executeSQLFile(uploadedFilePath);
      console.log('✅ Database import completed successfully');
    } catch (importError) {
      console.error('Database import failed:', importError.message);
      throw new Error(`Failed to import database: ${importError.message}`);
    }

    // Clean up uploaded file
    try {
      await fs.unlink(uploadedFilePath);
    } catch (cleanupError) {
      console.warn('Failed to clean up uploaded file:', cleanupError.message);
    }

    res.json({
      success: true,
      message: 'Database imported successfully',
      backup_created: preImportBackupFilename
    });

  } catch (error) {
    console.error('Database import error:', error);

    // Clean up uploaded file on error
    if (req.file && req.file.path) {
      try {
        await fs.unlink(req.file.path);
      } catch (cleanupError) {
        console.warn('Failed to clean up uploaded file after error:', cleanupError.message);
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to import database: ' + error.message
    });
  }
});

// Get backup history
router.get('/backups', async (req, res) => {
  try {
    console.log('🔄 Fetching backup history...');

    // Try to get from database first
    try {
      const query = `
        SELECT id, filename, file_path, file_size, created_at
        FROM backup_history
        ORDER BY created_at DESC
        LIMIT 50
      `;
      const result = await executeQuery(query);

      if (result.success && result.data.length > 0) {
        return res.json({
          success: true,
          data: result.data
        });
      }
    } catch (dbError) {
      console.warn('Failed to fetch backup history from database:', dbError.message);
    }

    // Fallback: scan backup directory
    const backupDir = path.join(__dirname, '../../backups');
    
    try {
      const files = await fs.readdir(backupDir);
      const backupFiles = [];

      for (const file of files) {
        if (file.endsWith('.sql')) {
          const filePath = path.join(backupDir, file);
          try {
            const stats = await fs.stat(filePath);
            backupFiles.push({
              id: file, // Use filename as ID for fallback
              filename: file,
              file_path: filePath,
              file_size: stats.size,
              created_at: stats.mtime.toISOString()
            });
          } catch (statError) {
            console.warn(`Failed to get stats for ${file}:`, statError.message);
          }
        }
      }

      // Sort by creation date (newest first)
      backupFiles.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

      res.json({
        success: true,
        data: backupFiles
      });

    } catch (dirError) {
      console.warn('Failed to read backup directory:', dirError.message);
      res.json({
        success: true,
        data: []
      });
    }

  } catch (error) {
    console.error('Get backup history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch backup history: ' + error.message
    });
  }
});

// Download specific backup
router.get('/backups/:backupId/download', async (req, res) => {
  try {
    const { backupId } = req.params;
    console.log('🔄 Downloading backup:', backupId);

    let backupPath;
    let filename;

    // Try to get backup info from database first
    try {
      const query = `
        SELECT filename, file_path
        FROM backup_history
        WHERE id = ? OR filename = ?
      `;
      const result = await executeQuery(query, [backupId, backupId]);

      if (result.success && result.data.length > 0) {
        const backup = result.data[0];
        backupPath = backup.file_path;
        filename = backup.filename;
      }
    } catch (dbError) {
      console.warn('Failed to fetch backup from database:', dbError.message);
    }

    // Fallback: treat backupId as filename
    if (!backupPath) {
      filename = backupId;
      backupPath = path.join(__dirname, '../../backups', filename);
    }

    // Check if file exists
    try {
      await fs.access(backupPath);
    } catch (error) {
      return res.status(404).json({
        success: false,
        message: 'Backup file not found'
      });
    }

    // Send file for download
    res.download(backupPath, filename, (err) => {
      if (err) {
        console.error('Download error:', err);
        if (!res.headersSent) {
          res.status(500).json({
            success: false,
            message: 'Failed to download backup file'
          });
        }
      }
    });

  } catch (error) {
    console.error('Download backup error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to download backup: ' + error.message
    });
  }
});

module.exports = router;
