// SmartReport - Utility Functions
// Common utility functions used throughout the application

// Date formatting utilities
const DateUtils = {
  // Format date to YYYY-MM-DD
  formatDate(date) {
    if (!date) return '';
    const d = new Date(date);
    return d.toISOString().split('T')[0];
  },
  
  // Format date to readable format
  formatDateReadable(date) {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('en-UG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  },
  
  // Get current academic year
  getCurrentAcademicYear() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1; // JavaScript months are 0-indexed
    
    // Academic year starts in February in Uganda
    if (month >= 2) {
      return year;
    } else {
      return year - 1;
    }
  },
  
  // Get current term based on date
  getCurrentTerm() {
    const now = new Date();
    const month = now.getMonth() + 1;
    
    if (month >= 2 && month <= 5) {
      return 1; // Term 1: February - May
    } else if (month >= 6 && month <= 9) {
      return 2; // Term 2: June - September
    } else {
      return 3; // Term 3: October - January
    }
  }
};

// String utilities
const StringUtils = {
  // Capitalize first letter
  capitalize(str) {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  },
  
  // Convert to title case
  toTitleCase(str) {
    if (!str) return '';
    return str.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  },
  
  // Generate random ID
  generateId(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
  
  // Truncate text
  truncate(str, length = 50) {
    if (!str) return '';
    if (str.length <= length) return str;
    return str.substring(0, length) + '...';
  }
};

// Validation utilities
const ValidationUtils = {
  // Validate email
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },
  
  // Validate phone number (Uganda format)
  isValidPhone(phone) {
    const phoneRegex = /^(\+256|0)[7-9]\d{8}$/;
    return phoneRegex.test(phone);
  },
  
  // Validate national ID (Uganda format)
  isValidNationalId(id) {
    const idRegex = /^[A-Z]{2}\d{7}[A-Z]$/;
    return idRegex.test(id);
  },
  
  // Validate required field
  isRequired(value) {
    return value !== null && value !== undefined && value.toString().trim() !== '';
  },
  
  // Validate grade (0-3 for CA, 0-100 integers for exams)
  isValidGrade(grade, type = 'exam') {
    const num = parseFloat(grade);
    if (isNaN(num)) return false;

    if (type === 'ca') {
      // CA scores can be decimals (0.0-3.0)
      return num >= 0 && num <= 3;
    } else {
      // Exam marks must be integers (0-100)
      return Number.isInteger(num) && num >= 0 && num <= 100;
    }
  }
};

// Grade calculation utilities for Two-Tier Assessment System
const GradeUtils = {
  // Note: All weight calculations are handled by database triggers automatically
  // These methods are for display and validation purposes only

  // Get final grade from pre-calculated weights (no calculation, just sum)
  getFinalGradeFromWeights(caWeight, examWeight) {
    if (caWeight === null || caWeight === undefined || examWeight === null || examWeight === undefined) {
      return null;
    }
    return caWeight + examWeight; // Simple sum of pre-calculated weights
  },
  
  // Get letter grade from percentage using database boundaries
  // This method requires GradeBoundariesComponents to be loaded and initialized
  getLetterGrade(percentage, subjectLevel = 'o_level', isSubsidiary = false) {
    if (percentage === null || percentage === undefined) return 'N/A';

    // Use the grade boundaries component - required for proper operation
    if (window.GradeBoundariesComponents && window.GradeBoundariesComponents.getGradeLetterForPercentage) {
      if (subjectLevel === 'a_level') {
        return window.GradeBoundariesComponents.getGradeLetterForPercentage(percentage, isSubsidiary);
      } else {
        // For O-Level, use the O-Level boundaries
        return window.GradeBoundariesComponents.getOLevelGradeLetterForPercentage(percentage);
      }
    }

    console.error('GradeBoundariesComponents not available. Grade boundaries must be properly configured.');
    return 'N/A';
  },



  // Get grade description from database boundaries
  getGradeDescription(letterGrade, subjectLevel = 'o_level') {
    // Use database boundaries - required for proper operation
    if (window.GradeBoundariesComponents && window.GradeBoundariesComponents.state) {
      const boundaries = subjectLevel === 'a_level'
        ? window.GradeBoundariesComponents.state.aLevelPrincipalBoundaries?.data
        : window.GradeBoundariesComponents.state.oLevelBoundaries?.data;

      if (boundaries) {
        const boundary = boundaries.find(b => b.grade_letter === letterGrade);
        if (boundary) {
          return boundary.grade_descriptor;
        }
      }
    }

    console.error('Grade boundaries not available. Database boundaries must be properly configured.');
    return 'N/A';
  },
  
  // Calculate class average
  calculateClassAverage(grades) {
    if (!grades || grades.length === 0) return 0;

    const validGrades = grades.filter(grade => grade !== null && grade !== undefined && !isNaN(grade));
    if (validGrades.length === 0) return 0;

    const sum = validGrades.reduce((acc, grade) => acc + parseFloat(grade), 0);
    return Math.round((sum / validGrades.length) * 100) / 100;
  },

  // Validate exam weights for two-tier assessment system
  validateExamWeights(examWeights) {
    if (!examWeights || examWeights.length === 0) {
      return { isValid: false, message: 'No exam weights provided' };
    }

    const totalWeight = examWeights.reduce((sum, weight) => sum + (weight || 0), 0);

    if (totalWeight !== 100) {
      return {
        isValid: false,
        message: `Exam weights must total 100% for summative assessment portion. Current total: ${totalWeight}%`
      };
    }

    return { isValid: true, message: 'Exam weights are valid' };
  },

  // Display helper: Show exam contribution breakdown (for UI display only)
  getExamContributionsDisplay(examScores, examWeights) {
    if (!examScores || !examWeights || examScores.length !== examWeights.length) {
      return [];
    }

    return examScores.map((score, index) => {
      const weight = examWeights[index];

      return {
        score: score,
        weight: weight,
        display_weight: `${weight}%`,
        display_contribution: `${weight}% of Summative (80%)`
      };
    });
  }
};

// UI utilities
const UIUtils = {
  // Show loading spinner
  showLoading(element, text = 'Loading...') {
    if (element) {
      element.innerHTML = `
        <div class="loading-spinner">
          <div class="spinner"></div>
          <span>${text}</span>
        </div>
      `;
    }
  },
  
  // Hide loading spinner
  hideLoading(element, content = '') {
    if (element) {
      element.innerHTML = content;
    }
  },
  
  // Show notification
  showNotification(message, type = 'info', duration = 5000) {
    const container = document.getElementById('notification-container');
    if (!container) return;
    
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-message">${message}</span>
        <button class="notification-close">&times;</button>
      </div>
    `;
    
    container.appendChild(notification);
    
    // Auto remove after duration
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, duration);
    
    // Manual close
    notification.querySelector('.notification-close').addEventListener('click', () => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    });
  },
  
  // Confirm dialog
  showConfirm(message, callback) {
    if (confirm(message)) {
      callback();
    }
  },
  
  // Format number with commas
  formatNumber(num) {
    if (num === null || num === undefined) return '0';
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
};

// Export utilities to global scope
window.DateUtils = DateUtils;
window.StringUtils = StringUtils;
window.ValidationUtils = ValidationUtils;
window.GradeUtils = GradeUtils;
window.UIUtils = UIUtils;
