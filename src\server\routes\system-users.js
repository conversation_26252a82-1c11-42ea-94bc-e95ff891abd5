const express = require('express');
const bcrypt = require('bcryptjs');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');


const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get all system users
router.get('/', async (req, res) => {
  try {
    const query = `
      SELECT
        id, username, email, first_name, last_name, middle_name,
        role, phone_number, date_of_birth, gender,
        profile_picture, is_active,
        created_at, updated_at
      FROM system_users
      ORDER BY created_at DESC
    `;
    
    const result = await executeQuery(query, []);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get system users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve system users'
    });
  }
});

// Get system user by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT
        id, username, email, first_name, last_name, middle_name,
        role, phone_number, date_of_birth, gender,
        profile_picture, is_active,
        created_at, updated_at
      FROM system_users
      WHERE id = ?
    `;
    
    const result = await executeQuery(query, [id]);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'System user not found'
      });
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get system user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve system user'
    });
  }
});

// Create new system user
router.post('/', async (req, res) => {
  try {
    const {
      username, email, password, first_name, last_name, middle_name,
      phone_number, date_of_birth, gender, role, profile_picture
    } = req.body;

    console.log('📝 Creating new system user:', {
      username,
      email,
      first_name,
      last_name,
      hasPassword: !!password
    });

    // Validate required fields
    if (!username || !email || !password || !first_name || !last_name) {
      console.error('❌ Missing required fields');
      return res.status(400).json({
        success: false,
        message: 'Username, email, password, first name, and last name are required'
      });
    }

    // Validate password strength
    const passwordRequirements = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
    };

    const metRequirements = Object.values(passwordRequirements).filter(Boolean).length;
    const hasMinLength = passwordRequirements.length;
    const hasThreeOfFour = metRequirements >= 3;

    if (!hasMinLength || !hasThreeOfFour) {
      console.error('❌ Password does not meet requirements');
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long and include at least 3 of: uppercase letter, lowercase letter, number, special character'
      });
    }

    // Check if username or email already exists
    const checkQuery = `
      SELECT id FROM system_users 
      WHERE username = ? OR email = ?
    `;
    
    const checkResult = await executeQuery(checkQuery, [username, email]);
    
    if (!checkResult.success) {
      throw new Error(checkResult.error);
    }

    if (checkResult.data.length > 0) {
      console.error('❌ Username or email already exists');
      return res.status(409).json({
        success: false,
        message: 'Username or email already exists'
      });
    }

    // Hash password
    console.log('🔐 Hashing password...');
    const hashedPassword = await bcrypt.hash(password, 10);

    // Insert new user
    const insertQuery = `
      INSERT INTO system_users (
        username, email, password, first_name, last_name, middle_name,
        role, phone_number, date_of_birth, gender, profile_picture,
        is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, TRUE, NOW(), NOW())
    `;

    console.log('💾 Inserting user into database...');
    // Validate role
    const validRoles = ['system_admin', 'super_user'];
    const userRole = validRoles.includes(role) ? role : 'system_admin';

    // Handle optional fields - convert undefined/empty to null (consistent with student registration)
    const processedMiddleName = middle_name || null;
    const processedDateOfBirth = date_of_birth || null;
    const processedProfilePicture = (profile_picture && typeof profile_picture === 'string' && profile_picture.trim() !== '') ? profile_picture : null;

    const insertResult = await executeQuery(insertQuery, [
      username,
      email,
      hashedPassword,
      first_name,
      last_name,
      processedMiddleName,
      userRole,
      phone_number,
      processedDateOfBirth,
      gender,
      processedProfilePicture
    ]);

    if (!insertResult.success) {
      console.error('❌ Database insert failed:', insertResult.error);
      throw new Error(insertResult.error);
    }

    console.log('✅ User created successfully with ID:', insertResult.data.insertId);

    // Get the created user (without password)
    const newUserQuery = `
      SELECT
        id, username, email, first_name, last_name, middle_name,
        role, phone_number, date_of_birth, gender,
        profile_picture, is_active, created_at
      FROM system_users
      WHERE id = ?
    `;

    const newUserResult = await executeQuery(newUserQuery, [insertResult.data.insertId]);

    if (!newUserResult.success) {
      throw new Error(newUserResult.error);
    }

    if (newUserResult.data.length === 0) {
      throw new Error('Failed to retrieve created user');
    }

    res.status(201).json({
      success: true,
      message: 'System user created successfully',
      data: newUserResult.data[0]
    });

  } catch (error) {
    console.error('Create system user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create system user'
    });
  }
});

// Update system user
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      username, email, first_name, last_name, middle_name,
      phone_number, date_of_birth, gender, is_active, profile_picture
    } = req.body;

    // Check if user exists
    const checkQuery = 'SELECT id FROM system_users WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);
    
    if (!checkResult.success) {
      throw new Error(checkResult.error);
    }

    if (checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'System user not found'
      });
    }

    // Check if username or email already exists for other users
    const duplicateQuery = `
      SELECT id FROM system_users 
      WHERE (username = ? OR email = ?) AND id != ?
    `;
    
    const duplicateResult = await executeQuery(duplicateQuery, [username, email, id]);
    
    if (!duplicateResult.success) {
      throw new Error(duplicateResult.error);
    }

    if (duplicateResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Username or email already exists'
      });
    }

    // Update user
    const updateQuery = `
      UPDATE system_users SET
        username = ?, email = ?, first_name = ?, last_name = ?, middle_name = ?,
        phone_number = ?, date_of_birth = ?, gender = ?,
        is_active = ?, profile_picture = ?, updated_at = NOW()
      WHERE id = ?
    `;

    // Handle optional fields - convert undefined/empty to null (consistent with student registration)
    const processedMiddleName = middle_name || null;
    const processedDateOfBirth = date_of_birth || null;
    const processedProfilePicture = (profile_picture && typeof profile_picture === 'string' && profile_picture.trim() !== '') ? profile_picture : null;

    const updateResult = await executeQuery(updateQuery, [
      username,
      email,
      first_name,
      last_name,
      processedMiddleName,
      phone_number,
      processedDateOfBirth,
      gender,
      is_active !== undefined ? is_active : true,
      processedProfilePicture,
      id
    ]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    // Get updated user
    const updatedUserQuery = `
      SELECT
        id, username, email, first_name, last_name, middle_name,
        role, phone_number, date_of_birth, gender,
        is_active, profile_picture, updated_at
      FROM system_users
      WHERE id = ?
    `;

    const updatedUserResult = await executeQuery(updatedUserQuery, [id]);

    res.json({
      success: true,
      message: 'System user updated successfully',
      data: updatedUserResult.data[0]
    });

  } catch (error) {
    console.error('Update system user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update system user'
    });
  }
});

// Delete system user
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Prevent deletion of the last admin user
    const adminCountQuery = 'SELECT COUNT(*) as count FROM system_users WHERE role = "system_admin" AND is_active = TRUE';
    const adminCountResult = await executeQuery(adminCountQuery, []);

    if (adminCountResult.success && adminCountResult.data[0].count <= 1) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete the last active system administrator'
      });
    }

    // Delete user (middleware already validated permissions and user existence)
    const deleteQuery = 'DELETE FROM system_users WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'System user deleted successfully'
    });

  } catch (error) {
    console.error('Delete system user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete system user'
    });
  }
});

// Update system user password
router.put('/:id/password', async (req, res) => {
  try {
    const { id } = req.params;
    const { current_password, new_password } = req.body;

    // Validate required fields
    if (!current_password || !new_password) {
      return res.status(400).json({
        success: false,
        message: 'Current password and new password are required'
      });
    }

    // Validate password strength
    const passwordRequirements = {
      length: new_password.length >= 8,
      uppercase: /[A-Z]/.test(new_password),
      lowercase: /[a-z]/.test(new_password),
      number: /[0-9]/.test(new_password),
      special: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(new_password)
    };

    const metRequirements = Object.values(passwordRequirements).filter(Boolean).length;
    const hasMinLength = passwordRequirements.length;
    const hasThreeOfFour = metRequirements >= 3;

    if (!hasMinLength || !hasThreeOfFour) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long and include at least 3 of: uppercase letter, lowercase letter, number, special character'
      });
    }

    // Check if user exists and get current password
    const userQuery = 'SELECT id, password FROM system_users WHERE id = ?';
    const userResult = await executeQuery(userQuery, [id]);

    if (!userResult.success) {
      throw new Error(userResult.error);
    }

    if (userResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'System user not found'
      });
    }

    const user = userResult.data[0];

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(current_password, user.password);

    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(new_password, 10);

    // Update password
    const updateQuery = `
      UPDATE system_users SET
        password = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const updateResult = await executeQuery(updateQuery, [hashedNewPassword, id]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    res.json({
      success: true,
      message: 'Password updated successfully'
    });

  } catch (error) {
    console.error('Update password error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update password'
    });
  }
});

// Admin password reset (no current password required)
router.put('/:id/reset-password', async (req, res) => {
  try {
    const { id } = req.params;
    const { new_password } = req.body;

    // Validate required fields
    if (!new_password) {
      return res.status(400).json({
        success: false,
        message: 'New password is required'
      });
    }

    // Validate password strength
    const passwordRequirements = {
      length: new_password.length >= 8,
      uppercase: /[A-Z]/.test(new_password),
      lowercase: /[a-z]/.test(new_password),
      number: /[0-9]/.test(new_password),
      special: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(new_password)
    };

    const metRequirements = Object.values(passwordRequirements).filter(Boolean).length;
    const hasMinLength = passwordRequirements.length;
    const hasThreeOfFour = metRequirements >= 3;

    if (!hasMinLength || !hasThreeOfFour) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long and include at least 3 of: uppercase letter, lowercase letter, number, special character'
      });
    }

    // Check if user exists
    const userQuery = 'SELECT id FROM system_users WHERE id = ?';
    const userResult = await executeQuery(userQuery, [id]);

    if (!userResult.success) {
      throw new Error(userResult.error);
    }

    if (userResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'System user not found'
      });
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(new_password, 10);

    // Update password
    const updateQuery = `
      UPDATE system_users SET
        password = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const updateResult = await executeQuery(updateQuery, [hashedNewPassword, id]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    res.json({
      success: true,
      message: 'Password reset successfully'
    });

  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset password'
    });
  }
});



module.exports = router;
