// SmartReport - Grade Boundaries Management Components
// Comprehensive grade boundary management for O-Level and A-Level

// Uses global API services: window.GradeBoundariesAPI
// Uses global config: window.SRConfig
// Uses environment configuration: ../config/environment.js

// Shared Grade Boundaries Validation Utility
const GradeBoundariesValidator = {
  // Real-time validation for boundary inputs
  validateBoundaryInput(inputElement, boundaryType = 'o-level') {
    const inputValue = parseInt(inputElement.value);

    // Clear previous validation styling
    this.clearValidationStyling(inputElement);

    // Basic range validation
    if (inputValue < 0 || inputValue > 100) {
      this.showInputError(inputElement, 'Value must be between 0 and 100');
      return false;
    }

    // Get all boundaries for continuous validation
    const boundaries = this.getAllBoundariesFromTable(boundaryType);
    if (boundaries.length === 0) return true;

    // Validate continuous boundaries
    const validationResult = this.validateContinuousBoundaries(boundaries, boundaryType);

    if (!validationResult.isValid) {
      // Find which input caused the issue and highlight it
      const problematicInputs = this.findProblematicInputs(validationResult.errors, boundaryType);
      problematicInputs.forEach(input => {
        this.showInputError(input.element, input.message);
      });
      return false;
    }

    // If validation passes, show success styling
    this.showInputSuccess(inputElement);
    return true;
  },

  // Get all boundaries from the current table
  getAllBoundariesFromTable(boundaryType) {
    const tbody = document.getElementById('grade-boundaries-table-body');
    if (!tbody) return [];

    const boundaries = [];
    const rows = tbody.querySelectorAll('tr[data-boundary-id]');

    rows.forEach(row => {
      const gradeField = boundaryType === 'o-level' ? 'grade_letter' : 'grade_code';
      const gradeInput = row.querySelector(`input[id^="${gradeField}_"]`);
      const minInput = row.querySelector('input[id^="min_"]');
      const maxInput = row.querySelector('input[id^="max_"]');

      if (gradeInput && minInput && maxInput && minInput.value && maxInput.value) {
        const gradeValue = gradeInput.value;
        const minValue = parseInt(minInput.value);
        const maxValue = parseInt(maxInput.value);

        if (!isNaN(minValue) && !isNaN(maxValue)) {
          boundaries.push({
            [gradeField]: gradeValue,
            min_percentage: minValue,
            max_percentage: maxValue,
            minInput: minInput,
            maxInput: maxInput
          });
        }
      }
    });

    return boundaries;
  },

  // Validate continuous boundaries with detailed error reporting
  validateContinuousBoundaries(boundaries, boundaryType) {
    const errors = [];

    // Sort boundaries by max percentage (descending)
    const sortedBoundaries = [...boundaries].sort((a, b) => b.max_percentage - a.max_percentage);

    // Validate each boundary individually
    for (let i = 0; i < sortedBoundaries.length; i++) {
      const boundary = sortedBoundaries[i];

      // Check min < max for each boundary
      if (boundary.min_percentage >= boundary.max_percentage) {
        errors.push({
          type: 'invalid_range',
          boundary: boundary,
          message: `Min (${boundary.min_percentage}%) must be less than Max (${boundary.max_percentage}%)`,
          inputs: [boundary.minInput, boundary.maxInput]
        });
      }
    }

    // Validate first boundary (highest grade) must have max = 100
    if (sortedBoundaries.length > 0) {
      const firstBoundary = sortedBoundaries[0];

      if (firstBoundary.max_percentage !== 100) {
        errors.push({
          type: 'highest_max',
          boundary: firstBoundary,
          message: `Highest grade must have maximum of 100%`,
          inputs: [firstBoundary.maxInput]
        });
      }
    }

    // Validate last boundary (lowest grade) must have min = 0
    if (sortedBoundaries.length > 0) {
      const lastBoundary = sortedBoundaries[sortedBoundaries.length - 1];

      if (lastBoundary.min_percentage !== 0) {
        errors.push({
          type: 'lowest_min',
          boundary: lastBoundary,
          message: `Lowest grade must have minimum of 0%`,
          inputs: [lastBoundary.minInput]
        });
      }
    }

    // Validate continuous boundaries (no gaps or overlaps)
    for (let i = 0; i < sortedBoundaries.length - 1; i++) {
      const current = sortedBoundaries[i];
      const next = sortedBoundaries[i + 1];
      const gradeField = boundaryType === 'o-level' ? 'grade_letter' : 'grade_code';

      // Current boundary's min should equal next boundary's max + 1
      if (current.min_percentage !== next.max_percentage + 1) {
        if (current.min_percentage > next.max_percentage + 1) {
          errors.push({
            type: 'gap',
            current: current,
            next: next,
            message: `Gap between ${next[gradeField]} (max: ${next.max_percentage}%) and ${current[gradeField]} (min: ${current.min_percentage}%)`,
            inputs: [next.maxInput, current.minInput]
          });
        } else {
          errors.push({
            type: 'overlap',
            current: current,
            next: next,
            message: `Overlap between ${next[gradeField]} (max: ${next.max_percentage}%) and ${current[gradeField]} (min: ${current.min_percentage}%)`,
            inputs: [next.maxInput, current.minInput]
          });
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  },

  // Find problematic inputs from validation errors
  findProblematicInputs(errors) {
    const problematicInputs = [];

    errors.forEach(error => {
      error.inputs.forEach(input => {
        problematicInputs.push({
          element: input,
          message: error.message
        });
      });
    });

    return problematicInputs;
  },

  // Show input error styling
  showInputError(inputElement, message) {
    inputElement.classList.remove('border-gray-300', 'border-green-500');
    inputElement.classList.add('border-red-500', 'bg-red-50');
    inputElement.title = message;
  },

  // Show input success styling
  showInputSuccess(inputElement) {
    inputElement.classList.remove('border-gray-300', 'border-red-500', 'bg-red-50');
    inputElement.classList.add('border-green-500');
    inputElement.title = '';
  },

  // Clear validation styling
  clearValidationStyling(inputElement) {
    inputElement.classList.remove('border-red-500', 'border-green-500', 'bg-red-50');
    inputElement.classList.add('border-gray-300');
    inputElement.title = '';
  }
};

const GradeBoundariesComponents = {
  // Component state
  state: {
    oLevelBoundaries: [],
    aLevelPaperBoundaries: [],
    loading: false,
    currentView: 'o-level', // 'o-level', 'a-level-paper'
    editingBoundary: null,
    showCreateModal: false,
    showEditModal: false
  },

  // Initialize component
  async init() {
    await this.loadInitialData();
  },

  // Load initial data
  async loadInitialData() {
    try {
      this.state.loading = true;

      console.log('🔄 Loading grade boundaries data...');

      // Use the API services for O-Level and A-Level with proper error handling
      const dataPromises = [
        this.loadOLevelBoundaries(),
        this.loadALevelPaperBoundaries()
      ];

      await Promise.allSettled(dataPromises);

      console.log('✅ Grade boundaries data loaded successfully');

    } catch (error) {
      console.error('❌ Failed to load grade boundaries data:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to load grade boundaries data', 'error');
      } else {
        alert('Error: Failed to load grade boundaries data');
      }
    } finally {
      this.state.loading = false;
    }
  },

  // Load O-Level boundaries with error handling
  async loadOLevelBoundaries() {
    try {
      console.log('🔄 Loading O-Level boundaries...');
      const result = await window.GradeBoundariesAPI.oLevel.getAll();
      this.state.oLevelBoundaries = result;
      console.log('✅ O-Level boundaries loaded:', result.data?.length || 0, 'boundaries');
    } catch (error) {
      console.error('❌ Failed to load O-Level boundaries:', error);
      this.state.oLevelBoundaries = { success: false, data: [] };
    }
  },



  // Load A-Level Paper boundaries with error handling
  async loadALevelPaperBoundaries() {
    try {
      console.log('🔄 Loading A-Level Paper boundaries...');
      const result = await window.GradeBoundariesAPI.aLevelPaper.getAll();
      this.state.aLevelPaperBoundaries = result;
      console.log('✅ A-Level Paper boundaries loaded:', result.data?.length || 0, 'boundaries');
    } catch (error) {
      console.error('❌ Failed to load A-Level Paper boundaries:', error);
      this.state.aLevelPaperBoundaries = { success: false, data: [] };
    }
  },

  // Create A-Level Paper boundary
  async createALevelPaperBoundary(boundaryData) {
    try {
      console.log('🔄 Creating A-Level Paper boundary...');
      const result = await window.GradeBoundariesAPI.aLevelPaper.create(boundaryData);

      if (result.success) {
        console.log('✅ A-Level Paper boundary created successfully');
        await this.loadALevelPaperBoundaries(); // Reload data
        return result;
      } else {
        throw new Error(result.message || 'Failed to create boundary');
      }
    } catch (error) {
      console.error('❌ Failed to create A-Level Paper boundary:', error);
      throw error;
    }
  },

  // Update A-Level Paper boundary
  async updateALevelPaperBoundary(id, boundaryData) {
    try {
      console.log('🔄 Updating A-Level Paper boundary...');
      const result = await window.GradeBoundariesAPI.aLevelPaper.update(id, boundaryData);

      if (result.success) {
        console.log('✅ A-Level Paper boundary updated successfully');
        await this.loadALevelPaperBoundaries(); // Reload data
        return result;
      } else {
        throw new Error(result.message || 'Failed to update boundary');
      }
    } catch (error) {
      console.error('❌ Failed to update A-Level Paper boundary:', error);
      throw error;
    }
  },

  // Delete A-Level Paper boundary
  async deleteALevelPaperBoundary(id) {
    try {
      console.log('🔄 Deleting A-Level Paper boundary...');
      const result = await window.GradeBoundariesAPI.aLevelPaper.delete(id);

      if (result.success) {
        console.log('✅ A-Level Paper boundary deleted successfully');
        await this.loadALevelPaperBoundaries(); // Reload data
        return result;
      } else {
        throw new Error(result.message || 'Failed to delete boundary');
      }
    } catch (error) {
      console.error('❌ Failed to delete A-Level Paper boundary:', error);
      throw error;
    }
  },

  // Get combined boundaries data
  getCombinedBoundaries() {
    const oLevelData = this.state.oLevelBoundaries?.data || [];
    const aLevelPaperData = this.state.aLevelPaperBoundaries?.data || [];

    return [
      ...oLevelData.map(b => ({ ...b, level: 'o_level' })),
      ...aLevelPaperData.map(b => ({ ...b, level: 'a_level_paper' }))
    ];
  }
};

// O-Level Grade Boundaries Component
const OLevelGradeBoundariesComponent = {
  // Render O-Level grade boundaries interface
  render() {
    // Show loading state if data is still loading
    if (GradeBoundariesComponents.state.loading) {
      // Use centralized loading state from page router
      const container = document.getElementById('content-area');
      if (container && window.PageRouter) {
        window.PageRouter.showLoadingState(container, 'Loading O-Level grade boundaries data...');
      }
      return '';
    }

    // Check if academic context is available
    const hasAcademicContext = window.AcademicContext &&
                              window.AcademicContext.getActiveAcademicYear() &&
                              window.AcademicContext.getActiveTerm();

    if (!hasAcademicContext) {
      return this.renderAcademicContextError();
    }

    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'O-Level Grade Boundaries',
          'Manage grade boundaries for O-Level (UCE) assessment system',
        )}

        <!-- Grade Boundaries Management -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-4 border-b border-primary-200">
            <div class="flex items-center justify-between">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-primary-900 flex items-center">
                ${SRDesignSystem.components.icon('fas fa-chart-bar', 'base', 'primary-600')}
                <span class="ml-3">Grade Boundaries</span>
              </h3>
              ${SRDesignSystem.forms.button('edit-boundaries', 'Edit Boundaries', 'primary', {
                icon: 'fas fa-edit',
                onclick: 'OLevelGradeBoundariesComponent.enableEditing()'
              })}
            </div>
          </div>

          <div class="p-6">

          <!-- Grade Boundaries Validation Info -->
          <div class="bg-amber-50 border border-amber-200 rounded-lg ${SRDesignSystem.responsive.spacing.padding} mb-6">
            <div class="flex items-start">
              <div class="mt-1 mr-3">
                ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', 'base', 'amber-500')}
              </div>
              <div>
                <h4 class="${SRDesignSystem.responsive.text.sm} font-semibold text-amber-900 mb-1">Grade Boundaries Requirements</h4>
                <div class="${SRDesignSystem.responsive.text.sm} text-amber-800 space-y-1">
                  <p>• <strong>Highest grade:</strong> Must have maximum of 100%</p>
                  <p>• <strong>Lowest grade:</strong> Must have minimum of 0%</p>
                  <p>• <strong>Continuous boundaries:</strong> No gaps or overlaps allowed (e.g., A: 80-100%, B: 60-79%, C: 40-59%, D: 20-39%, E: 0-19%)</p>
                  <p>• <strong>Each boundary:</strong> Min % must be less than Max %</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Grade Boundaries Table -->
          <div class="overflow-x-auto border border-gray-200 rounded-lg">
            <table class="${SRDesignSystem.responsive.table.base}">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Grade</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-center ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Min %</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-center ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Max %</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Descriptor</th>
                </tr>
              </thead>
              <tbody id="grade-boundaries-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Grade boundaries will be populated here -->
              </tbody>
            </table>
          </div>

          <!-- Add Grade Boundary Link (shown only in edit mode) -->
          <div id="add-grade-boundary-link" class="hidden mt-4">
            <button onclick="OLevelGradeBoundariesComponent.addNewGradeBoundary()"
                    class="text-blue-600 hover:text-blue-800 ${SRDesignSystem.responsive.text.sm} font-medium flex items-center">
              ${SRDesignSystem.components.icon('fas fa-plus', 'sm', 'current')}
              <span class="ml-2">Add Grade Boundary</span>
            </button>
          </div>

            <!-- Edit Actions -->
            <div id="edit-actions" class="hidden mt-6 flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
              ${SRDesignSystem.forms.button('cancel-edit', 'Cancel', 'secondary', {
                onclick: 'OLevelGradeBoundariesComponent.cancelEditing()'
              })}
              ${SRDesignSystem.forms.button('save-boundaries', 'Save', 'primary', {
                onclick: 'OLevelGradeBoundariesComponent.saveBoundaries()'
              })}
            </div>
          </div>
        </div>

      </div>
    `;
  },

  // Render academic context error
  renderAcademicContextError() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'O-Level Grade Boundaries',
          'Configure grade boundaries'
        )}

        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-600')}
            </div>
            <div class="ml-4">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-red-900">Academic Context Required</h3>
              <p class="text-red-700 mt-2">
                You cannot configure grade boundaries without setting up the academic year and terms first.
              </p>
              <p class="text-red-700 mt-1">
                Please go to <strong>Academic Year → Setup</strong> to configure your academic year and terms before configuring grade boundaries.
              </p>
              <div class="mt-4 text-center">
                ${SRDesignSystem.forms.button('setup-academic-o-level-boundaries', 'Setup Academic Year & Terms', 'danger', {
                  icon: 'fas fa-calendar-plus',
                  onclick: 'OLevelGradeBoundariesComponent.navigateToAcademicSetup()'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize O-Level grade boundaries component
  async init() {
    console.log('🔧 Initializing O-Level Grade Boundaries Component...');

    // Reset component state
    this.resetComponentState();

    // Check academic context first
    await window.AcademicContext.initialize();
    const activeYear = window.AcademicContext.getActiveAcademicYear();
    const activeTerm = window.AcademicContext.getActiveTerm();

    if (!activeYear || !activeTerm) {
      console.log('⚠️ No active academic year or term found - component will show error state');
      return;
    }

    // Load initial data first
    await GradeBoundariesComponents.loadInitialData();

    // Populate table (DOM elements should be available by now due to lifecycle manager)
    this.populateGradeBoundariesTable();
  },

  // Reset component state
  resetComponentState() {
    // Reset any component-specific state if needed
    console.log('🔄 O-Level Grade Boundaries Component state reset');
  },

  // Cleanup component
  cleanup() {
    console.log('🧹 Cleaning up O-Level Grade Boundaries Component...');
    this.resetComponentState();

    // Hide any open modals
    const modals = ['add-grade-boundary-modal', 'edit-grade-boundary-modal'];
    modals.forEach(modalId => {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.classList.add('hidden');
      }
    });

    console.log('✅ O-Level Grade Boundaries Component cleanup completed');
  },




  // Populate grade boundaries table
  populateGradeBoundariesTable() {
    const tbody = document.getElementById('grade-boundaries-table-body');
    if (!tbody) {
      console.warn('⚠️ Grade boundaries table body not found');
      return;
    }

    // Get O-Level boundaries from database via API
    const oLevelBoundaries = GradeBoundariesComponents.state.oLevelBoundaries;
    const boundaries = (oLevelBoundaries?.success && oLevelBoundaries?.data) ?
                      oLevelBoundaries.data : [];

    console.log('📊 Populating O-Level boundaries table with', boundaries.length, 'boundaries');

    if (!boundaries || boundaries.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="4" class="px-6 py-12 text-center text-gray-500">
            <div class="text-gray-400 mb-4">
              ${SRDesignSystem.components.icon('fas fa-chart-line', '4xl', 'gray-300')}
            </div>
            <p class="${SRDesignSystem.responsive.text.lg} font-medium">No grade boundaries found</p>
            <p class="${SRDesignSystem.responsive.text.sm}">Grade boundaries will be loaded automatically or you can configure them.</p>
          </td>
        </tr>
      `;
      return;
    }

    tbody.innerHTML = boundaries.map((boundary, index) => {
      // Only show delete button for boundaries beyond the first 5 (default ones)
      const isAdditionalBoundary = index >= 5;

      return `
        <tr data-boundary-id="${boundary.id}" data-is-default="${!isAdditionalBoundary}">
          <td class="${SRDesignSystem.responsive.table.cell}">
            <div class="flex items-center">
              <span class="inline-flex items-center justify-center w-8 h-8 rounded-full ${this.getGradeBadgeClass(boundary.grade_letter)} font-bold text-sm">
                ${boundary.grade_letter}
              </span>
            </div>
            <input type="hidden"
                   id="grade_letter_${boundary.grade_letter}"
                   value="${boundary.grade_letter}">
          </td>
          <td class="${SRDesignSystem.responsive.table.cell} text-center">
            <input type="number"
                   id="min_${boundary.grade_letter}"
                   value="${boundary.min_percentage}"
                   min="0"
                   max="100"
                   step="1"
                   disabled
                   class="w-20 px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 disabled:text-gray-500">
          </td>
          <td class="${SRDesignSystem.responsive.table.cell} text-center">
            <input type="number"
                   id="max_${boundary.grade_letter}"
                   value="${boundary.max_percentage}"
                   min="0"
                   max="100"
                   step="1"
                   disabled
                   class="w-20 px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 disabled:text-gray-500">
          </td>
          <td class="${SRDesignSystem.responsive.table.cell}">
            ${isAdditionalBoundary ? `
              <div class="flex items-center space-x-2">
                <input type="text"
                       id="descriptor_${boundary.grade_letter}"
                       value="${boundary.grade_descriptor}"
                       disabled
                       class="flex-1 px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 disabled:text-gray-500">
                <button onclick="OLevelGradeBoundariesComponent.deleteExistingGradeBoundary(${boundary.id}, '${boundary.grade_letter}')"
                        class="hidden delete-btn text-red-600 hover:text-red-900 p-1 hover:bg-red-50 rounded transition-colors"
                        title="Delete this grade boundary">
                  ${SRDesignSystem.components.icon('fas fa-trash', 'sm', 'current')}
                </button>
              </div>
            ` : `
              <input type="text"
                     id="descriptor_${boundary.grade_letter}"
                     value="${boundary.grade_descriptor}"
                     disabled
                     class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 disabled:text-gray-500">
            `}
          </td>
        </tr>
      `;
    }).join('');
  },



  // Get grade badge CSS class
  getGradeBadgeClass(grade) {
    const classes = {
      'A+': 'bg-emerald-600 text-white',
      'A': 'bg-green-600 text-white',
      'A-': 'bg-green-500 text-white',
      'B+': 'bg-blue-600 text-white',
      'B': 'bg-blue-500 text-white',
      'B-': 'bg-cyan-500 text-white',
      'C+': 'bg-purple-600 text-white',
      'C': 'bg-purple-500 text-white',
      'C-': 'bg-indigo-500 text-white',
      'D+': 'bg-orange-600 text-white',
      'D': 'bg-orange-500 text-white',
      'D-': 'bg-amber-500 text-white',
      'E+': 'bg-red-600 text-white',
      'E': 'bg-red-500 text-white',
      'E-': 'bg-pink-500 text-white',
      'F+': 'bg-rose-600 text-white',
      'F': 'bg-rose-500 text-white',
      'F-': 'bg-red-700 text-white',
      'G': 'bg-gray-600 text-white',
      'H': 'bg-slate-600 text-white',
      'I': 'bg-zinc-600 text-white',
      'J': 'bg-stone-600 text-white'
    };
    return classes[grade] || 'bg-gray-500 text-white';
  },



  // Enable editing mode
  enableEditing() {
    // Enable all input fields
    const inputs = document.querySelectorAll('#grade-boundaries-table-body input');
    inputs.forEach(input => {
      input.disabled = false;
      input.classList.remove('disabled:bg-gray-50', 'disabled:text-gray-500');

      // Add real-time validation event listeners for number inputs
      if (input.type === 'number' && (input.id.startsWith('min_') || input.id.startsWith('max_'))) {
        input.addEventListener('input', () => {
          GradeBoundariesValidator.validateBoundaryInput(input, 'o-level');
        });
        input.addEventListener('blur', () => {
          GradeBoundariesValidator.validateBoundaryInput(input, 'o-level');
        });
      }
    });

    // Show delete buttons for additional boundaries (beyond the first 5)
    const deleteButtons = document.querySelectorAll('#grade-boundaries-table-body .delete-btn');
    deleteButtons.forEach(btn => {
      btn.classList.remove('hidden');
    });

    // Show edit actions and add grade boundary link
    document.getElementById('edit-actions').classList.remove('hidden');
    document.getElementById('add-grade-boundary-link').classList.remove('hidden');

    // Hide edit button
    document.getElementById('edit-boundaries').style.display = 'none';
  },

  // Cancel editing mode
  cancelEditing() {
    // Clear all validation styling
    const inputs = document.querySelectorAll('#grade-boundaries-table-body input[type="number"]');
    inputs.forEach(input => {
      GradeBoundariesValidator.clearValidationStyling(input);
    });

    // Reload the table to reset values
    this.populateGradeBoundariesTable();

    // Hide delete buttons
    const deleteButtons = document.querySelectorAll('#grade-boundaries-table-body .delete-btn');
    deleteButtons.forEach(btn => {
      btn.classList.add('hidden');
    });

    // Hide edit actions and add grade boundary link
    document.getElementById('edit-actions').classList.add('hidden');
    document.getElementById('add-grade-boundary-link').classList.add('hidden');

    // Show edit button
    document.getElementById('edit-boundaries').style.display = 'inline-flex';
  },

  // Add new grade boundary row
  addNewGradeBoundary() {
    const tbody = document.getElementById('grade-boundaries-table-body');
    if (!tbody) return;

    // Generate a unique ID for the new boundary
    const newId = 'new_' + Date.now();

    // Create new row HTML with min percentage set to 0 and delete button
    const newRowHtml = `
      <tr id="row_${newId}" data-is-new="true">
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center space-x-2">
            <span id="badge_${newId}" class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-400 text-white font-bold text-sm">
              ?
            </span>
            <input type="text"
                   id="grade_letter_${newId}"
                   value=""
                   maxlength="2"
                   placeholder="Grade"
                   oninput="OLevelGradeBoundariesComponent.updateGradeBadge('${newId}', this.value)"
                   class="w-16 px-2 py-1 border border-gray-300 rounded-md text-center font-bold focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <input type="number"
                 id="min_${newId}"
                 value="0"
                 min="0"
                 max="100"
                 step="1"
                 title="Min percentage for new grade boundary (will be validated for continuity)"
                 class="w-20 px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <input type="number"
                 id="max_${newId}"
                 value=""
                 min="0"
                 max="100"
                 step="1"
                 placeholder="Max %"
                 class="w-20 px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200">
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center space-x-2">
            <input type="text"
                   id="descriptor_${newId}"
                   value=""
                   placeholder="Grade descriptor"
                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <button onclick="OLevelGradeBoundariesComponent.deleteNewGradeBoundary('${newId}')"
                    class="text-red-600 hover:text-red-900 p-1"
                    title="Delete this grade boundary">
              ${SRDesignSystem.components.icon('fas fa-trash', 'sm', 'current')}
            </button>
          </div>
        </td>
      </tr>
    `;

    // Add the new row to the table
    tbody.insertAdjacentHTML('beforeend', newRowHtml);

    // Focus on the grade letter input
    document.getElementById(`grade_letter_${newId}`).focus();
  },

  // Update grade badge color as user types
  updateGradeBadge(boundaryId, gradeValue) {
    const badge = document.getElementById(`badge_${boundaryId}`);
    if (badge) {
      const grade = gradeValue.trim().toUpperCase();
      if (grade) {
        badge.textContent = grade;
        badge.className = `inline-flex items-center justify-center w-8 h-8 rounded-full ${this.getGradeBadgeClass(grade)} font-bold text-sm`;
      } else {
        badge.textContent = '?';
        badge.className = 'inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-400 text-white font-bold text-sm';
      }
    }
  },

  // Delete new grade boundary row
  deleteNewGradeBoundary(boundaryId) {
    const row = document.getElementById(`row_${boundaryId}`);
    if (row && row.getAttribute('data-is-new') === 'true') {
      // Confirm deletion
      if (confirm('Are you sure you want to delete this grade boundary?')) {
        row.remove();
      }
    }
  },

  // Delete existing grade boundary (only for additional boundaries beyond the first 5)
  async deleteExistingGradeBoundary(boundaryId, gradeLetter) {
    // Confirm deletion
    const confirmMessage = `Are you sure you want to delete grade boundary "${gradeLetter}"? This action cannot be undone.`;

    if (confirm(confirmMessage)) {
      try {
        // Find the row to check if it's deletable
        const row = document.querySelector(`tr[data-boundary-id="${boundaryId}"]`);
        if (!row || row.getAttribute('data-is-default') === 'true') {
          if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
            window.SRDesignSystem.notifications.show('Cannot delete default grade boundaries. Only additional boundaries can be removed.', 'error');
          }
          return;
        }

        // Call the API to delete the boundary
        const result = await window.GradeBoundariesAPI.oLevel.delete(boundaryId);

        if (result.success) {
          // Remove the row from the DOM
          row.remove();

          // Show success notification
          if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
            window.SRDesignSystem.notifications.show(`Grade boundary "${gradeLetter}" deleted successfully.`, 'success');
          }

          // Reload the data to ensure consistency
          await GradeBoundariesComponents.loadInitialData();
          this.populateGradeBoundariesTable();
        } else {
          if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
            window.SRDesignSystem.notifications.show(result.message || 'Failed to delete grade boundary', 'error');
          }
        }
      } catch (error) {
        console.error('Delete grade boundary error:', error);
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('Failed to delete grade boundary: ' + error.message, 'error');
        }
      }
    }
  },

  // Get suggested values for new grade boundary
  getSuggestedBoundaryValues() {
    const tbody = document.getElementById('grade-boundaries-table-body');
    const rows = tbody.querySelectorAll('tr');
    const existingBoundaries = [];

    // Collect existing boundaries
    rows.forEach(row => {
      const minInput = row.querySelector('input[id^="min_"]');
      const maxInput = row.querySelector('input[id^="max_"]');

      if (minInput && maxInput && minInput.value && maxInput.value) {
        existingBoundaries.push({
          min: parseInt(minInput.value),
          max: parseInt(maxInput.value)
        });
      }
    });

    if (existingBoundaries.length === 0) {
      return { suggestedMin: 0, suggestedMax: 100 };
    }

    // Sort by max percentage (descending)
    existingBoundaries.sort((a, b) => b.max - a.max);

    // Find the lowest max percentage
    const lowestMax = Math.min(...existingBoundaries.map(b => b.max));

    return {
      suggestedMin: 0,
      suggestedMax: lowestMax - 1,
      guidance: `New boundary should typically have max < ${lowestMax}% to maintain order`
    };
  },

  // Save boundaries
  async saveBoundaries() {
    try {
      const boundaries = [];

      // Collect data from all rows in the table (both existing and new)
      const tbody = document.getElementById('grade-boundaries-table-body');
      const rows = tbody.querySelectorAll('tr');

      rows.forEach(row => {
        // Get all inputs in this row
        const gradeLetterInput = row.querySelector('input[id^="grade_letter_"]');
        const minInput = row.querySelector('input[id^="min_"]');
        const maxInput = row.querySelector('input[id^="max_"]');
        const descriptorInput = row.querySelector('input[id^="descriptor_"]');

        if (gradeLetterInput && minInput && maxInput && descriptorInput) {
          const gradeLetter = gradeLetterInput.value.trim().toUpperCase();
          const minPercentage = parseInt(minInput.value);
          const maxPercentage = parseInt(maxInput.value);
          const gradeDescriptor = descriptorInput.value.trim();

          // Skip empty rows
          if (!gradeLetter && !gradeDescriptor && !minInput.value && !maxInput.value) {
            return;
          }

          // Validate required fields
          if (!gradeLetter || !gradeDescriptor) {
            throw new Error(`Grade letter and descriptor are required for all grades.`);
          }

          // Validate INT values and range
          if (isNaN(minPercentage) || isNaN(maxPercentage) ||
              minPercentage < 0 || maxPercentage > 100 ||
              minPercentage > maxPercentage) {
            throw new Error(`Invalid percentage range for grade ${gradeLetter}. Must be integers 0-100 with min ≤ max.`);
          }

          boundaries.push({
            grade_letter: gradeLetter,
            min_percentage: minPercentage,
            max_percentage: maxPercentage,
            grade_descriptor: gradeDescriptor
          });
        }
      });

      // Validate boundaries using shared validator
      const validationResult = GradeBoundariesValidator.validateContinuousBoundaries(boundaries, 'o-level');
      if (!validationResult.isValid) {
        // Show first validation error
        if (validationResult.errors.length > 0) {
          const firstError = validationResult.errors[0];
          if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
            window.SRDesignSystem.notifications.show(firstError.message, 'error');
          } else {
            alert('Validation Error: ' + firstError.message);
          }
        }
        return;
      }

      if (window.SRDesignSystem && window.SRDesignSystem.forms) {
        window.SRDesignSystem.forms.setButtonLoading('save-boundaries', true);
      }

      // Use the O-Level API service
      const result = await window.GradeBoundariesAPI.oLevel.update(boundaries);

      if (result.success) {
        // Show success notification if available
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('O-Level grade boundaries updated successfully!', 'success');
        } else {
          alert('O-Level grade boundaries updated successfully!');
        }
        await GradeBoundariesComponents.loadInitialData();
        this.cancelEditing();
      } else {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to update boundaries', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to update boundaries'));
        }
      }

    } catch (error) {
      console.error('Save boundaries error:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show(error.message || 'Failed to update boundaries', 'error');
      } else {
        alert('Error: ' + (error.message || 'Failed to update boundaries'));
      }
    } finally {
      if (window.SRDesignSystem && window.SRDesignSystem.forms) {
        window.SRDesignSystem.forms.setButtonLoading('save-boundaries', false);
      }
    }
  },

  // Validate boundaries
  validateBoundaries(boundaries) {
    // O-Level has 5 default boundaries that cannot be deleted, so this check is not needed
    // if (boundaries.length === 0) - This should never happen for O-Level

    // Sort boundaries by max percentage (descending) to ensure proper order
    const sortedBoundaries = [...boundaries].sort((a, b) => b.max_percentage - a.max_percentage);

    // Validate each boundary individually
    for (let i = 0; i < sortedBoundaries.length; i++) {
      const boundary = sortedBoundaries[i];

      // Check min < max for each boundary
      if (boundary.min_percentage >= boundary.max_percentage) {
        this.showValidationError(`Invalid range for grade ${boundary.grade_letter}: Min (${boundary.min_percentage}%) must be less than Max (${boundary.max_percentage}%)`);
        return false;
      }
    }

    // Validate first boundary (highest grade) must have max = 100
    const firstBoundary = sortedBoundaries[0];
    if (firstBoundary.max_percentage !== 100) {
      this.showValidationError(`The highest grade (${firstBoundary.grade_letter}) must have a maximum of 100%. Currently: ${firstBoundary.max_percentage}%`);
      return false;
    }

    // Validate last boundary (lowest grade) must have min = 0
    const lastBoundary = sortedBoundaries[sortedBoundaries.length - 1];
    if (lastBoundary.min_percentage !== 0) {
      this.showValidationError(`The lowest grade (${lastBoundary.grade_letter}) must have a minimum of 0%. Currently: ${lastBoundary.min_percentage}%`);
      return false;
    }

    // Validate continuous boundaries (no gaps or overlaps)
    for (let i = 0; i < sortedBoundaries.length - 1; i++) {
      const current = sortedBoundaries[i];
      const next = sortedBoundaries[i + 1];

      // Current boundary's min should equal next boundary's max + 1
      if (current.min_percentage !== next.max_percentage + 1) {
        if (current.min_percentage > next.max_percentage + 1) {
          this.showValidationError(`Gap detected between grades ${next.grade_letter} (max: ${next.max_percentage}%) and ${current.grade_letter} (min: ${current.min_percentage}%). Boundaries must be continuous.`);
        } else {
          this.showValidationError(`Overlap detected between grades ${next.grade_letter} (max: ${next.max_percentage}%) and ${current.grade_letter} (min: ${current.min_percentage}%). Boundaries cannot overlap.`);
        }
        return false;
      }
    }

    return true;
  },

  // Show validation error message
  showValidationError(message) {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, 'error');
    } else {
      alert('Validation Error: ' + message);
    }
  },

  // Validate A-Level Principal boundaries
  validateALevelPrincipalBoundaries(boundaries) {
    // A-Level boundaries are fixed/default from database, so this check is not needed
    // if (boundaries.length === 0) - This should never happen for A-Level

    // Sort boundaries by max percentage (descending) to ensure proper order
    const sortedBoundaries = [...boundaries].sort((a, b) => b.max_percentage - a.max_percentage);

    // Validate each boundary individually
    for (let i = 0; i < sortedBoundaries.length; i++) {
      const boundary = sortedBoundaries[i];

      // Check min < max for each boundary
      if (boundary.min_percentage >= boundary.max_percentage) {
        this.showValidationError(`Invalid range for grade ${boundary.grade_letter}: Min (${boundary.min_percentage}%) must be less than Max (${boundary.max_percentage}%)`);
        return false;
      }
    }

    // Validate first boundary (highest grade) must have max = 100
    const firstBoundary = sortedBoundaries[0];
    if (firstBoundary.max_percentage !== 100) {
      this.showValidationError(`The highest grade (${firstBoundary.grade_letter}) must have a maximum of 100%. Currently: ${firstBoundary.max_percentage}%`);
      return false;
    }

    // Validate last boundary (lowest grade) must have min = 0
    const lastBoundary = sortedBoundaries[sortedBoundaries.length - 1];
    if (lastBoundary.min_percentage !== 0) {
      this.showValidationError(`The lowest grade (${lastBoundary.grade_letter}) must have a minimum of 0%. Currently: ${lastBoundary.min_percentage}%`);
      return false;
    }

    // Validate continuous boundaries (no gaps or overlaps)
    for (let i = 0; i < sortedBoundaries.length - 1; i++) {
      const current = sortedBoundaries[i];
      const next = sortedBoundaries[i + 1];

      // Current boundary's min should equal next boundary's max + 1
      if (current.min_percentage !== next.max_percentage + 1) {
        if (current.min_percentage > next.max_percentage + 1) {
          this.showValidationError(`Gap detected between grades ${next.grade_letter} (max: ${next.max_percentage}%) and ${current.grade_letter} (min: ${current.min_percentage}%). A-Level Principal boundaries must be continuous.`);
        } else {
          this.showValidationError(`Overlap detected between grades ${next.grade_letter} (max: ${next.max_percentage}%) and ${current.grade_letter} (min: ${current.min_percentage}%). A-Level Principal boundaries cannot overlap.`);
        }
        return false;
      }
    }

    return true;
  },

  // Validate A-Level Subsidiary boundaries
  validateALevelSubsidiaryBoundaries(boundaries) {
    // A-Level boundaries are fixed/default from database, so this check is not needed
    // if (boundaries.length === 0) - This should never happen for A-Level

    // Sort boundaries by max percentage (descending) to ensure proper order
    const sortedBoundaries = [...boundaries].sort((a, b) => b.max_percentage - a.max_percentage);

    // Validate each boundary individually
    for (let i = 0; i < sortedBoundaries.length; i++) {
      const boundary = sortedBoundaries[i];

      // Check min < max for each boundary
      if (boundary.min_percentage >= boundary.max_percentage) {
        this.showValidationError(`Invalid range for grade ${boundary.grade_letter}: Min (${boundary.min_percentage}%) must be less than Max (${boundary.max_percentage}%)`);
        return false;
      }
    }

    // Validate first boundary (highest grade) must have max = 100
    const firstBoundary = sortedBoundaries[0];
    if (firstBoundary.max_percentage !== 100) {
      this.showValidationError(`The highest grade (${firstBoundary.grade_letter}) must have a maximum of 100%. Currently: ${firstBoundary.max_percentage}%`);
      return false;
    }

    // Validate last boundary (lowest grade) must have min = 0
    const lastBoundary = sortedBoundaries[sortedBoundaries.length - 1];
    if (lastBoundary.min_percentage !== 0) {
      this.showValidationError(`The lowest grade (${lastBoundary.grade_letter}) must have a minimum of 0%. Currently: ${lastBoundary.min_percentage}%`);
      return false;
    }

    // Validate continuous boundaries (no gaps or overlaps)
    for (let i = 0; i < sortedBoundaries.length - 1; i++) {
      const current = sortedBoundaries[i];
      const next = sortedBoundaries[i + 1];

      // Current boundary's min should equal next boundary's max + 1
      if (current.min_percentage !== next.max_percentage + 1) {
        if (current.min_percentage > next.max_percentage + 1) {
          this.showValidationError(`Gap detected between grades ${next.grade_letter} (max: ${next.max_percentage}%) and ${current.grade_letter} (min: ${current.min_percentage}%). A-Level Subsidiary boundaries must be continuous.`);
        } else {
          this.showValidationError(`Overlap detected between grades ${next.grade_letter} (max: ${next.max_percentage}%) and ${current.grade_letter} (min: ${current.min_percentage}%). A-Level Subsidiary boundaries cannot overlap.`);
        }
        return false;
      }
    }

    return true;
  },

  // Navigate to academic year setup
  navigateToAcademicSetup() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('academic-year-setup');
    }
  }
};



// A-Level Grade Boundaries Component
const ALevelGradeBoundariesComponent = {
  // Component state
  isEditing: false,

  // Render A-Level grade boundaries interface
  render() {
    // Show loading state if data is still loading
    if (GradeBoundariesComponents.state.loading) {
      const container = document.getElementById('content-area');
      if (container && window.PageRouter) {
        window.PageRouter.showLoadingState(container, 'Loading A-Level grade boundaries data...');
      }
      return '';
    }

    // Check if academic context is available
    const hasAcademicContext = window.AcademicContext &&
                              window.AcademicContext.getActiveAcademicYear() &&
                              window.AcademicContext.getActiveTerm();

    if (!hasAcademicContext) {
      return this.renderAcademicContextError();
    }

    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'A-Level Grade Boundaries',
          'Manage grade boundaries for A-Level (UACE) assessment system',
        )}

        <!-- Grade Boundaries Management -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="bg-gradient-to-r from-primary-50 to-primary-100 px-6 py-4 border-b border-primary-200">
            <div class="flex items-center justify-between">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-primary-900 flex items-center">
                ${SRDesignSystem.components.icon('fas fa-chart-bar', 'base', 'primary-600')}
                <span class="ml-3">Grade Boundaries</span>
              </h3>
              ${SRDesignSystem.forms.button('edit-boundaries', 'Edit Boundaries', 'primary', {
                icon: 'fas fa-edit',
                onclick: 'ALevelGradeBoundariesComponent.enableEditing()'
              })}
            </div>
          </div>

          <div class="p-6">

          <!-- Grade Boundaries Validation Info -->
          <div class="bg-amber-50 border border-amber-200 rounded-lg ${SRDesignSystem.responsive.spacing.padding} mb-6">
            <div class="flex items-start">
              <div class="mt-1 mr-3">
                ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', 'base', 'amber-500')}
              </div>
              <div>
                <h4 class="${SRDesignSystem.responsive.text.sm} font-semibold text-amber-900 mb-1">Grade Boundaries Requirements</h4>
                <div class="${SRDesignSystem.responsive.text.sm} text-amber-800 space-y-1">
                  <p>• <strong>Highest grade:</strong> Must have maximum of 100%</p>
                  <p>• <strong>Lowest grade:</strong> Must have minimum of 0%</p>
                  <p>• <strong>Continuous boundaries:</strong> No gaps or overlaps allowed (e.g., D1: 80-100%, D2: 75-79%, C3: 66-74%)</p>
                  <p>• <strong>Each boundary:</strong> Min % must be less than Max %</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Grade Boundaries Table -->
          <div class="overflow-x-auto border border-gray-200 rounded-lg">
            <table class="${SRDesignSystem.responsive.table.base}">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Grade</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-center ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Min %</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-center ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Max %</th>
                  <th class="${SRDesignSystem.responsive.table.cell} text-left ${SRDesignSystem.responsive.text.xs} font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Descriptor</th>
                </tr>
              </thead>
              <tbody id="grade-boundaries-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Grade boundaries will be populated here -->
              </tbody>
            </table>
          </div>

          <!-- Edit Actions -->
          <div id="edit-actions" class="hidden mt-6 flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            ${SRDesignSystem.forms.button('cancel-edit', 'Cancel', 'secondary', {
              onclick: 'ALevelGradeBoundariesComponent.cancelEditing()'
            })}
              ${SRDesignSystem.forms.button('save-boundaries', 'Save', 'primary', {
                onclick: 'ALevelGradeBoundariesComponent.saveBoundaries()'
              })}
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Render academic context error
  renderAcademicContextError() {
    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'A-Level Grade Boundaries',
          'Manage grade boundaries for A-Level (UACE) assessment system'
        )}

        <div class="bg-red-50 border border-red-200 rounded-xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', '2xl', 'red-600')}
            </div>
            <div class="ml-4">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-red-900">Academic Context Required</h3>
              <p class="text-red-700 mt-2">
                You cannot configure A-Level grade boundaries without setting up the academic year and terms first.
              </p>
              <p class="text-red-700 mt-1">
                Please go to <strong>Academic Year → Setup</strong> to configure your academic year and terms before configuring grade boundaries.
              </p>
              <div class="mt-4 text-center">
                ${SRDesignSystem.forms.button('setup-academic-a-level-boundaries', 'Setup Academic Year & Terms', 'danger', {
                  icon: 'fas fa-calendar-plus',
                  onclick: 'ALevelGradeBoundariesComponent.navigateToAcademicSetup()'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize A-Level grade boundaries component
  async init() {
    console.log('🔧 Initializing A-Level Grade Boundaries Component...');

    // Reset component state
    this.resetComponentState();

    // Check academic context first
    await window.AcademicContext.initialize();
    const activeYear = window.AcademicContext.getActiveAcademicYear();
    const activeTerm = window.AcademicContext.getActiveTerm();

    if (!activeYear || !activeTerm) {
      console.log('⚠️ No active academic year or term found - component will show error state');
      return;
    }

    // Load initial data first
    await GradeBoundariesComponents.loadInitialData();

    // Populate table (DOM elements should be available by now due to lifecycle manager)
    this.populateGradeBoundariesTable();
  },

  // Reset component state
  resetComponentState() {
    this.isEditing = false;
  },

  // Populate grade boundaries table
  populateGradeBoundariesTable() {
    const tbody = document.getElementById('grade-boundaries-table-body');
    if (!tbody) {
      console.warn('⚠️ Grade boundaries table body not found');
      return;
    }

    // Get A-Level boundaries from database via API
    const aLevelBoundaries = GradeBoundariesComponents.state.aLevelPaperBoundaries;
    const boundaries = (aLevelBoundaries?.success && aLevelBoundaries?.data) ?
                      aLevelBoundaries.data : [];

    console.log('📊 Populating A-Level boundaries table with', boundaries.length, 'boundaries');

    if (!boundaries || boundaries.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="4" class="px-6 py-12 text-center text-gray-500">
            <div class="text-gray-400 mb-4">
              ${SRDesignSystem.components.icon('fas fa-chart-line', '4xl', 'gray-300')}
            </div>
            <p class="${SRDesignSystem.responsive.text.lg} font-medium">No grade boundaries found</p>
            <p class="${SRDesignSystem.responsive.text.sm}">Grade boundaries will be loaded automatically or you can configure them.</p>
          </td>
        </tr>
      `;
      return;
    }

    tbody.innerHTML = boundaries.map((boundary) => {
      return `
        <tr data-boundary-id="${boundary.id}" data-is-default="true">
          <td class="${SRDesignSystem.responsive.table.cell}">
            <div class="flex items-center">
              <span class="inline-flex items-center justify-center w-8 h-8 rounded-full ${this.getGradeBadgeClass(boundary.grade_code)} font-bold text-sm">
                ${boundary.grade_code}
              </span>
            </div>
            <input type="hidden"
                   id="grade_code_${boundary.grade_code}"
                   value="${boundary.grade_code}">
          </td>
          <td class="${SRDesignSystem.responsive.table.cell} text-center">
            <input type="number"
                   id="min_${boundary.grade_code}"
                   value="${boundary.min_percentage}"
                   min="0"
                   max="100"
                   step="1"
                   disabled
                   class="w-20 px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 disabled:text-gray-500">
          </td>
          <td class="${SRDesignSystem.responsive.table.cell} text-center">
            <input type="number"
                   id="max_${boundary.grade_code}"
                   value="${boundary.max_percentage}"
                   min="0"
                   max="100"
                   step="1"
                   disabled
                   class="w-20 px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 disabled:text-gray-500">
          </td>
          <td class="${SRDesignSystem.responsive.table.cell}">
            <input type="text"
                   id="descriptor_${boundary.grade_code}"
                   value="${boundary.grade_descriptor}"
                   disabled
                   class="w-full px-3 py-2 ${SRDesignSystem.responsive.text.sm} border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 disabled:text-gray-500">
          </td>
        </tr>
      `;
    }).join('');
  },

  // Get grade badge class for styling
  getGradeBadgeClass(gradeCode) {
    const gradeColors = {
      'D1': 'bg-green-100 text-green-800',
      'D2': 'bg-green-100 text-green-800',
      'C3': 'bg-blue-100 text-blue-800',
      'C4': 'bg-blue-100 text-blue-800',
      'C5': 'bg-blue-100 text-blue-800',
      'C6': 'bg-blue-100 text-blue-800',
      'P7': 'bg-yellow-100 text-yellow-800',
      'P8': 'bg-yellow-100 text-yellow-800',
      'F9': 'bg-red-100 text-red-800'
    };
    return gradeColors[gradeCode] || 'bg-gray-100 text-gray-800';
  },

  // Enable editing mode
  enableEditing() {
    this.isEditing = true;

    // Enable all input fields
    const inputs = document.querySelectorAll('#grade-boundaries-table-body input');
    inputs.forEach(input => {
      input.disabled = false;
      input.classList.remove('disabled:bg-gray-50', 'disabled:text-gray-500');

      // Add real-time validation event listeners for number inputs
      if (input.type === 'number' && (input.id.startsWith('min_') || input.id.startsWith('max_'))) {
        input.addEventListener('input', () => {
          GradeBoundariesValidator.validateBoundaryInput(input, 'a-level');
        });
        input.addEventListener('blur', () => {
          GradeBoundariesValidator.validateBoundaryInput(input, 'a-level');
        });
      }
    });

    // Show edit actions
    const editActions = document.getElementById('edit-actions');
    if (editActions) {
      editActions.classList.remove('hidden');
    }

    // Hide edit button
    const editButton = document.getElementById('edit-boundaries');
    if (editButton) {
      editButton.style.display = 'none';
    }

    console.log('✏️ A-Level grade boundaries editing enabled');
  },

  // Cancel editing
  cancelEditing() {
    this.isEditing = false;

    // Clear all validation styling
    const inputs = document.querySelectorAll('#grade-boundaries-table-body input[type="number"]');
    inputs.forEach(input => {
      GradeBoundariesValidator.clearValidationStyling(input);
    });

    // Reload the original data
    this.populateGradeBoundariesTable();

    // Hide edit actions
    const editActions = document.getElementById('edit-actions');
    if (editActions) {
      editActions.classList.add('hidden');
    }

    // Show edit button
    const editButton = document.getElementById('edit-boundaries');
    if (editButton) {
      editButton.style.display = 'inline-flex';
    }

    console.log('❌ A-Level grade boundaries editing cancelled');
  },

  // Save boundaries
  async saveBoundaries() {
    try {
      console.log('💾 Saving A-Level grade boundaries...');

      // Get all boundaries from the table
      const boundaries = [];
      const aLevelBoundaries = GradeBoundariesComponents.state.aLevelPaperBoundaries;
      const originalBoundaries = (aLevelBoundaries?.success && aLevelBoundaries?.data) ?
                                aLevelBoundaries.data : [];

      // For each original boundary, get the updated values
      originalBoundaries.forEach(boundary => {
        const minInput = document.getElementById(`min_${boundary.grade_code}`);
        const maxInput = document.getElementById(`max_${boundary.grade_code}`);
        const descriptorInput = document.getElementById(`descriptor_${boundary.grade_code}`);

        if (minInput && maxInput && descriptorInput) {
          boundaries.push({
            id: boundary.id,
            grade_code: boundary.grade_code,
            min_percentage: parseInt(minInput.value),
            max_percentage: parseInt(maxInput.value),
            grade_descriptor: descriptorInput.value.trim()
          });
        }
      });

      // Validate boundaries using shared validator
      const validationResult = GradeBoundariesValidator.validateContinuousBoundaries(boundaries, 'a-level');
      if (!validationResult.isValid) {
        // Show first validation error
        if (validationResult.errors.length > 0) {
          const firstError = validationResult.errors[0];
          if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
            window.SRDesignSystem.notifications.show(firstError.message, 'error');
          } else {
            alert('Validation Error: ' + firstError.message);
          }
        }
        return;
      }

      // Use the A-Level API service
      const result = await window.GradeBoundariesAPI.aLevelPaper.update(boundaries);

      if (result.success) {
        // Show success notification if available
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show('A-Level grade boundaries updated successfully!', 'success');
        } else {
          alert('A-Level grade boundaries updated successfully!');
        }
        await GradeBoundariesComponents.loadInitialData();
        this.cancelEditing();
      } else {
        if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
          window.SRDesignSystem.notifications.show(result.message || 'Failed to update boundaries', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to update boundaries'));
        }
      }
    } catch (error) {
      console.error('❌ Error saving A-Level grade boundaries:', error);
      if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
        window.SRDesignSystem.notifications.show('Failed to save grade boundaries', 'error');
      } else {
        alert('Error: Failed to save grade boundaries');
      }
    }
  },

  // Validate boundaries
  validateBoundaries() {
    try {
      // Get all boundaries from the table
      const boundaries = [];
      const aLevelBoundaries = GradeBoundariesComponents.state.aLevelPaperBoundaries;
      const originalBoundaries = (aLevelBoundaries?.success && aLevelBoundaries?.data) ?
                                aLevelBoundaries.data : [];

      // For each original boundary, get the updated values
      originalBoundaries.forEach(boundary => {
        const minInput = document.getElementById(`min_${boundary.grade_code}`);
        const maxInput = document.getElementById(`max_${boundary.grade_code}`);
        const descriptorInput = document.getElementById(`descriptor_${boundary.grade_code}`);

        if (minInput && maxInput && descriptorInput) {
          boundaries.push({
            id: boundary.id,
            grade_code: boundary.grade_code,
            min_percentage: parseInt(minInput.value),
            max_percentage: parseInt(maxInput.value),
            grade_descriptor: descriptorInput.value.trim()
          });
        }
      });

      // Sort boundaries by max_percentage in descending order
      const sortedBoundaries = [...boundaries].sort((a, b) => b.max_percentage - a.max_percentage);

      // Validate each boundary individually
      for (let i = 0; i < sortedBoundaries.length; i++) {
        const boundary = sortedBoundaries[i];

        // Check min < max for each boundary
        if (boundary.min_percentage >= boundary.max_percentage) {
          this.showValidationError(`Invalid range for grade ${boundary.grade_code}: Min (${boundary.min_percentage}%) must be less than Max (${boundary.max_percentage}%)`);
          return false;
        }
      }

      // Validate first boundary (highest grade) must have max = 100
      const firstBoundary = sortedBoundaries[0];
      if (firstBoundary.max_percentage !== 100) {
        this.showValidationError(`The highest grade (${firstBoundary.grade_code}) must have a maximum of 100%. Currently: ${firstBoundary.max_percentage}%`);
        return false;
      }

      // Validate last boundary (lowest grade) must have min = 0
      const lastBoundary = sortedBoundaries[sortedBoundaries.length - 1];
      if (lastBoundary.min_percentage !== 0) {
        this.showValidationError(`The lowest grade (${lastBoundary.grade_code}) must have a minimum of 0%. Currently: ${lastBoundary.min_percentage}%`);
        return false;
      }

      // Validate continuous boundaries (no gaps or overlaps)
      // For A-Level UACE system: D1: 80-100%, D2: 75-79%, C3: 66-74%
      // Current boundary's min should equal next boundary's max + 1
      for (let i = 0; i < sortedBoundaries.length - 1; i++) {
        const current = sortedBoundaries[i];
        const next = sortedBoundaries[i + 1];

        if (current.min_percentage !== next.max_percentage + 1) {
          if (current.min_percentage > next.max_percentage + 1) {
            this.showValidationError(`Gap detected between grades ${next.grade_code} (max: ${next.max_percentage}%) and ${current.grade_code} (min: ${current.min_percentage}%). Boundaries must be continuous.`);
          } else {
            this.showValidationError(`Overlap detected between grades ${next.grade_code} (max: ${next.max_percentage}%) and ${current.grade_code} (min: ${current.min_percentage}%). Boundaries cannot overlap.`);
          }
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('❌ Error validating boundaries:', error);
      this.showValidationError('An error occurred while validating boundaries');
      return false;
    }
  },

  // Show validation error
  showValidationError(message) {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, 'error');
    } else {
      alert('Validation Error: ' + message);
    }
  },

  // Navigate to academic year setup
  navigateToAcademicSetup() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('academic-year-setup');
    }
  }
};

// Export components to global scope
window.OLevelGradeBoundariesComponent = OLevelGradeBoundariesComponent;
window.ALevelGradeBoundariesComponent = ALevelGradeBoundariesComponent;
window.GradeBoundariesComponents = GradeBoundariesComponents;
