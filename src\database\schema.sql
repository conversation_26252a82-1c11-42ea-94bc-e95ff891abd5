-- SmartReport Database Schema
-- Version: 1.0.0
-- Designed for competency-based assessment system
--
-- UGANDA ASSESSMENT SYSTEM:
--
-- O-LEVEL & A-LEVEL ASSESSMENT:
-- Continuous Assessment (CA) - 20% Weight:
--   - Topic-based assessments
--   - Activities of Integration
--   - Projects
--   - Assignments
--   - Group work
--   - Practical exercises
-- End-of-Term Examinations - 80% Weight
-- Final Grade = CA (20%) + Exam (80%)
--

-- Create database with proper character set
CREATE DATABASE IF NOT EXISTS smartreport_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE smartreport_db;

-- =============================================
-- 1. SYSTEM CONFIGURATION TABLES
-- =============================================

-- School settings table (stores all system configurations)
CREATE TABLE IF NOT EXISTS school_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NULL,
    setting_type ENUM('string', 'integer', 'boolean', 'json') NOT NULL,
    category VARCHAR(50) NOT NULL,
    description TEXT NULL,
    is_editable BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key),
    INDEX idx_category (category)
);


-- O-Level Grade Boundaries (Competency-Based Curriculum)
CREATE TABLE IF NOT EXISTS o_level_grade_boundaries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    grade_letter VARCHAR(2) UNIQUE NOT NULL COMMENT 'A, B+, B, C+, C, D, E',
    min_percentage INT NOT NULL COMMENT 'Minimum percentage (0-100)',
    max_percentage INT NOT NULL COMMENT 'Maximum percentage (0-100)',
    grade_descriptor VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_o_level_grade_letter (grade_letter),
    CONSTRAINT chk_o_level_percentage_range CHECK (min_percentage >= 0 AND max_percentage <= 100 AND min_percentage <= max_percentage)
);

-- A-Level Paper Grade Boundaries (UACE Grading Scale: D1, D2, C3, C4, C5, C6, P7, P8, F9)
CREATE TABLE IF NOT EXISTS a_level_paper_grade_boundaries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    grade_code VARCHAR(2) UNIQUE NOT NULL COMMENT 'D1, D2, C3, C4, C5, C6, P7, P8, F9',
    min_percentage INT NOT NULL COMMENT 'Minimum percentage (0-100)',
    max_percentage INT NOT NULL COMMENT 'Maximum percentage (0-100)',
    grade_descriptor VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_grade_code (grade_code),
    CONSTRAINT chk_percentage_range CHECK (min_percentage >= 0 AND max_percentage <= 100 AND min_percentage <= max_percentage)
);

-- =============================================
-- 2. USER MANAGEMENT TABLES 
-- =============================================

-- System users table (for system administrators)
CREATE TABLE IF NOT EXISTS system_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    middle_name VARCHAR(50) NULL,
    role ENUM('super_user', 'system_admin') NOT NULL DEFAULT 'system_admin',
    phone_number VARCHAR(20) NULL,
    date_of_birth DATE NULL,
    gender ENUM('Male', 'Female') NOT NULL,
    profile_picture VARCHAR(255) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_active (is_active)
);

-- =============================================
-- 3. ACADEMIC STRUCTURE TABLES
-- =============================================

-- Academic years table
CREATE TABLE IF NOT EXISTS academic_years (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(10) UNIQUE NOT NULL COMMENT 'Academic year (e.g., 2025, 2026)',
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT FALSE COMMENT 'Only one academic year can be active (current) at a time',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active_year (is_active),
    INDEX idx_name (name),
    CONSTRAINT chk_year_format CHECK (name REGEXP '^[0-9]{4}$'),
    CONSTRAINT chk_date_order CHECK (start_date < end_date)
);

-- Terms table
CREATE TABLE IF NOT EXISTS terms (
    id INT PRIMARY KEY AUTO_INCREMENT,
    academic_year_id INT NOT NULL,
    name VARCHAR(50) NOT NULL COMMENT 'e.g. Term 1',
    number TINYINT NOT NULL COMMENT '1, 2, or 3',
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT FALSE COMMENT 'Only one term can be active (current) at a time',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_active_term (is_active),
    UNIQUE KEY uk_term_year (academic_year_id, number)
    -- Note: Date and term number validation moved to business logic layer
);

-- O-Level Subjects table (S1-S4)
CREATE TABLE IF NOT EXISTS o_level_subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    short_name VARCHAR(20) UNIQUE NOT NULL,
    subject_type ENUM(
        'Compulsory', 'Language',
        'Practical (pre-vocational)', 'Religious Education'
    ) NOT NULL,
    uneb_code VARCHAR(10) NOT NULL COMMENT 'Official UNEB subject codes (e.g., 112, 456)',
    exam_papers INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_subject_type (subject_type),
    INDEX idx_active (is_active),
    INDEX idx_uneb_code (uneb_code)
);

-- A-Level Subjects table (S5-S6)
CREATE TABLE IF NOT EXISTS a_level_subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    short_name VARCHAR(20) UNIQUE NOT NULL,
    subject_type ENUM('Principal', 'Subsidiary') NOT NULL,
    uneb_code VARCHAR(10) NOT NULL COMMENT 'Official UNEB subject codes (e.g., P425, P510, S101)',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_subject_type (subject_type),
    INDEX idx_active (is_active),
    INDEX idx_uneb_code (uneb_code)
);

-- A-Level Subject Papers table (Normalized paper structure)
CREATE TABLE IF NOT EXISTS a_level_subject_papers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    subject_id INT NOT NULL,
    paper_number TINYINT NOT NULL COMMENT 'Paper number (1, 2, 3, etc.)',
    paper_name VARCHAR(100) NULL COMMENT 'Optional paper name (e.g., "Paper 1: Theory", "Paper 2: Practical")',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES a_level_subjects(id) ON DELETE CASCADE,
    INDEX idx_subject (subject_id),
    INDEX idx_paper_number (paper_number),
    INDEX idx_active (is_active),
    UNIQUE KEY uk_subject_paper_number (subject_id, paper_number),
    CONSTRAINT chk_paper_number CHECK (paper_number >= 1 AND paper_number <= 4)
);

-- =============================================
-- 3.5. LEVELS TABLE 
-- =============================================

-- Education Levels table (O-Level and A-Level) - the main education categories
CREATE TABLE IF NOT EXISTS education_levels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(10) UNIQUE NOT NULL COMMENT 'o_level, a_level',
    name VARCHAR(50) NOT NULL COMMENT 'O-Level, A-Level',
    display_name VARCHAR(100) NOT NULL COMMENT 'Ordinary Level, Advanced Level',
    sort_order INT NOT NULL COMMENT 'For ordering levels (1-2)',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_sort_order (sort_order),
    INDEX idx_active (is_active)
);

-- Classes table (S.1, S.2, S.3, S.4, S.5, S.6) - individual year groups within education levels
CREATE TABLE IF NOT EXISTS class_levels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(10) UNIQUE NOT NULL COMMENT 'e.g. s1, s2, s3, s4, s5, s6',
    name VARCHAR(50) NOT NULL COMMENT 'e.g. S.1, S.2, S.3, S.4, S.5, S.6',
    display_name VARCHAR(100) NOT NULL COMMENT 'e.g. Senior One, Senior Two, etc.',
    education_level_id INT NOT NULL COMMENT 'Reference to education_levels table',
    sort_order INT NOT NULL COMMENT 'For ordering classes (1-6)',
    streams_optional BOOLEAN DEFAULT TRUE COMMENT 'Whether streams are optional for this class',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (education_level_id) REFERENCES education_levels(id) ON DELETE RESTRICT,
    INDEX idx_education_level (education_level_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_active (is_active)
);

-- O-Level Subject-Class relationships (which classes each subject applies to)
CREATE TABLE IF NOT EXISTS o_level_subject_classes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    subject_id INT NOT NULL,
    class_level_id INT NOT NULL,
    subject_status ENUM('compulsory', 'elective') NOT NULL DEFAULT 'elective' COMMENT 'Subject status for this class level',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES o_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE CASCADE,
    UNIQUE KEY uk_subject_class (subject_id, class_level_id),
    INDEX idx_subject (subject_id),
    INDEX idx_class_level (class_level_id),
    INDEX idx_subject_status (subject_status)
);

-- A-Level subjects are available to all A-Level classes (S.5 and S.6) without restrictions

-- =============================================
-- 4. CLASS STRUCTURE TABLES
-- =============================================

-- Streams table for managing class stream divisions (permanent structure)
CREATE TABLE IF NOT EXISTS streams (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT 'e.g. A, B, C, D (O-Level), Sciences, Arts (A-Level)',
    stream_type ENUM('o_level', 'a_level') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_stream_type (stream_type)
);

-- Classes table (base classes only - streams are managed through stream_classes relationship)
CREATE TABLE IF NOT EXISTS classes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT 'e.g. S.1, S.2, S.3, S.4, S.5, S.6',
    class_level_id INT NOT NULL COMMENT 'Reference to class_levels table',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE RESTRICT,
    INDEX idx_class_level (class_level_id),
    INDEX idx_active (is_active),
    UNIQUE KEY uk_class_name (name)
    -- Note: Only base classes (S.1 to S.6) are stored here
    -- Stream assignments are handled through the stream_classes relationship table
);

-- Stream-Class relationships (permanent assignments)
CREATE TABLE IF NOT EXISTS stream_classes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    stream_id INT NOT NULL,
    class_level_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (stream_id) REFERENCES streams(id) ON DELETE CASCADE,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE CASCADE,
    INDEX idx_stream_class (stream_id, class_level_id),
    UNIQUE KEY uk_stream_class (stream_id, class_level_id)
);

-- =============================================
-- 5. TEACHER MANAGEMENT
-- =============================================

-- Teachers table
CREATE TABLE IF NOT EXISTS teachers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    middle_name VARCHAR(50) NULL,
    initials VARCHAR(10) NULL COMMENT 'Teacher initials (e.g., M.N.)',
    gender ENUM('Male', 'Female') NOT NULL,
    teacher_type ENUM('Class Teacher', 'Subject Teacher') DEFAULT 'Subject Teacher',
    joining_date DATE NULL,
    employment_status ENUM('active', 'inactive') DEFAULT 'active',
    passport_photo VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_employment_status (employment_status),
    INDEX idx_teacher_type (teacher_type),
    INDEX idx_initials (initials),
    INDEX idx_gender (gender)
);

-- Teacher subjects table for tracking teacher-subject assignments (for both Class and Subject Teachers)
CREATE TABLE IF NOT EXISTS teacher_subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    teacher_id INT NOT NULL,
    subject_id INT NOT NULL,
    subject_level ENUM('o_level', 'a_level') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    INDEX idx_teacher (teacher_id),
    INDEX idx_subject_level (subject_id, subject_level),
    UNIQUE KEY uk_teacher_subject_level (teacher_id, subject_id, subject_level)
    -- Note: Teacher subject reference validation moved to business logic layer
);

-- Class subject assignments (which subjects are taught in which classes and by which teachers)
CREATE TABLE IF NOT EXISTS class_subject_assignments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    class_id INT NOT NULL,
    subject_id INT NOT NULL,
    subject_level ENUM('o_level', 'a_level') NOT NULL,
    teacher_id INT NULL COMMENT 'Teacher assigned to teach this subject in this class',
    academic_year_id INT NOT NULL,
    periods_per_week TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE SET NULL,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    INDEX idx_class (class_id),
    INDEX idx_subject_level (subject_id, subject_level),
    INDEX idx_teacher (teacher_id),
    INDEX idx_academic_year (academic_year_id),
    UNIQUE KEY uk_class_subject_year (class_id, subject_id, subject_level, academic_year_id)
    -- Note: Teacher qualification validation moved to business logic layer
);

-- =============================================
-- 6. STUDENT MANAGEMENT
-- =============================================

-- O-Level Students Registration Table
CREATE TABLE IF NOT EXISTS o_level_students (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admission_number VARCHAR(50) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    middle_name VARCHAR(100) NULL,
    last_name VARCHAR(100) NOT NULL,
    gender ENUM('Male', 'Female') NOT NULL,
    date_of_birth DATE NULL COMMENT 'Student date of birth',

    -- Class Assignment Fields
    current_class_id INT NULL COMMENT 'Current O-Level class assignment',
    stream_id INT NULL COMMENT 'Stream assignment (optional for O-Level)',
    current_academic_year_id INT NULL COMMENT 'Current academic year',
    current_term_id INT NULL COMMENT 'Current term',

    -- Status and Administrative
    status ENUM('active', 'transferred', 'graduated', 'dropped', 'suspended') DEFAULT 'active',
    enrollment_date DATE NOT NULL COMMENT 'Date when student was first enrolled',
    passport_photo VARCHAR(255) NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign Keys
    FOREIGN KEY (current_class_id) REFERENCES classes(id) ON DELETE SET NULL,
    FOREIGN KEY (stream_id) REFERENCES streams(id) ON DELETE SET NULL,
    FOREIGN KEY (current_academic_year_id) REFERENCES academic_years(id) ON DELETE SET NULL,
    FOREIGN KEY (current_term_id) REFERENCES terms(id) ON DELETE SET NULL,

    -- Indexes
    INDEX idx_admission_number (admission_number),
    INDEX idx_names (first_name, last_name),
    INDEX idx_status (status),
    INDEX idx_gender (gender),
    INDEX idx_current_class (current_class_id),
    INDEX idx_stream (stream_id),
    INDEX idx_current_academic_year (current_academic_year_id),
    INDEX idx_current_term (current_term_id),
    INDEX idx_enrollment_date (enrollment_date),

    -- Constraints
    CONSTRAINT chk_admission_number_format CHECK (LENGTH(TRIM(admission_number)) >= 3),
    CONSTRAINT chk_name_not_empty CHECK (LENGTH(TRIM(first_name)) > 0 AND LENGTH(TRIM(last_name)) > 0)
);

-- A-Level Students Registration Table
CREATE TABLE IF NOT EXISTS a_level_students (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admission_number VARCHAR(50) UNIQUE NOT NULL COMMENT 'A-Level admission number',

    -- Personal Information
    first_name VARCHAR(100) NOT NULL,
    middle_name VARCHAR(100) NULL,
    last_name VARCHAR(100) NOT NULL,
    gender ENUM('Male', 'Female') NOT NULL,
    date_of_birth DATE NULL COMMENT 'Student date of birth',

    -- Current Class Assignment Fields
    current_class_id INT NOT NULL COMMENT 'Current A-Level class assignment',
    stream_id INT NULL COMMENT 'Stream assignment (Arts or Sciences)',
    current_academic_year_id INT NOT NULL COMMENT 'Current academic year',
    current_term_id INT NULL COMMENT 'Current term',

    -- Status and Administrative
    status ENUM('active', 'transferred', 'graduated', 'dropped', 'suspended') DEFAULT 'active',
    registration_date DATE NOT NULL DEFAULT (CURRENT_DATE) COMMENT 'Date of A-Level registration',
    passport_photo VARCHAR(255) NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,


    FOREIGN KEY (current_class_id) REFERENCES classes(id) ON DELETE RESTRICT,
    FOREIGN KEY (stream_id) REFERENCES streams(id) ON DELETE SET NULL,
    FOREIGN KEY (current_academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (current_term_id) REFERENCES terms(id) ON DELETE SET NULL,


    INDEX idx_admission_number (admission_number),
    INDEX idx_names (first_name, last_name),
    INDEX idx_current_class (current_class_id),
    INDEX idx_stream (stream_id),
    INDEX idx_current_academic_year (current_academic_year_id),
    INDEX idx_current_term (current_term_id),

    INDEX idx_registration_date (registration_date),
    INDEX idx_status (status),

    CONSTRAINT chk_admission_number_format CHECK (LENGTH(TRIM(admission_number)) >= 3),
    CONSTRAINT chk_name_not_empty CHECK (LENGTH(TRIM(first_name)) > 0 AND LENGTH(TRIM(last_name)) > 0)
);


-- =============================================
-- 7. STUDENT ENROLLMENT TRACKING
-- =============================================
-- Note: Enrollment tracking is now handled directly in o_level_students and a_level_students tables
-- This eliminates the need for a separate student_enrollments table


-- =============================================
-- 8. ASSESSMENT CONFIGURATION AND GRADING TABLES
-- =============================================

-- =============================================
-- 8. CA CONFIGURATION TABLES (Separate for O-Level and A-Level)
-- =============================================

-- O-Level CA Configuration Table (Subject-based)
-- Configure number of CAs per class-subject per term for O-Level
CREATE TABLE IF NOT EXISTS o_level_ca_configuration (
    id INT PRIMARY KEY AUTO_INCREMENT,
    class_id INT NOT NULL COMMENT 'References classes.id - Specific class (S.1, S.2, etc.)',
    subject_id INT NOT NULL COMMENT 'References o_level_subjects.id',
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,
    total_cas INT NOT NULL DEFAULT 3 COMMENT 'Total number of CAs for this class-subject in this term',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign key relationships
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES o_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,

    -- Indexes for performance
    INDEX idx_class_subject (class_id, subject_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_term (term_id),

    -- Unique constraint ensures one configuration per class-subject-term combination
    UNIQUE KEY uk_o_level_ca_config (class_id, subject_id, academic_year_id, term_id),

    -- Business rules
    CONSTRAINT chk_o_level_total_cas CHECK (total_cas >= 1 AND total_cas <= 6)
);

-- A-Level CA Configuration Table (Paper-based)
-- Configure number of CAs per class-subject-paper per term for A-Level
CREATE TABLE IF NOT EXISTS a_level_ca_configuration (
    id INT PRIMARY KEY AUTO_INCREMENT,
    class_id INT NOT NULL COMMENT 'References classes.id - Specific class (S.5, S.6)',
    subject_id INT NOT NULL COMMENT 'References a_level_subjects.id',
    subject_paper_id INT NOT NULL COMMENT 'References a_level_subject_papers.id',
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,
    total_cas INT NOT NULL DEFAULT 3 COMMENT 'Total number of CAs for this class-subject-paper in this term',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign key relationships
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES a_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_paper_id) REFERENCES a_level_subject_papers(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,

    -- Indexes for performance
    INDEX idx_class_subject (class_id, subject_id),
    INDEX idx_class_subject_paper (class_id, subject_paper_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_term (term_id),

    -- Unique constraint ensures one configuration per class-subject-paper-term combination
    UNIQUE KEY uk_a_level_ca_config (class_id, subject_paper_id, academic_year_id, term_id),

    -- Business rules
    CONSTRAINT chk_a_level_total_cas CHECK (total_cas >= 1 AND total_cas <= 6)
);

-- =============================================
-- 9. ASSESSMENT AND GRADING TABLES
-- =============================================

-- O-Level Subject Continuous Assessment Scores (CA - 20% weight)
CREATE TABLE IF NOT EXISTS o_level_subject_continuous_assessments_scores (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,

    -- Assessment Sequence and Identification
    ca_number TINYINT NOT NULL COMMENT 'CA sequence number within the term (1, 2, 3, etc.)',

    -- Competency Assessment
    competency_score DECIMAL(2,1) NOT NULL COMMENT 'Score field (0-3 scale for competency-based assessment)',

    -- Administrative
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (student_id) REFERENCES o_level_students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES o_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_subject (subject_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_term (term_id),
    INDEX idx_ca_number (ca_number),
    UNIQUE KEY uk_student_subject_term_ca (student_id, subject_id, term_id, ca_number),
    CONSTRAINT chk_competency_score CHECK (competency_score = 0.0 OR (competency_score >= 0.9 AND competency_score <= 3.0)),
    CONSTRAINT chk_ca_number CHECK (ca_number >= 1 AND ca_number <= 6)
    -- Note: Allows multiple CA assessments per term per subject
);


-- Exam Types Configuration
CREATE TABLE IF NOT EXISTS exam_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT 'e.g., Beginning of Term, Mid Term, End of Term, Mock Exam',
    short_name VARCHAR(20) NOT NULL COMMENT 'e.g., BOT, MID, EOT, MOCK',
    is_active BOOLEAN DEFAULT TRUE,
    sort_order TINYINT DEFAULT 1 COMMENT 'Order for display purposes',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_sort_order (sort_order),
    UNIQUE KEY uk_name (name),
    UNIQUE KEY uk_short_name (short_name)
);

-- Class-Exam Type Associations
-- This table defines which exam types are active for each class per term per academic year
-- Flexible system - any exam type can be assigned to any class based on school's needs
-- Admins configure these associations per term since exam requirements can vary by term
CREATE TABLE IF NOT EXISTS class_exam_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    class_id INT NOT NULL,
    exam_type_id INT NOT NULL,
    academic_year_id INT NOT NULL COMMENT 'Associations are per academic year',
    term_id INT NOT NULL COMMENT 'Associations are per term since exam requirements can vary by term',
    weight_percentage INT NOT NULL DEFAULT 0 COMMENT 'Weight percentage for this exam type in this class (1-100)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (exam_type_id) REFERENCES exam_types(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    INDEX idx_class (class_id),
    INDEX idx_exam_type (exam_type_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_term (term_id),
    UNIQUE KEY uk_class_exam_type_term_year (class_id, exam_type_id, term_id, academic_year_id),
    CONSTRAINT chk_weight_percentage CHECK (weight_percentage >= 1 AND weight_percentage <= 100)
);

-- O-Level Term Examinations (Subject-level exams)
CREATE TABLE IF NOT EXISTS o_level_term_examinations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    class_id INT NOT NULL,
    subject_id INT NOT NULL COMMENT 'References o_level_subjects table',
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,
    exam_type_id INT NOT NULL COMMENT 'Reference to exam_types table',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES o_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (exam_type_id) REFERENCES exam_types(id) ON DELETE RESTRICT,
    INDEX idx_class (class_id),
    INDEX idx_subject (subject_id),
    INDEX idx_term (term_id),
    INDEX idx_exam_type (exam_type_id),
    UNIQUE KEY uk_class_subject_term_exam_type (class_id, subject_id, term_id, exam_type_id)
);

-- A-Level Paper Examinations (Paper-level exams)
CREATE TABLE IF NOT EXISTS a_level_paper_examinations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    class_id INT NOT NULL,
    subject_paper_id INT NOT NULL COMMENT 'References a_level_subject_papers table',
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,
    exam_type_id INT NOT NULL COMMENT 'Reference to exam_types table',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_paper_id) REFERENCES a_level_subject_papers(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (exam_type_id) REFERENCES exam_types(id) ON DELETE RESTRICT,
    INDEX idx_class (class_id),
    INDEX idx_subject_paper (subject_paper_id),
    INDEX idx_term (term_id),
    INDEX idx_exam_type (exam_type_id),
    UNIQUE KEY uk_class_subject_paper_term_exam_type (class_id, subject_paper_id, term_id, exam_type_id)
);

-- O-Level Student Exam Marks (Subject-level marks)
CREATE TABLE IF NOT EXISTS o_level_student_exam_marks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    examination_id INT NOT NULL COMMENT 'References o_level_term_examinations table',
    marks_obtained INT NULL COMMENT 'Percentage score obtained (0-100), NULL if not taken',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES o_level_students(id) ON DELETE CASCADE,
    FOREIGN KEY (examination_id) REFERENCES o_level_term_examinations(id) ON DELETE CASCADE,

    INDEX idx_student (student_id),
    INDEX idx_examination (examination_id),

    INDEX idx_marks_obtained (marks_obtained),
    UNIQUE KEY uk_student_examination (student_id, examination_id),
    CONSTRAINT chk_marks_percentage_range CHECK (marks_obtained IS NULL OR (marks_obtained >= 0 AND marks_obtained <= 100))
);

-- A-Level Student Paper Exam Marks (Paper-level marks)
CREATE TABLE IF NOT EXISTS a_level_student_paper_exam_marks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    examination_id INT NOT NULL COMMENT 'References a_level_paper_examinations table',
    marks_obtained INT NULL COMMENT 'Percentage score obtained (0-100), NULL if not taken',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES a_level_students(id) ON DELETE CASCADE,
    FOREIGN KEY (examination_id) REFERENCES a_level_paper_examinations(id) ON DELETE CASCADE,

    INDEX idx_student (student_id),
    INDEX idx_examination (examination_id),

    INDEX idx_marks_obtained (marks_obtained),
    UNIQUE KEY uk_student_examination (student_id, examination_id),
    CONSTRAINT chk_marks_percentage_range_paper CHECK (marks_obtained IS NULL OR (marks_obtained >= 0 AND marks_obtained <= 100))
);


-- O-Level Subject CA Weights (Stores calculated 20% CA weight for each student-subject-term)
CREATE TABLE IF NOT EXISTS o_level_subject_ca_weights (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,

    -- CA Weight Calculation (20% of final grade)
    ca_average DECIMAL(2,1) NULL COMMENT 'Average of all CA scores for this student-subject-term (0.0-3.0 scale)',
    ca_weight_points INT NULL COMMENT 'CA weight converted to 20-point scale: (ca_average ÷ 3) × 20 (0-20)',

    -- Administrative
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (student_id) REFERENCES o_level_students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES o_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_subject (subject_id),
    INDEX idx_term (term_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_student_term (student_id, term_id),
    INDEX idx_subject_term (subject_id, term_id),
    UNIQUE KEY uk_student_subject_term (student_id, subject_id, term_id),
    CONSTRAINT chk_ca_average_range CHECK (ca_average IS NULL OR ca_average = 0.0 OR (ca_average >= 0.9 AND ca_average <= 3.0)),
    CONSTRAINT chk_ca_weight_points CHECK (ca_weight_points IS NULL OR (ca_weight_points >= 0 AND ca_weight_points <= 20))
);


-- A-Level Paper Continuous Assessment Scores (CA - 20% weight for each paper)
CREATE TABLE IF NOT EXISTS a_level_paper_continuous_assessments_scores (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_paper_id INT NOT NULL COMMENT 'References a_level_subject_papers table',
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,

    -- Assessment Sequence and Identification
    ca_number TINYINT NOT NULL COMMENT 'CA sequence number within the term (1, 2, 3, etc.)',

    -- Competency Assessment
    competency_score DECIMAL(2,1) NOT NULL COMMENT 'CA competency score (0.9-3.0 scale)',

    -- Administrative
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (student_id) REFERENCES a_level_students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_paper_id) REFERENCES a_level_subject_papers(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_subject_paper (subject_paper_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_term (term_id),
    INDEX idx_ca_number (ca_number),
    UNIQUE KEY uk_student_subject_paper_term_ca (student_id, subject_paper_id, term_id, ca_number),
    CONSTRAINT chk_competency_score_paper CHECK (competency_score = 0.0 OR (competency_score >= 0.9 AND competency_score <= 3.0)),
    CONSTRAINT chk_ca_number_paper CHECK (ca_number >= 1 AND ca_number <= 6)
);


-- A-Level Paper CA Weights (Stores calculated 20% CA weight for each student-paper-term)
CREATE TABLE IF NOT EXISTS a_level_paper_ca_weights (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_paper_id INT NOT NULL COMMENT 'References a_level_subject_papers table',
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,

    -- CA Weight Calculation (20% of final grade per paper)
    ca_average DECIMAL(2,1) NULL COMMENT 'Average of all CA scores for this student-paper-term (0.0-3.0 scale)',
    ca_weight_points INT NULL COMMENT 'CA weight converted to 20-point scale: (ca_average ÷ 3) × 20 (0-20)',

    -- Administrative
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (student_id) REFERENCES a_level_students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_paper_id) REFERENCES a_level_subject_papers(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_subject_paper (subject_paper_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_term (term_id),
    INDEX idx_student_term (student_id, term_id),
    INDEX idx_paper_term (subject_paper_id, term_id),
    UNIQUE KEY uk_student_subject_paper_term (student_id, subject_paper_id, term_id),
    CONSTRAINT chk_ca_average_range CHECK (ca_average IS NULL OR ca_average = 0.0 OR (ca_average >= 0.9 AND ca_average <= 3.0)),
    CONSTRAINT chk_ca_weight_points CHECK (ca_weight_points IS NULL OR (ca_weight_points >= 0 AND ca_weight_points <= 20))
);

-- O-Level Subject Exam Weights (Stores calculated 80% exam weight for each student-subject-term)
CREATE TABLE IF NOT EXISTS o_level_subject_exam_weights (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,

    -- Exam Weight Calculation (80% of final grade)
    exam_weight_points INT NULL COMMENT 'Exam weight as 80% of final grade: weighted_average * 0.8 (0-80)',

    -- Administrative
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (student_id) REFERENCES o_level_students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES o_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_subject (subject_id),
    INDEX idx_term (term_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_student_term (student_id, term_id),
    INDEX idx_subject_term (subject_id, term_id),
    UNIQUE KEY uk_student_subject_term (student_id, subject_id, term_id),
    CONSTRAINT chk_exam_weight_points CHECK (exam_weight_points IS NULL OR (exam_weight_points >= 0 AND exam_weight_points <= 80))
);

-- A-Level Paper Exam Weights (Stores calculated 80% exam weight for each student-paper-term)
CREATE TABLE IF NOT EXISTS a_level_paper_exam_weights (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_paper_id INT NOT NULL COMMENT 'References a_level_subject_papers table',
    academic_year_id INT NOT NULL,
    term_id INT NOT NULL,

    -- Exam Weight Calculation (80% of final grade per paper)
    exam_weight_points INT NULL COMMENT 'Exam weight as 80% of final grade: weighted_average * 0.8 (0-80)',

    -- Administrative
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (student_id) REFERENCES a_level_students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_paper_id) REFERENCES a_level_subject_papers(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_subject_paper (subject_paper_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_term (term_id),
    INDEX idx_student_term (student_id, term_id),
    INDEX idx_paper_term (subject_paper_id, term_id),
    UNIQUE KEY uk_student_subject_paper_term (student_id, subject_paper_id, term_id),
    CONSTRAINT chk_exam_weight_points CHECK (exam_weight_points IS NULL OR (exam_weight_points >= 0 AND exam_weight_points <= 80))
);

-- A-Level Subject Final Grades are calculated dynamically from paper weights
-- No storage table needed - grades computed on-demand using UACE rules from:
-- - a_level_paper_ca_weights (CA component)
-- - a_level_paper_exam_weights (Exam component)
-- - UACE grading rules in calculateUACEGrade() function

-- UACE Points are calculated dynamically from subject grades
-- No storage table needed - points computed on-demand from:
-- - a_level_student_subjects (subject assignments)
-- - Grade calculation functions (final grades)
-- - UACE point rules (A=6, B=5, C=4, D=3, E=2, O=1, F=0 for Principal; 1/0 for Subsidiary)



-- =============================================
-- 6. REPORTING 
-- =============================================





-- =============================================
-- 7. Other Tables
-- =============================================


-- Teacher-Class assignments (for Class Teachers - one class per teacher)
CREATE TABLE IF NOT EXISTS class_teacher_assignments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    teacher_id INT NOT NULL,
    class_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    INDEX idx_teacher (teacher_id),
    INDEX idx_class (class_id),
    UNIQUE KEY uk_teacher_class (teacher_id, class_id)
);

-- O-Level student subjects table
CREATE TABLE IF NOT EXISTS o_level_student_subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    class_level_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES o_level_students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES o_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_subject (subject_id),
    INDEX idx_class_level (class_level_id),
    UNIQUE KEY uk_student_subject_class_level (student_id, subject_id, class_level_id)
);

-- A-Level student subjects table
CREATE TABLE IF NOT EXISTS a_level_student_subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    class_level_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES a_level_students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES a_level_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (class_level_id) REFERENCES class_levels(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_subject (subject_id),
    INDEX idx_class_level (class_level_id),
    UNIQUE KEY uk_student_subject_class_level (student_id, subject_id, class_level_id)
);

-- =============================================
-- 8. ACADEMIC OPERATIONS TABLES
-- =============================================


-- =============================================
-- 10. GRADING SCALES FOR COMPETENCY-BASED ASSESSMENT
-- =============================================

-- O-Level Competency-Based Grading Scale
CREATE TABLE IF NOT EXISTS o_level_grading_scale (
    id INT PRIMARY KEY AUTO_INCREMENT,
    competency_level INT NOT NULL COMMENT 'Competency level (0, 1, 2, 3)',
    competency_description VARCHAR(255) NOT NULL COMMENT 'Description of competency level',
    min_score DECIMAL(3,2) NOT NULL COMMENT 'Minimum score for this level (0.00-3.00)',
    max_score DECIMAL(3,2) NOT NULL COMMENT 'Maximum score for this level (0.00-3.00)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_competency_level (competency_level),
    UNIQUE KEY uk_competency_level (competency_level),
    CONSTRAINT chk_score_range CHECK (min_score >= 0.00 AND max_score <= 3.00 AND min_score <= max_score)
);



-- =============================================
-- 12. STORED PROCEDURES AND TRIGGERS FOR AUTOMATIC CALCULATIONS
-- =============================================


-- IMPORTANT: ALL WEIGHT CALCULATIONS ARE HANDLED BY DATABASE TRIGGERS ONLY
--
-- CA Weight Calculation (20% of final grade):
-- - Triggers automatically calculate when CA scores are inserted/updated
-- - Formula: (Sum of CA Scores ÷ Sum of Max Scores) × 20
-- - Stored as integers (0-20) in weight tables
--
-- Exam Weight Calculation (80% of final grade):
-- - Triggers automatically calculate when exam marks are inserted/updated
-- - Formula: (Weighted Average of Exams) × 0.8
-- - Stored as integers (0-80) in weight tables
--
-- Final Grade Calculation:
-- - Simple sum: CA Weight + Exam Weight = Final Grade (0-100)
-- - No additional calculations needed in application layer




-- =============================================
-- 11. TRIGGERS FOR CA WEIGHT CALCULATIONS
-- =============================================

-- Trigger to calculate O-Level CA weights when CA scores are inserted
DELIMITER //
CREATE TRIGGER calculate_o_level_ca_weight_insert
AFTER INSERT ON o_level_subject_continuous_assessments_scores
FOR EACH ROW
BEGIN
    DECLARE total_ca_scores DECIMAL(10,2);
    DECLARE total_max_scores DECIMAL(10,2);
    DECLARE ca_count INT;
    DECLARE weight_points INT;

    -- Calculate total CA scores and count for this student-subject-term
    SELECT SUM(competency_score), COUNT(*) INTO total_ca_scores, ca_count
    FROM o_level_subject_continuous_assessments_scores
    WHERE student_id = NEW.student_id
      AND subject_id = NEW.subject_id
      AND term_id = NEW.term_id
      AND academic_year_id = NEW.academic_year_id;

    -- Calculate total max scores (each CA has max of 3.0)
    SET total_max_scores = ca_count * 3.0;

    -- Convert to 20-point scale: (total_ca_scores ÷ total_max_scores) × 20
    SET weight_points = CASE
        WHEN total_ca_scores IS NULL OR total_max_scores = 0 THEN 0
        ELSE ROUND((total_ca_scores / total_max_scores) * 20.0)
    END;

    -- Calculate average for storage (for reference only)
    DECLARE avg_score DECIMAL(2,1);
    SET avg_score = CASE
        WHEN ca_count = 0 THEN NULL
        ELSE total_ca_scores / ca_count
    END;

    -- Insert or update CA weight
    INSERT INTO o_level_subject_ca_weights
    (student_id, subject_id, academic_year_id, term_id, ca_average, ca_weight_points)
    VALUES (NEW.student_id, NEW.subject_id, NEW.academic_year_id, NEW.term_id, avg_score, weight_points)
    ON DUPLICATE KEY UPDATE
        ca_average = avg_score,
        ca_weight_points = weight_points;
END//
DELIMITER ;

-- Trigger to calculate O-Level CA weights when CA scores are updated
DELIMITER //
CREATE TRIGGER calculate_o_level_ca_weight_update
AFTER UPDATE ON o_level_subject_continuous_assessments_scores
FOR EACH ROW
BEGIN
    DECLARE total_ca_scores DECIMAL(10,2);
    DECLARE total_max_scores DECIMAL(10,2);
    DECLARE ca_count INT;
    DECLARE weight_points INT;

    -- Calculate total CA scores and count for this student-subject-term
    SELECT SUM(competency_score), COUNT(*) INTO total_ca_scores, ca_count
    FROM o_level_subject_continuous_assessments_scores
    WHERE student_id = NEW.student_id
      AND subject_id = NEW.subject_id
      AND term_id = NEW.term_id
      AND academic_year_id = NEW.academic_year_id;

    -- Calculate total max scores (each CA has max of 3.0)
    SET total_max_scores = ca_count * 3.0;

    -- Convert to 20-point scale: (total_ca_scores ÷ total_max_scores) × 20
    SET weight_points = CASE
        WHEN total_ca_scores IS NULL OR total_max_scores = 0 THEN 0
        ELSE ROUND((total_ca_scores / total_max_scores) * 20.0)
    END;

    -- Calculate average for storage (for reference only)
    DECLARE avg_score DECIMAL(2,1);
    SET avg_score = CASE
        WHEN ca_count = 0 THEN NULL
        ELSE total_ca_scores / ca_count
    END;

    -- Update CA weight
    INSERT INTO o_level_subject_ca_weights
    (student_id, subject_id, academic_year_id, term_id, ca_average, ca_weight_points)
    VALUES (NEW.student_id, NEW.subject_id, NEW.academic_year_id, NEW.term_id, avg_score, weight_points)
    ON DUPLICATE KEY UPDATE
        ca_average = avg_score,
        ca_weight_points = weight_points;
END//
DELIMITER ;

-- Trigger to calculate A-Level CA weights when CA scores are inserted
DELIMITER //
CREATE TRIGGER calculate_a_level_ca_weight_insert
AFTER INSERT ON a_level_paper_continuous_assessments_scores
FOR EACH ROW
BEGIN
    DECLARE total_ca_scores DECIMAL(10,2);
    DECLARE total_max_scores DECIMAL(10,2);
    DECLARE ca_count INT;
    DECLARE weight_points INT;

    -- Calculate total CA scores and count for this student-paper-term
    SELECT SUM(competency_score), COUNT(*) INTO total_ca_scores, ca_count
    FROM a_level_paper_continuous_assessments_scores
    WHERE student_id = NEW.student_id
      AND subject_paper_id = NEW.subject_paper_id
      AND term_id = NEW.term_id
      AND academic_year_id = NEW.academic_year_id;

    -- Calculate total max scores (each CA has max of 3.0)
    SET total_max_scores = ca_count * 3.0;

    -- Convert to 20-point scale: (total_ca_scores ÷ total_max_scores) × 20
    SET weight_points = CASE
        WHEN total_ca_scores IS NULL OR total_max_scores = 0 THEN 0
        ELSE ROUND((total_ca_scores / total_max_scores) * 20.0)
    END;

    -- Calculate average for storage (for reference only)
    DECLARE avg_score DECIMAL(2,1);
    SET avg_score = CASE
        WHEN ca_count = 0 THEN NULL
        ELSE total_ca_scores / ca_count
    END;

    -- Insert or update CA weight
    INSERT INTO a_level_paper_ca_weights
    (student_id, subject_paper_id, academic_year_id, term_id, ca_average, ca_weight_points)
    VALUES (NEW.student_id, NEW.subject_paper_id, NEW.academic_year_id, NEW.term_id, avg_score, weight_points)
    ON DUPLICATE KEY UPDATE
        ca_average = avg_score,
        ca_weight_points = weight_points;
END//
DELIMITER ;

-- Trigger to calculate A-Level CA weights when CA scores are updated
DELIMITER //
CREATE TRIGGER calculate_a_level_ca_weight_update
AFTER UPDATE ON a_level_paper_continuous_assessments_scores
FOR EACH ROW
BEGIN
    DECLARE total_ca_scores DECIMAL(10,2);
    DECLARE total_max_scores DECIMAL(10,2);
    DECLARE ca_count INT;
    DECLARE weight_points INT;

    -- Calculate total CA scores and count for this student-paper-term
    SELECT SUM(competency_score), COUNT(*) INTO total_ca_scores, ca_count
    FROM a_level_paper_continuous_assessments_scores
    WHERE student_id = NEW.student_id
      AND subject_paper_id = NEW.subject_paper_id
      AND term_id = NEW.term_id
      AND academic_year_id = NEW.academic_year_id;

    -- Calculate total max scores (each CA has max of 3.0)
    SET total_max_scores = ca_count * 3.0;

    -- Convert to 20-point scale: (total_ca_scores ÷ total_max_scores) × 20
    SET weight_points = CASE
        WHEN total_ca_scores IS NULL OR total_max_scores = 0 THEN 0
        ELSE ROUND((total_ca_scores / total_max_scores) * 20.0)
    END;

    -- Calculate average for storage (for reference only)
    DECLARE avg_score DECIMAL(2,1);
    SET avg_score = CASE
        WHEN ca_count = 0 THEN NULL
        ELSE total_ca_scores / ca_count
    END;

    -- Update CA weight
    INSERT INTO a_level_paper_ca_weights
    (student_id, subject_paper_id, academic_year_id, term_id, ca_average, ca_weight_points)
    VALUES (NEW.student_id, NEW.subject_paper_id, NEW.academic_year_id, NEW.term_id, avg_score, weight_points)
    ON DUPLICATE KEY UPDATE
        ca_average = avg_score,
        ca_weight_points = weight_points;
END//
DELIMITER ;

-- =============================================
-- 12. TRIGGERS FOR EXAM WEIGHT CALCULATIONS
-- =============================================

-- Trigger to calculate O-Level exam weights when exam marks are inserted
DELIMITER //
CREATE TRIGGER calculate_o_level_exam_weight_insert
AFTER INSERT ON o_level_student_exam_marks
FOR EACH ROW
BEGIN
    DECLARE avg_score DECIMAL(5,2);
    DECLARE weight_points INT;
    DECLARE subject_id_var INT;
    DECLARE academic_year_id_var INT;
    DECLARE term_id_var INT;

    -- Get subject_id, academic_year_id, and term_id from the examination
    SELECT te.subject_id, te.academic_year_id, te.term_id
    INTO subject_id_var, academic_year_id_var, term_id_var
    FROM o_level_term_examinations te
    WHERE te.id = NEW.examination_id;

    -- Calculate weighted average exam score for this student-subject-term
    SELECT
        SUM(em.marks_obtained * (cet.weight_percentage / 100.0)) / SUM(cet.weight_percentage / 100.0) INTO avg_score
    FROM o_level_student_exam_marks em
    JOIN o_level_term_examinations te ON em.examination_id = te.id
    JOIN class_exam_types cet ON te.exam_type_id = cet.exam_type_id
      AND te.class_id = cet.class_id AND te.term_id = cet.term_id AND te.academic_year_id = cet.academic_year_id
    WHERE em.student_id = NEW.student_id
      AND te.subject_id = subject_id_var
      AND te.term_id = term_id_var
      AND te.academic_year_id = academic_year_id_var
      AND em.marks_obtained IS NOT NULL;

    -- Convert to 80-point scale: average * 0.8
    SET weight_points = CASE
        WHEN avg_score IS NULL THEN 0
        ELSE ROUND(avg_score * 0.8)
    END;

    -- Insert or update exam weight
    INSERT INTO o_level_subject_exam_weights
    (student_id, subject_id, academic_year_id, term_id, exam_weight_points)
    VALUES (NEW.student_id, subject_id_var, academic_year_id_var, term_id_var, weight_points)
    ON DUPLICATE KEY UPDATE
        exam_weight_points = weight_points;
END//
DELIMITER ;

-- Trigger to calculate O-Level exam weights when exam marks are updated
DELIMITER //
CREATE TRIGGER calculate_o_level_exam_weight_update
AFTER UPDATE ON o_level_student_exam_marks
FOR EACH ROW
BEGIN
    DECLARE avg_score DECIMAL(5,2);
    DECLARE weight_points INT;
    DECLARE subject_id_var INT;
    DECLARE academic_year_id_var INT;
    DECLARE term_id_var INT;

    -- Get subject_id, academic_year_id, and term_id from the examination
    SELECT te.subject_id, te.academic_year_id, te.term_id
    INTO subject_id_var, academic_year_id_var, term_id_var
    FROM o_level_term_examinations te
    WHERE te.id = NEW.examination_id;

    -- Calculate weighted average exam score for this student-subject-term
    SELECT
        SUM(em.marks_obtained * (cet.weight_percentage / 100.0)) / SUM(cet.weight_percentage / 100.0) INTO avg_score
    FROM o_level_student_exam_marks em
    JOIN o_level_term_examinations te ON em.examination_id = te.id
    JOIN class_exam_types cet ON te.exam_type_id = cet.exam_type_id
      AND te.class_id = cet.class_id AND te.term_id = cet.term_id AND te.academic_year_id = cet.academic_year_id
    WHERE em.student_id = NEW.student_id
      AND te.subject_id = subject_id_var
      AND te.term_id = term_id_var
      AND te.academic_year_id = academic_year_id_var
      AND em.marks_obtained IS NOT NULL;

    -- Convert to 80-point scale: average * 0.8
    SET weight_points = CASE
        WHEN avg_score IS NULL THEN 0
        ELSE ROUND(avg_score * 0.8)
    END;

    -- Update exam weight
    INSERT INTO o_level_subject_exam_weights
    (student_id, subject_id, academic_year_id, term_id, exam_weight_points)
    VALUES (NEW.student_id, subject_id_var, academic_year_id_var, term_id_var, weight_points)
    ON DUPLICATE KEY UPDATE
        exam_weight_points = weight_points;
END//
DELIMITER ;

-- Trigger to calculate A-Level exam weights when exam marks are inserted
DELIMITER //
CREATE TRIGGER calculate_a_level_exam_weight_insert
AFTER INSERT ON a_level_student_paper_exam_marks
FOR EACH ROW
BEGIN
    DECLARE avg_score DECIMAL(5,2);
    DECLARE weight_points INT;
    DECLARE subject_paper_id_var INT;
    DECLARE academic_year_id_var INT;
    DECLARE term_id_var INT;

    -- Get subject_paper_id, academic_year_id, and term_id from the examination
    SELECT pe.subject_paper_id, pe.academic_year_id, pe.term_id
    INTO subject_paper_id_var, academic_year_id_var, term_id_var
    FROM a_level_paper_examinations pe
    WHERE pe.id = NEW.examination_id;

    -- Calculate weighted average exam score for this student-paper-term
    SELECT
        SUM(em.marks_obtained * (cet.weight_percentage / 100.0)) / SUM(cet.weight_percentage / 100.0) INTO avg_score
    FROM a_level_student_paper_exam_marks em
    JOIN a_level_paper_examinations pe ON em.examination_id = pe.id
    JOIN class_exam_types cet ON pe.exam_type_id = cet.exam_type_id
      AND pe.class_id = cet.class_id AND pe.term_id = cet.term_id AND pe.academic_year_id = cet.academic_year_id
    WHERE em.student_id = NEW.student_id
      AND pe.subject_paper_id = subject_paper_id_var
      AND pe.term_id = term_id_var
      AND pe.academic_year_id = academic_year_id_var
      AND em.marks_obtained IS NOT NULL;

    -- Convert to 80-point scale: average * 0.8
    SET weight_points = CASE
        WHEN avg_score IS NULL THEN 0
        ELSE ROUND(avg_score * 0.8)
    END;

    -- Insert or update exam weight
    INSERT INTO a_level_paper_exam_weights
    (student_id, subject_paper_id, academic_year_id, term_id, exam_weight_points)
    VALUES (NEW.student_id, subject_paper_id_var, academic_year_id_var, term_id_var, weight_points)
    ON DUPLICATE KEY UPDATE
        exam_weight_points = weight_points;
END//
DELIMITER ;

-- Trigger to calculate A-Level exam weights when exam marks are updated
DELIMITER //
CREATE TRIGGER calculate_a_level_exam_weight_update
AFTER UPDATE ON a_level_student_paper_exam_marks
FOR EACH ROW
BEGIN
    DECLARE avg_score DECIMAL(5,2);
    DECLARE weight_points INT;
    DECLARE subject_paper_id_var INT;
    DECLARE academic_year_id_var INT;
    DECLARE term_id_var INT;

    -- Get subject_paper_id, academic_year_id, and term_id from the examination
    SELECT pe.subject_paper_id, pe.academic_year_id, pe.term_id
    INTO subject_paper_id_var, academic_year_id_var, term_id_var
    FROM a_level_paper_examinations pe
    WHERE pe.id = NEW.examination_id;

    -- Calculate weighted average exam score for this student-paper-term
    SELECT
        SUM(em.marks_obtained * (cet.weight_percentage / 100.0)) / SUM(cet.weight_percentage / 100.0) INTO avg_score
    FROM a_level_student_paper_exam_marks em
    JOIN a_level_paper_examinations pe ON em.examination_id = pe.id
    JOIN class_exam_types cet ON pe.exam_type_id = cet.exam_type_id
      AND pe.class_id = cet.class_id AND pe.term_id = cet.term_id AND pe.academic_year_id = cet.academic_year_id
    WHERE em.student_id = NEW.student_id
      AND pe.subject_paper_id = subject_paper_id_var
      AND pe.term_id = term_id_var
      AND pe.academic_year_id = academic_year_id_var
      AND em.marks_obtained IS NOT NULL;

    -- Convert to 80-point scale: average * 0.8
    SET weight_points = CASE
        WHEN avg_score IS NULL THEN 0
        ELSE ROUND(avg_score * 0.8)
    END;

    -- Update exam weight
    INSERT INTO a_level_paper_exam_weights
    (student_id, subject_paper_id, academic_year_id, term_id, exam_weight_points)
    VALUES (NEW.student_id, subject_paper_id_var, academic_year_id_var, term_id_var, weight_points)
    ON DUPLICATE KEY UPDATE
        exam_weight_points = weight_points;
END//
DELIMITER ;

-- Note: Class Teacher assignment validation will be handled by the application layer
-- to avoid complex database constraints that can cause initialization issues

-- =============================================
-- 12. INITIAL DATA POPULATION
-- =============================================

-- Default admin user will be created by init.js during database initialization

-- Insert default education levels data
INSERT IGNORE INTO education_levels
(code, name, display_name, sort_order, is_active)
VALUES
('o_level', 'O-Level', 'Ordinary Level', 1, TRUE),
('a_level', 'A-Level', 'Advanced Level', 2, TRUE);

-- Insert default class levels data
INSERT IGNORE INTO class_levels
(code, name, display_name, education_level_id, sort_order, streams_optional, is_active)
VALUES
('s1', 'S.1', 'Senior One', (SELECT id FROM education_levels WHERE code = 'o_level'), 1, TRUE, TRUE),
('s2', 'S.2', 'Senior Two', (SELECT id FROM education_levels WHERE code = 'o_level'), 2, TRUE, TRUE),
('s3', 'S.3', 'Senior Three', (SELECT id FROM education_levels WHERE code = 'o_level'), 3, TRUE, TRUE),
('s4', 'S.4', 'Senior Four', (SELECT id FROM education_levels WHERE code = 'o_level'), 4, TRUE, TRUE),
('s5', 'S.5', 'Senior Five', (SELECT id FROM education_levels WHERE code = 'a_level'), 5, FALSE, TRUE),
('s6', 'S.6', 'Senior Six', (SELECT id FROM education_levels WHERE code = 'a_level'), 6, FALSE, TRUE);

-- Insert default A-Level streams (Sciences and Arts) - these are pre-configured and cannot be modified
INSERT IGNORE INTO streams (name, stream_type) VALUES
('SCIENCES', 'a_level'),
('ARTS', 'a_level');

-- Link A-Level streams to S.5 and S.6 classes (permanent assignments)
-- A-Level streams (Sciences/Arts) are always available and cannot be changed
INSERT IGNORE INTO stream_classes (stream_id, class_level_id)
SELECT s.id, cl.id
FROM streams s
CROSS JOIN class_levels cl
JOIN education_levels el ON cl.education_level_id = el.id
WHERE s.stream_type = 'a_level'
AND el.code = 'a_level';

-- Create default base classes (S.1 to S.6 only)
INSERT IGNORE INTO classes (name, class_level_id)
SELECT
    cl.name,
    cl.id
FROM class_levels cl
JOIN education_levels el ON cl.education_level_id = el.id
WHERE cl.code IN ('s1', 's2', 's3', 's4', 's5', 's6');


-- Insert O-Level Competency-Based Grading Scale
INSERT IGNORE INTO o_level_grade_boundaries
(grade_letter, min_percentage, max_percentage, grade_descriptor)
VALUES
('A', 80, 100, 'Exceptional'),
('B', 60, 79, 'Outstanding'),
('C', 50, 59, 'Satisfactory'),
('D', 40, 49, 'Basic'),
('E', 0, 39, 'Elementary');



-- Insert A-Level Paper Grade Boundaries (UACE Grading Scale)
INSERT IGNORE INTO a_level_paper_grade_boundaries
(grade_code, min_percentage, max_percentage, grade_descriptor)
VALUES
('D1', 80, 100, 'Distinction 1'),
('D2', 75, 79, 'Distinction 2'),
('C3', 66, 74, 'Credit 3'),
('C4', 60, 65, 'Credit 4'),
('C5', 55, 59, 'Credit 5'),
('C6', 50, 54, 'Credit 6'),
('P7', 45, 49, 'Pass 7'),
('P8', 35, 44, 'Pass 8'),
('F9', 0, 34, 'Fail 9');


-- =============================================
-- UGANDA O-LEVEL CURRICULUM FRAMEWORK - OFFICIAL STRUCTURE
-- =============================================

-- S1-S2 COMPULSORY SUBJECTS (10 subjects) + subjects that change status
INSERT IGNORE INTO o_level_subjects
(name, short_name, subject_type, uneb_code)
VALUES
('English', 'ENG', 'Compulsory', '112'), 
('History & Political Education', 'HPE', 'Compulsory', '241'), 
('Geography', 'GEOG', 'Compulsory', '273'), 
('Mathematics', 'MATH', 'Compulsory', '456'), 
('Physics', 'PHY', 'Compulsory', '535'), 
('Chemistry', 'CHEM', 'Compulsory', '545'), 
('Biology', 'BIO', 'Compulsory', '553'), 
-- Subjects that are compulsory in S1-S2 but become elective in S3-S4
('Kiswahili', 'KIS', 'Language', '336'),
('Physical Education', 'PE', 'Practical (pre-vocational)', '555'), 
('Entrepreneurship', 'ENT', 'Practical (pre-vocational)', '845');

-- RELIGIOUS EDUCATION ELECTIVES
INSERT IGNORE INTO o_level_subjects
(name, short_name, subject_type, uneb_code)
VALUES
('Christian Religious Education', 'CRE', 'Religious Education', '223'),
('Islamic Religious Education', 'IRE', 'Religious Education', '225');

-- PRACTICAL (PRE-VOCATIONAL) ELECTIVES (S1-S2: choose 1, S3-S4: elective)
INSERT IGNORE INTO o_level_subjects
(name, short_name, subject_type, uneb_code)
VALUES
('Agriculture', 'AGRIC', 'Practical (pre-vocational)', '527'),
('Information & Communications Technology', 'ICT', 'Practical (pre-vocational)', '840'),
('Technology and Design', 'TD', 'Practical (pre-vocational)', '745'),
('Nutrition & Food Technology', 'NFT', 'Practical (pre-vocational)', '662'), 
('Art and Design', 'ART', 'Practical (pre-vocational)', '612'),
('Performing Arts', 'PA', 'Practical (pre-vocational)', '621');

-- LANGUAGE ELECTIVES (S1-S2: choose 1, S3-S4: elective)
INSERT IGNORE INTO o_level_subjects
(name, short_name, subject_type, uneb_code)
VALUES
('French', 'FRE', 'Language', '314'),
('German', 'GER', 'Language', '309'),
('Latin', 'LAT', 'Language', '301'),
('Arabic', 'ARAB', 'Language', '337'),
('Chinese', 'CHI', 'Language', '396'),
('Literature in English', 'LIT', 'Language', '208'),
('Luganda', 'LUG', 'Language', '335'),
('Lusoga', 'LUS', 'Language', '355'),
('Runyankore/ Rukiga', 'RR', 'Language', '345'),
('Runyoro/ Rutooro', 'RRT', 'Language', '385'),
('Leb Acoli', 'ACO', 'Language', '305'),
('Leb Lango', 'LAN', 'Language', '315'),
('Ateso', 'ATES', 'Language', '365'),
('LugbaraTi', 'LUGB', 'Language', '325'),
('Dhopadhola', 'DHO', 'Language', '375'),
('Lumasaaba', 'LUM', 'Language', '395'),
('Ugandan Sign Language', 'USL', 'Language', '397');

-- Insert O-Level Subject-Class relationships
-- Compulsory subjects for all classes (S1-S4)
INSERT IGNORE INTO o_level_subject_classes (subject_id, class_level_id, subject_status)
SELECT s.id, cl.id, 'compulsory'
FROM o_level_subjects s
CROSS JOIN class_levels cl
JOIN education_levels el ON cl.education_level_id = el.id
WHERE s.short_name IN ('ENG', 'MATH', 'HPE', 'GEOG', 'PHY', 'BIO', 'CHEM')
AND el.code = 'o_level';

-- Subjects that are compulsory in S1-S2 but elective in S3-S4
INSERT IGNORE INTO o_level_subject_classes (subject_id, class_level_id, subject_status)
SELECT s.id, cl.id, 'compulsory'
FROM o_level_subjects s
CROSS JOIN class_levels cl
JOIN education_levels el ON cl.education_level_id = el.id
WHERE s.short_name IN ('PE', 'ENT', 'KIS')
AND el.code = 'o_level'
AND cl.code IN ('s1', 's2');

INSERT IGNORE INTO o_level_subject_classes (subject_id, class_level_id, subject_status)
SELECT s.id, cl.id, 'elective'
FROM o_level_subjects s
CROSS JOIN class_levels cl
JOIN education_levels el ON cl.education_level_id = el.id
WHERE s.short_name IN ('PE', 'ENT', 'KIS')
AND el.code = 'o_level'
AND cl.code IN ('s3', 's4');

-- Religious Education subjects (elective for all O-Level)
INSERT IGNORE INTO o_level_subject_classes (subject_id, class_level_id, subject_status)
SELECT s.id, cl.id, 'elective'
FROM o_level_subjects s
CROSS JOIN class_levels cl
JOIN education_levels el ON cl.education_level_id = el.id
WHERE s.subject_type = 'Religious Education'
AND el.code = 'o_level';

-- Practical and Language subjects (elective for all O-Level)
INSERT IGNORE INTO o_level_subject_classes (subject_id, class_level_id, subject_status)
SELECT s.id, cl.id, 'elective'
FROM o_level_subjects s
CROSS JOIN class_levels cl
JOIN education_levels el ON cl.education_level_id = el.id
WHERE s.subject_type IN ('Practical (pre-vocational)', 'Language')
AND el.code = 'o_level';


-- COMPULSORY SUBJECT (All students must take)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code)
VALUES
('General Paper', 'GP', 'Subsidiary', 'S101');

-- SUBSIDIARY SUBJECTS (Choose 1: SMATH or SICT)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code)
VALUES
('Subsidiary Mathematics', 'SMATH', 'Subsidiary', 'S475'),
('Subsidiary ICT', 'SICT', 'Subsidiary', 'S850');

-- SCIENCE SUBJECTS (Principal)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code)
VALUES
('Principal Mathematics', 'MATH', 'Principal', 'P425'),
('Physics', 'PHY', 'Principal', 'P510'),
('Agriculture', 'AGRI', 'Principal', 'P515'),
('Chemistry', 'CHEM', 'Principal', 'P525'),
('Biology', 'BIO', 'Principal', 'P530');

-- HUMANITIES SUBJECTS (Principal)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code)
VALUES
('History', 'HIST', 'Principal', 'P210'),
('Geography', 'GEOG', 'Principal', 'P250');

-- RELIGIOUS EDUCATION SUBJECTS (Principal)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code)
VALUES
('Islamic Religious Education', 'IRE', 'Principal', 'P235'),
('Christian Religious Education', 'CRE', 'Principal', 'P245');

-- BUSINESS SUBJECTS (Principal)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code)
VALUES
('Economics', 'ECON', 'Principal', 'P220'),
('Entrepreneurship', 'ENT', 'Principal', 'P230');

-- LOCAL LANGUAGE SUBJECTS (3 papers each)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code)
VALUES
('Luganda', 'LUG', 'Principal', 'P360'),
('Leb Acoli', 'ACO', 'Principal', 'P361'),
('Leb Lango', 'LAN', 'Principal', 'P362'),
('LugbaraTi', 'LUGB', 'Principal', 'P363'),
('Runyankore/ Rukiga', 'RR', 'Principal', 'P364'),
('Lusoga', 'LUS', 'Principal', 'P366'),
('Ateso', 'ATES', 'Principal', 'P367'),
('Dhopadhola', 'DHO', 'Principal', 'P368'),
('Runyoro/ Rutooro', 'RRT', 'Principal', 'P369'),
('Lumasaaba', 'LUM', 'Principal', 'P371');

-- INTERNATIONAL LANGUAGE SUBJECTS
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code)
VALUES
('Literature in English', 'LIT', 'Principal', 'P310'),
('Fasihi ya Kiswahili', 'KIS', 'Principal', 'P320'),
('French', 'FRE', 'Principal', 'P330'),
('German', 'GER', 'Principal', 'P340'),
('Latin', 'LAT', 'Principal', 'P350'),
('Arabic', 'ARA', 'Principal', 'P370'),
('Chinese', 'CHI', 'Principal', 'P372');

-- CREATIVE ARTS SUBJECTS (Practical)
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code)
VALUES
('Art & Design', 'ART', 'Principal', 'P615'),
('Music', 'MUS', 'Principal', 'P620');

-- TECHNICAL/PRACTICAL SUBJECTS
INSERT IGNORE INTO a_level_subjects
(name, short_name, subject_type, uneb_code)
VALUES
('Foods and Nutrition', 'FN', 'Principal', 'P640'),
('Technical Drawing', 'TD', 'Principal', 'P720'),
('Woodwork', 'WW', 'Principal', 'P730'),
('Metal Work', 'MW', 'Principal', 'P740'),
('Clothing and Textiles', 'CT', 'Principal', 'P630');

-- Note: A-Level combinations are determined by the 3 Principal + 2 Subsidiary subjects each student takes
-- Stream selection (Arts/Sciences) for S.5 and S.6 is made during student registration

-- =============================================
-- POPULATE A-LEVEL SUBJECT PAPERS
-- =============================================

-- Populate a_level_subject_papers based on subject requirements
-- This creates individual paper records for each subject

-- 1-paper subjects (Subsidiary subjects)
INSERT IGNORE INTO a_level_subject_papers (subject_id, paper_number, paper_name)
SELECT s.id, 1, 'Paper 1'
FROM a_level_subjects s
WHERE s.short_name IN ('GP', 'SMATH');

-- 2-paper subjects
INSERT IGNORE INTO a_level_subject_papers (subject_id, paper_number, paper_name)
SELECT s.id, p.paper_num, CONCAT('Paper ', p.paper_num)
FROM a_level_subjects s
CROSS JOIN (SELECT 1 as paper_num UNION ALL SELECT 2) p
WHERE s.short_name IN ('SICT', 'MATH', 'HIST', 'ECON', 'LIT', 'KIS', 'FRE', 'GER', 'LAT', 'ARA', 'CHI', 'MUS', 'FN', 'TD', 'WW', 'MW', 'CT');

-- 3-paper subjects
INSERT IGNORE INTO a_level_subject_papers (subject_id, paper_number, paper_name)
SELECT s.id, p.paper_num, CONCAT('Paper ', p.paper_num)
FROM a_level_subjects s
CROSS JOIN (SELECT 1 as paper_num UNION ALL SELECT 2 UNION ALL SELECT 3) p
WHERE s.short_name IN ('PHY', 'AGRI', 'CHEM', 'BIO', 'GEOG', 'IRE', 'CRE', 'ENT', 'LUG', 'ACO', 'LAN', 'LUGB', 'RR', 'LUS', 'ATES', 'DHO', 'RRT', 'LUM');

-- 4-paper subjects
INSERT IGNORE INTO a_level_subject_papers (subject_id, paper_number, paper_name)
SELECT s.id, p.paper_num, CONCAT('Paper ', p.paper_num)
FROM a_level_subjects s
CROSS JOIN (SELECT 1 as paper_num UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4) p
WHERE s.short_name IN ('ART');

-- =============================================
-- CA CONFIGURATION NOTES
-- =============================================

-- CA Configuration Usage:
-- 1. Use the ca_configuration table to set how many CAs each subject should have per term
-- 2. Configure through admin interface using /api/ca-configuration endpoints
-- 3. Different subjects can have different CA counts (1-10 CAs per term)
-- 4. Teachers can only enter CAs up to the configured limit
-- 5. System validates CA entries against these configurations

-- Example: Set 3 CAs for all O-Level Mathematics in Term 1, 2025:
-- INSERT INTO ca_configuration (subject_id, subject_level, academic_year_id, term_id, total_cas, configured_by_id)
-- VALUES (1, 'o_level', 1, 1, 3, 1);

-- =============================================
-- EXAM TYPES INITIALIZATION
-- =============================================

-- Insert default exam types for the two-tier assessment system
-- Note: Weights are configured per class and represent the exam's share within the Summative Assessment (80%)
INSERT IGNORE INTO exam_types (name, short_name, sort_order) VALUES
('Beginning of Term', 'BOT', 1),
('Mid Term', 'MID', 2),
('End of Term', 'EOT', 3),
('Mock Exam', 'MOCK', 4),
('Remedial Exam', 'REM', 5);

-- Note: No default class-exam type associations are inserted
-- The system follows a two-tier assessment structure:
-- 1. Continuous Assessment (CA) - Always 20% of final grade
-- 2. Summative Assessment (All Exams Combined) - Always 80% of final grade
-- Administrators configure exam type weights per class, where weights represent
-- the exam's share within the Summative Assessment portion (must total 100%)
-- Final calculation: Final Grade = CA (20%) + (Exam1×weight1 + Exam2×weight2 + ...) × 0.8

-- O-Level Competency Grading Scale
CREATE TABLE IF NOT EXISTS o_level_grading_scale (
    id INT PRIMARY KEY AUTO_INCREMENT,
    competency_level INT NOT NULL COMMENT 'Competency level (0-3)',
    competency_description TEXT NOT NULL COMMENT 'Description of competency level',
    min_score DECIMAL(2,2) NOT NULL COMMENT 'Minimum score for this level',
    max_score DECIMAL(2,2) NOT NULL COMMENT 'Maximum score for this level',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_competency_level (competency_level),
    INDEX idx_score_range (min_score, max_score)
);

-- Insert O-Level competency grading scale
INSERT IGNORE INTO o_level_grading_scale
(competency_level, competency_description, min_score, max_score)
VALUES
(0, 'Absent: No Learning Outcomes achieved. Learner was absent', 0.0, 0.0),
(1, 'Basic: Few learning outcomes achieved, not sufficient for overall achievement', 0.9, 1.49),
(2, 'Moderate: Many learning outcomes achieved, enough for overall achievement', 1.5, 2.49),
(3, 'Outstanding: Most or all learning outcomes achieved', 2.5, 3.0);

-- Validation function for two-tier assessment system
DELIMITER //
CREATE FUNCTION validate_exam_weights_total(
    p_class_id INT,
    p_academic_year_id INT,
    p_term_id INT
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE total_weight INT DEFAULT 0;

    SELECT COALESCE(SUM(weight_percentage), 0) INTO total_weight
    FROM class_exam_types
    WHERE class_id = p_class_id
      AND academic_year_id = p_academic_year_id
      AND term_id = p_term_id;

    RETURN total_weight = 100;
END //
DELIMITER ;

-- Default Configuration Notes:
-- - MT (30%) + EOT (70%) = 100% of the exam component (recommended)
-- - BOT, MOCK, and REM start with 0% weight (can be configured by admin)
-- - System admins have full control to adjust weights as per curriculum requirements
-- - Final grade calculation: CA (20%) + Weighted Exams (80%) = Final Grade
-- - Weights can be modified anytime through the admin interface

-- =============================================
-- 15. BACKUP HISTORY TABLE
-- =============================================

-- Backup History table for tracking database backups
CREATE TABLE IF NOT EXISTS backup_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL COMMENT 'Backup file name',
    file_path VARCHAR(500) NOT NULL COMMENT 'Full path to backup file',
    file_size BIGINT NOT NULL DEFAULT 0 COMMENT 'File size in bytes',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_created_at (created_at),
    INDEX idx_filename (filename)
);

-- End of SmartReport Database Schema