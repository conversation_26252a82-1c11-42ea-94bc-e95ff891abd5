// SmartReport - Academic Year Management Component
// Manages current academic year and term dates
// Allows updating term dates for the active academic year

// Uses global API services: window.AcademicYearsAPI, window.TermsAPI, window.AcademicAPI
// Uses global config: window.SRConfig
// Uses environment configuration: ../config/environment.js

const AcademicYearManagementComponents = {
  // Component state
  state: {
    currentAcademicYear: null,
    terms: [],
    loading: false,
    editingTerm: null
  },

  // Initialize component
  async init() {
    await this.loadInitialData();
  },

  // Load initial data
  async loadInitialData() {
    try {
      this.state.loading = true;
      console.log('🔄 Loading academic year management data...');

      // Get current academic context
      const contextResponse = await window.AcademicAPI.getCurrentContext();
      console.log('📅 Academic context response:', contextResponse);

      if (contextResponse && contextResponse.success) {
        this.state.currentAcademicYear = contextResponse.data.academicYear;
        console.log('✅ Current academic year loaded:', this.state.currentAcademicYear);
      } else {
        console.warn('⚠️ No active academic year found');
      }

      // Get all terms for current academic year
      if (this.state.currentAcademicYear) {
        const termsResponse = await window.TermsAPI.getAll({
          academic_year_id: this.state.currentAcademicYear.id
        });
        console.log('📋 Terms response:', termsResponse);

        if (termsResponse && termsResponse.success) {
          this.state.terms = termsResponse.data || [];
          console.log('✅ Terms loaded:', this.state.terms.length, 'terms');
        } else {
          console.warn('⚠️ Failed to load terms');
        }
      }

      console.log('✅ Academic year management data loaded successfully');

    } catch (error) {
      console.error('❌ Error loading academic year management data:', error);
      this.showError('Failed to load academic year data');
    } finally {
      this.state.loading = false;
    }
  },

  // Show success message
  showSuccess(message) {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, 'success');
    }
  },

  // Show error message
  showError(message) {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, 'error');
    }
  },

  // Show info message
  showInfo(message) {
    if (window.SRDesignSystem && window.SRDesignSystem.notifications) {
      window.SRDesignSystem.notifications.show(message, 'info');
    }
  }
};

// Academic Year Management Component
const AcademicYearManagementComponent = {
  // Render academic year management interface
  render() {
    // Show loading state if data is still loading
    if (AcademicYearManagementComponents.state.loading) {
      // Use centralized loading state from page router
      const container = document.getElementById('content-area');
      if (container && window.PageRouter) {
        window.PageRouter.showLoadingState(container, 'Loading academic year data...');
      }
      return '';
    }

    return `
      <div class="space-y-6">
        ${SRDesignSystem.layouts.pageHeader(
          'Academic Year Management',
          'Manage current academic year and update term dates'
        )}

        <!-- Current Academic Year Info -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
          <div class="flex items-center justify-between mb-6">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">Current Academic Year</h3>
            <div id="edit-year-button-container">
              <!-- Edit button will be populated here -->
            </div>
          </div>

          <div id="current-year-info">
            <!-- Current year info will be populated here -->
          </div>
        </div>

        <!-- Terms Management -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 ${SRDesignSystem.responsive.spacing.padding}">
          <div class="flex items-center justify-between mb-6">
            <h3 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">Term Dates Management</h3>
          </div>

          <div id="terms-management-content">
            <!-- Terms content will be populated here -->
          </div>
        </div>
      </div>
    `;
  },

  // Initialize academic year management component
  async init() {
    console.log('🔧 Initializing Academic Year Management Component...');

    // Wait for academic context to be loaded
    await window.AcademicContext.initialize();

    // Load initial data first
    await AcademicYearManagementComponents.loadInitialData();

    console.log('🔄 Populating Academic Year Management UI...');
    this.populateCurrentYearInfo();
    this.populateTermsManagement();
    this.initializeEventListeners();
    console.log('✅ Academic Year Management Component initialized successfully');
  },

  // Reset component state
  resetComponentState() {
    // Reset any component-specific state if needed
  },

  // Cleanup component
  cleanup() {
    this.resetComponentState();

    // Hide any open modals
    const modals = ['edit-year-modal', 'edit-term-modal', 'create-term-modal'];
    modals.forEach(modalId => {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.classList.add('hidden');
      }
    });
  },



  // Populate current academic year information
  populateCurrentYearInfo() {
    console.log('🔄 Populating current year info...');
    const container = document.getElementById('current-year-info');
    if (!container) {
      console.error('❌ Current year info container not found');
      return;
    }

    const currentYear = AcademicYearManagementComponents.state.currentAcademicYear;
    console.log('📅 Current year data:', currentYear);

    if (!currentYear) {
      container.innerHTML = `
        <div class="text-center py-8">
          <div class="text-gray-400 mb-4">
            ${SRDesignSystem.components.icon('fas fa-calendar-times', '4xl', 'gray-400')}
          </div>
          <h3 class="${SRDesignSystem.responsive.text.lg} font-medium text-gray-900 mb-2">No Active Academic Year</h3>
          <p class="${SRDesignSystem.responsive.text.base} text-gray-500 mb-4">There is no active academic year configured in the system.</p>
        </div>
      `;
      return;
    }

    const startDate = new Date(currentYear.start_date).toLocaleDateString();
    const endDate = new Date(currentYear.end_date).toLocaleDateString();

    // Add edit button
    const editButtonContainer = document.getElementById('edit-year-button-container');
    if (editButtonContainer) {
      editButtonContainer.innerHTML = `
        ${SRDesignSystem.forms.button('edit-academic-year', 'Edit Year Dates', 'secondary', {
          icon: 'fas fa-edit',
          onclick: `AcademicYearManagementComponent.editAcademicYear(${currentYear.id})`
        })}
      `;
    }

    container.innerHTML = `
      <div class="${SRDesignSystem.responsive.grid.cols3} ${SRDesignSystem.responsive.grid.gap}">
        <div class="bg-blue-50 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-calendar-alt', 'lg', 'blue-600')}
            </div>
            <div class="ml-3">
              <p class="${SRDesignSystem.responsive.text.xs} font-medium text-blue-900">Academic Year</p>
              <p class="${SRDesignSystem.responsive.text.base} font-semibold text-blue-700">${currentYear.name}</p>
            </div>
          </div>
        </div>

        <div class="bg-green-50 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-play-circle', 'lg', 'green-600')}
            </div>
            <div class="ml-3">
              <p class="${SRDesignSystem.responsive.text.xs} font-medium text-green-900">Start Date</p>
              <p class="${SRDesignSystem.responsive.text.base} font-semibold text-green-700">${startDate}</p>
            </div>
          </div>
        </div>

        <div class="bg-red-50 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              ${SRDesignSystem.components.icon('fas fa-stop-circle', 'lg', 'red-600')}
            </div>
            <div class="ml-3">
              <p class="${SRDesignSystem.responsive.text.xs} font-medium text-red-900">End Date</p>
              <p class="${SRDesignSystem.responsive.text.base} font-semibold text-red-700">${endDate}</p>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Populate terms management section
  populateTermsManagement() {
    console.log('🔄 Populating terms management...');
    const container = document.getElementById('terms-management-content');
    if (!container) {
      console.error('❌ Terms management container not found');
      return;
    }

    const terms = AcademicYearManagementComponents.state.terms;
    const currentYear = AcademicYearManagementComponents.state.currentAcademicYear;
    console.log('📋 Terms data:', terms, 'Current year:', currentYear);

    if (!currentYear) {
      container.innerHTML = `
        <div class="text-center py-8">
          <div class="text-gray-400 mb-4">
            ${SRDesignSystem.components.icon('fas fa-calendar-times', '4xl', 'gray-400')}
          </div>
          <h3 class="${SRDesignSystem.responsive.text.lg} font-medium text-gray-900 mb-2">No Academic Year Available</h3>
          <p class="${SRDesignSystem.responsive.text.base} text-gray-500">Cannot manage terms without an active academic year.</p>
        </div>
      `;
      return;
    }

    if (!terms || terms.length === 0) {
      container.innerHTML = `
        <div class="text-center py-8">
          <div class="text-gray-400 mb-4">
            ${SRDesignSystem.components.icon('fas fa-calendar-plus', '4xl', 'gray-400')}
          </div>
          <h3 class="${SRDesignSystem.responsive.text.lg} font-medium text-gray-900 mb-2">No Terms Found</h3>
          <p class="${SRDesignSystem.responsive.text.base} text-gray-500 mb-4">No terms are configured for the current academic year.</p>
          <p class="${SRDesignSystem.responsive.text.sm} text-gray-400">Terms should be created during academic year setup.</p>
        </div>
      `;
      return;
    }

    // Sort terms by number
    const sortedTerms = [...terms].sort((a, b) => a.number - b.number);

    container.innerHTML = `
      <div class="space-y-4">
        ${sortedTerms.map(term => this.renderTermCard(term)).join('')}
      </div>
    `;
  },

  // Render individual term card
  renderTermCard(term) {
    const startDate = new Date(term.start_date).toLocaleDateString();
    const endDate = new Date(term.end_date).toLocaleDateString();
    const isActive = term.is_active;

    return `
      <div class="border border-gray-200 rounded-lg ${SRDesignSystem.responsive.spacing.padding} ${isActive ? 'ring-2 ring-blue-500 bg-blue-50' : 'bg-white'}">
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <div class="flex items-center space-x-3 mb-3">
              <h4 class="${SRDesignSystem.responsive.text.lg} font-semibold text-gray-900">${term.name}</h4>
              ${isActive ?
                `<div class="flex items-center">
                  ${SRDesignSystem.components.icon('fas fa-check-circle', 'sm', 'green-600')}
                  <span class="ml-1">${SRDesignSystem.components.badge('Active Term', 'success')}</span>
                </div>` :
                SRDesignSystem.components.badge('Inactive', 'gray')
              }
            </div>

            <div class="${SRDesignSystem.responsive.grid.cols2} ${SRDesignSystem.responsive.grid.gapSm}">
              <div>
                <p class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-700">Start Date</p>
                <p class="${SRDesignSystem.responsive.text.sm} text-gray-600">${startDate}</p>
              </div>
              <div>
                <p class="${SRDesignSystem.responsive.text.sm} font-medium text-gray-700">End Date</p>
                <p class="${SRDesignSystem.responsive.text.sm} text-gray-600">${endDate}</p>
              </div>
            </div>
          </div>

          <div class="flex items-center space-x-2">
            ${SRDesignSystem.forms.button(`edit-term-${term.id}`, 'Edit Dates', 'secondary', {
              icon: 'fas fa-edit',
              onclick: `AcademicYearManagementComponent.editTerm(${term.id})`
            })}
            ${!isActive ? SRDesignSystem.forms.button(`activate-term-${term.id}`, 'Activate', 'primary', {
              icon: 'fas fa-play',
              onclick: `AcademicYearManagementComponent.activateTerm(${term.id})`
            }) : ''}
          </div>
        </div>
      </div>
    `;
  },

  // Edit term dates
  editTerm(termId) {
    // Ensure termId is a number for consistent comparison
    const numericTermId = parseInt(termId);
    const term = AcademicYearManagementComponents.state.terms.find(t => t.id === numericTermId);
    if (!term) {
      console.error('Term not found for editing. Available terms:', AcademicYearManagementComponents.state.terms);
      console.error('Looking for termId:', termId, 'converted to:', numericTermId);
      return;
    }

    AcademicYearManagementComponents.state.editingTerm = term;
    this.showEditTermModal(term);
  },

  // Show edit term modal
  showEditTermModal(term) {
    // Format dates for HTML date inputs (yyyy-MM-dd)
    const formatDateForInput = (dateString) => {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    };

    // Get academic year date constraints
    const academicYear = AcademicYearManagementComponents.state.currentAcademicYear;
    const minDate = academicYear ? formatDateForInput(academicYear.start_date) : '';
    const maxDate = academicYear ? formatDateForInput(academicYear.end_date) : '';

    const modalHtml = `
      <div id="edit-term-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 max-w-md shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-medium text-gray-900">Edit ${term.name} Dates</h3>
              <button onclick="AcademicYearManagementComponent.closeEditTermModal()"
                      class="text-gray-400 hover:text-gray-600 p-1">
                ${SRDesignSystem.components.icon('fas fa-times', 'base', 'current')}
              </button>
            </div>

            <form id="edit-term-form" class="space-y-4">
              <input type="hidden" id="edit-term-id" name="edit-term-id" value="${term.id}">

              <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    ${SRDesignSystem.components.icon('fas fa-info-circle', 'base', 'blue-400')}
                  </div>
                  <div class="ml-3">
                    <p class="${SRDesignSystem.responsive.text.xs} text-blue-600 mt-1">Term dates must fall within this period with at least 7 days between terms.</p>
                  </div>
                </div>
              </div>

              ${SRDesignSystem.forms.input('start_date', 'Start Date', formatDateForInput(term.start_date), {
                type: 'date',
                required: true,
                min: minDate,
                max: maxDate
              })}

              ${SRDesignSystem.forms.input('end_date', 'End Date', formatDateForInput(term.end_date), {
                type: 'date',
                required: true,
                min: minDate,
                max: maxDate
              })}

              <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                ${SRDesignSystem.forms.button('cancel-edit', 'Cancel', 'secondary', {
                  type: 'button',
                  onclick: 'AcademicYearManagementComponent.closeEditTermModal()'
                })}
                ${SRDesignSystem.forms.button('save-term', 'Save Changes', 'primary', {
                  type: 'submit'
                })}
              </div>
            </form>
          </div>
        </div>
      </div>
    `;

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Add form event listener
    const form = document.getElementById('edit-term-form');
    if (form) {
      form.addEventListener('submit', this.handleEditTermSubmit.bind(this));
    }
  },

  // Close edit term modal
  closeEditTermModal() {
    const modal = document.getElementById('edit-term-modal');
    if (modal) {
      modal.remove();
    }
    AcademicYearManagementComponents.state.editingTerm = null;
  },

  // Handle edit term form submission
  async handleEditTermSubmit(event) {
    event.preventDefault();

    try {
      const formData = new FormData(event.target);
      const termId = formData.get('edit-term-id');
      const startDate = formData.get('start_date');
      const endDate = formData.get('end_date');

      // Validate dates
      if (new Date(startDate) >= new Date(endDate)) {
        AcademicYearManagementComponents.showError('End date must be after start date');
        return;
      }

      // Validate holiday periods between terms
      const validationResult = this.validateTermDatesWithHolidays(parseInt(termId), startDate, endDate);
      if (!validationResult.valid) {
        AcademicYearManagementComponents.showError(validationResult.message);
        return;
      }

      // Get current term data (convert termId to number for comparison)
      const currentTerm = AcademicYearManagementComponents.state.terms.find(t => t.id == parseInt(termId));
      if (!currentTerm) {
        console.error('Term not found. Available terms:', AcademicYearManagementComponents.state.terms);
        console.error('Looking for termId:', termId, 'type:', typeof termId);
        AcademicYearManagementComponents.showError('Term not found');
        return;
      }

      // Update term
      const updateData = {
        name: currentTerm.name,
        number: currentTerm.number,
        academic_year_id: currentTerm.academic_year_id,
        start_date: startDate,
        end_date: endDate,
        is_active: currentTerm.is_active
      };

      const response = await window.TermsAPI.update(termId, updateData);

      if (response && response.success) {
        AcademicYearManagementComponents.showSuccess('Term dates updated successfully');
        this.closeEditTermModal();

        // Refresh global academic context
        if (window.AcademicContext) {
          await window.AcademicContext.refresh();
        }

        // Reload data and update UI
        await AcademicYearManagementComponents.loadInitialData();
        this.populateCurrentYearInfo();
        this.populateTermsManagement();
      } else {
        AcademicYearManagementComponents.showError(response?.message || 'Failed to update term dates');
      }

    } catch (error) {
      AcademicYearManagementComponents.showError('Failed to update term dates');
    }
  },

  // Validate term dates to ensure holiday periods between terms
  validateTermDatesWithHolidays(editingTermId, startDate, endDate) {
    const terms = AcademicYearManagementComponents.state.terms;
    const editingTerm = terms.find(t => t.id === editingTermId);

    if (!editingTerm) {
      return { valid: false, message: 'Term not found for validation' };
    }

    const newStartDate = new Date(startDate);
    const newEndDate = new Date(endDate);
    const minHolidayDays = 7; // Minimum 7 days holiday between terms

    // Check against all other terms
    for (const term of terms) {
      if (term.id === editingTermId) continue; // Skip the term being edited

      const termStart = new Date(term.start_date);
      const termEnd = new Date(term.end_date);

      // Check if this term comes before the editing term
      if (term.number < editingTerm.number) {
        // This term should end at least minHolidayDays before the new start date
        const daysBetween = Math.floor((newStartDate - termEnd) / (1000 * 60 * 60 * 24));
        if (daysBetween < minHolidayDays) {
          return {
            valid: false,
            message: `${editingTerm.name} must start at least ${minHolidayDays} days after ${term.name} ends. Currently only ${daysBetween} days apart.`
          };
        }
      }

      // Check if this term comes after the editing term
      if (term.number > editingTerm.number) {
        // The new end date should be at least minHolidayDays before this term starts
        const daysBetween = Math.floor((termStart - newEndDate) / (1000 * 60 * 60 * 24));
        if (daysBetween < minHolidayDays) {
          return {
            valid: false,
            message: `${editingTerm.name} must end at least ${minHolidayDays} days before ${term.name} starts. Currently only ${daysBetween} days apart.`
          };
        }
      }

      // Check for any overlap
      if ((newStartDate >= termStart && newStartDate <= termEnd) ||
          (newEndDate >= termStart && newEndDate <= termEnd) ||
          (newStartDate <= termStart && newEndDate >= termEnd)) {
        return {
          valid: false,
          message: `${editingTerm.name} dates overlap with ${term.name}. Terms cannot overlap.`
        };
      }
    }

    // Validate against academic year dates
    const academicYear = AcademicYearManagementComponents.state.currentAcademicYear;
    if (academicYear) {
      const yearStart = new Date(academicYear.start_date);
      const yearEnd = new Date(academicYear.end_date);

      if (newStartDate < yearStart || newEndDate > yearEnd) {
        return {
          valid: false,
          message: `Term dates must fall within the academic year period (${yearStart.toLocaleDateString()} - ${yearEnd.toLocaleDateString()}).`
        };
      }
    }

    return { valid: true };
  },

  // Edit academic year dates
  editAcademicYear(yearId) {
    const year = AcademicYearManagementComponents.state.currentAcademicYear;
    if (!year || year.id !== yearId) {
      console.error('Academic year not found for editing');
      return;
    }

    this.showEditAcademicYearModal(year);
  },

  // Show edit academic year modal
  showEditAcademicYearModal(year) {
    // Format dates for HTML date inputs (yyyy-MM-dd)
    const formatDateForInput = (dateString) => {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    };

    // Set reasonable date constraints for academic year (within the year itself)
    const currentYear = parseInt(year.name);
    const minDate = `${currentYear}-01-01`; // January 1st of the academic year
    const maxDate = `${currentYear}-12-31`; // December 31st of the academic year

    const modalHtml = `
      <div id="edit-year-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
              <h3 class="${SRDesignSystem.responsive.text.lg} font-medium text-gray-900">Edit Academic Year Dates</h3>
              <button onclick="AcademicYearManagementComponent.closeEditAcademicYearModal()"
                      class="text-gray-400 hover:text-gray-600">
                ${SRDesignSystem.components.icon('fas fa-times', 'base', 'current')}
              </button>
            </div>

            <form id="edit-year-form" class="space-y-4">
              <input type="hidden" id="edit-year-id" name="edit-year-id" value="${year.id}">

              <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    ${SRDesignSystem.components.icon('fas fa-info-circle', 'base', 'blue-400')}
                  </div>
                  <div class="ml-3">
                    <p class="${SRDesignSystem.responsive.text.sm} text-blue-700">
                      <strong>Academic Year ${year.name}:</strong> All term dates must fall within the academic year period.
                    </p>
                  </div>
                </div>
              </div>

              ${SRDesignSystem.forms.input('start_date', 'Start Date', formatDateForInput(year.start_date), {
                type: 'date',
                required: true,
                min: minDate,
                max: maxDate
              })}

              ${SRDesignSystem.forms.input('end_date', 'End Date', formatDateForInput(year.end_date), {
                type: 'date',
                required: true,
                min: minDate,
                max: maxDate
              })}

              <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div class="flex">
                  <div class="flex-shrink-0">
                    ${SRDesignSystem.components.icon('fas fa-exclamation-triangle', 'base', 'yellow-400')}
                  </div>
                  <div class="ml-3">
                    <p class="${SRDesignSystem.responsive.text.sm} text-yellow-700">
                      <strong>Note:</strong> Changing academic year dates may affect term dates.
                      Please ensure term dates fall within the academic year period.
                    </p>
                  </div>
                </div>
              </div>

              <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                ${SRDesignSystem.forms.button('cancel-edit-year', 'Cancel', 'secondary', {
                  type: 'button',
                  onclick: 'AcademicYearManagementComponent.closeEditAcademicYearModal()'
                })}
                ${SRDesignSystem.forms.button('save-year', 'Save', 'primary', {
                  type: 'submit'
                })}
              </div>
            </form>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Add form event listener
    const form = document.getElementById('edit-year-form');
    if (form) {
      form.addEventListener('submit', this.handleEditAcademicYearSubmit.bind(this));
    }
  },

  // Close edit academic year modal
  closeEditAcademicYearModal() {
    const modal = document.getElementById('edit-year-modal');
    if (modal) {
      modal.remove();
    }
  },

  // Handle edit academic year form submission
  async handleEditAcademicYearSubmit(event) {
    event.preventDefault();

    try {
      const formData = new FormData(event.target);
      const yearId = formData.get('edit-year-id');
      const startDate = formData.get('start_date');
      const endDate = formData.get('end_date');

      // Validate dates
      if (new Date(startDate) >= new Date(endDate)) {
        AcademicYearManagementComponents.showError('Academic year end date must be after start date');
        return;
      }

      // Get current year data
      const currentYear = AcademicYearManagementComponents.state.currentAcademicYear;
      if (!currentYear || currentYear.id != parseInt(yearId)) {
        AcademicYearManagementComponents.showError('Academic year not found');
        return;
      }

      // Update academic year
      const updateData = {
        name: currentYear.name,
        start_date: startDate,
        end_date: endDate,
        is_active: currentYear.is_active
      };

      if (window.SRDesignSystem && window.SRDesignSystem.forms) {
        window.SRDesignSystem.forms.setButtonLoading('save-year', true);
      }

      const response = await window.AcademicYearsAPI.update(yearId, updateData);

      if (response && response.success) {
        AcademicYearManagementComponents.showSuccess('Academic year dates updated successfully');
        this.closeEditAcademicYearModal();

        // Refresh global academic context
        if (window.AcademicContext) {
          await window.AcademicContext.refresh();
        }

        // Reload data and update UI
        await AcademicYearManagementComponents.loadInitialData();
        this.populateCurrentYearInfo();
        this.populateTermsManagement();
      } else {
        AcademicYearManagementComponents.showError(response?.message || 'Failed to update academic year dates');
      }

    } catch (error) {
      console.error('Error updating academic year:', error);
      AcademicYearManagementComponents.showError('Failed to update academic year dates');
    } finally {
      if (window.SRDesignSystem && window.SRDesignSystem.forms) {
        window.SRDesignSystem.forms.setButtonLoading('save-year', false);
      }
    }
  },

  // Activate term
  async activateTerm(termId) {
    try {
      // Show confirmation dialog
      const confirmed = confirm(
        'Activating this term will automatically promote ALL students in the current academic year to the new term. ' +
        'Students will keep their current class and stream assignments. Do you want to continue?'
      );

      if (!confirmed) {
        return;
      }

      const response = await window.TermsAPI.activate(termId);

      if (response && response.success) {
        // Automatically promote all students to the new term
        const promotionResult = await this.promoteAllStudentsToNewTerm(termId);

        AcademicYearManagementComponents.showSuccess(
          `Term activated successfully! ${promotionResult.promoted_count || 0} students promoted to new term.`
        );

        // Refresh global academic context
        if (window.AcademicContext) {
          await window.AcademicContext.refresh();
        }

        // Reload data and update UI
        await AcademicYearManagementComponents.loadInitialData();
        this.populateCurrentYearInfo();
        this.populateTermsManagement();
      } else {
        AcademicYearManagementComponents.showError(response?.message || 'Failed to activate term');
      }

    } catch (error) {
      console.error('Error activating term:', error);
      AcademicYearManagementComponents.showError('Failed to activate term');
    }
  },

  // Automatically promote all students to new term
  async promoteAllStudentsToNewTerm(newTermId) {
    try {
      // Get current academic year
      const activeYear = window.AcademicContext.getActiveAcademicYear();
      if (!activeYear) {
        throw new Error('No active academic year found');
      }

      // Call the backend API to automatically promote all students
      const apiUrl = window.SRConfig.getApiUrl('/enrollments/auto-promote-term');
      const token = localStorage.getItem('smartreport_token');

      const result = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          academic_year_id: activeYear.id,
          new_term_id: newTermId
        })
      });

      const data = await result.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to promote students');
      }

      console.log(`✅ Automatically promoted ${data.promoted_count || 0} students to new term`);
      return data;

    } catch (error) {
      console.error('Error promoting students to new term:', error);
      throw error;
    }
  },

  // Initialize event listeners
  initializeEventListeners() {
    // No additional event listeners needed for this component
    // All interactions are handled through onclick attributes
  }
};

// Export for global access
window.AcademicYearManagementComponent = AcademicYearManagementComponent;
window.AcademicYearManagementComponents = AcademicYearManagementComponents;