const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Get all school settings
router.get('/school', authenticateToken, async (req, res) => {
  try {
    const query = `
      SELECT 
        id,
        setting_key,
        setting_value,
        setting_type,
        category,
        description,
        is_editable,
        created_at,
        updated_at
      FROM school_settings 
      ORDER BY category, setting_key
    `;
    
    const result = await executeQuery(query);
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to fetch school settings',
        error: result.error
      });
    }
  } catch (error) {
    console.error('Get school settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Get school settings by category
router.get('/school/category/:category', authenticateToken, async (req, res) => {
  try {
    const { category } = req.params;
    
    const query = `
      SELECT 
        id,
        setting_key,
        setting_value,
        setting_type,
        category,
        description,
        is_editable,
        created_at,
        updated_at
      FROM school_settings 
      WHERE category = ?
      ORDER BY setting_key
    `;
    
    const result = await executeQuery(query, [category]);
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to fetch school settings by category',
        error: result.error
      });
    }
  } catch (error) {
    console.error('Get school settings by category error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Get school setting by key
router.get('/school/key/:key', authenticateToken, async (req, res) => {
  try {
    const { key } = req.params;
    
    const query = `
      SELECT 
        id,
        setting_key,
        setting_value,
        setting_type,
        category,
        description,
        is_editable,
        created_at,
        updated_at
      FROM school_settings 
      WHERE setting_key = ?
    `;
    
    const result = await executeQuery(query, [key]);
    
    if (result.success) {
      if (result.data.length > 0) {
        res.json({
          success: true,
          data: result.data[0]
        });
      } else {
        res.status(404).json({
          success: false,
          message: 'Setting not found'
        });
      }
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to fetch school setting',
        error: result.error
      });
    }
  } catch (error) {
    console.error('Get school setting by key error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Update school setting by key
router.put('/school/key/:key', authenticateToken, async (req, res) => {
  try {
    const { key } = req.params;
    const { value } = req.body;
    
    if (value === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Setting value is required'
      });
    }
    
    // First check if setting exists and is editable
    const checkQuery = `
      SELECT id, is_editable 
      FROM school_settings 
      WHERE setting_key = ?
    `;
    
    const checkResult = await executeQuery(checkQuery, [key]);
    
    if (!checkResult.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to check setting',
        error: checkResult.error
      });
    }
    
    if (checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Setting not found'
      });
    }
    
    if (!checkResult.data[0].is_editable) {
      return res.status(403).json({
        success: false,
        message: 'This setting is not editable'
      });
    }
    
    // Update the setting
    const updateQuery = `
      UPDATE school_settings 
      SET setting_value = ?, updated_at = CURRENT_TIMESTAMP
      WHERE setting_key = ?
    `;
    
    const result = await executeQuery(updateQuery, [value, key]);
    
    if (result.success) {
      res.json({
        success: true,
        message: 'Setting updated successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to update setting',
        error: result.error
      });
    }
  } catch (error) {
    console.error('Update school setting error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Update multiple school settings (bulk update)
router.put('/school/bulk', authenticateToken, async (req, res) => {
  try {
    const { settings } = req.body;

    if (!settings || !Array.isArray(settings)) {
      return res.status(400).json({
        success: false,
        message: 'Settings array is required'
      });
    }

    // Validate all settings first
    for (const setting of settings) {
      if (!setting.key || setting.value === undefined) {
        return res.status(400).json({
          success: false,
          message: 'Each setting must have key and value'
        });
      }
    }

    // Check existing settings for editability (only for settings that already exist)
    const keys = settings.map(s => s.key);
    const placeholders = keys.map(() => '?').join(',');
    const checkQuery = `
      SELECT setting_key, is_editable
      FROM school_settings
      WHERE setting_key IN (${placeholders})
    `;

    const checkResult = await executeQuery(checkQuery, keys);

    if (!checkResult.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to validate settings',
        error: checkResult.error
      });
    }

    const existingSettings = checkResult.data;
    const nonEditableSettings = existingSettings.filter(s => !s.is_editable);

    if (nonEditableSettings.length > 0) {
      return res.status(403).json({
        success: false,
        message: `The following settings are not editable: ${nonEditableSettings.map(s => s.setting_key).join(', ')}`
      });
    }

    // Insert or update all settings using INSERT ... ON DUPLICATE KEY UPDATE
    const upsertPromises = settings.map(setting => {
      const upsertQuery = `
        INSERT INTO school_settings (setting_key, setting_value, setting_type, category, description, is_editable, created_at, updated_at)
        VALUES (?, ?, 'string', 'school_info', ?, TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON DUPLICATE KEY UPDATE
        setting_value = VALUES(setting_value),
        updated_at = CURRENT_TIMESTAMP
      `;

      // Generate description based on setting key
      const description = setting.key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

      return executeQuery(upsertQuery, [setting.key, setting.value, description]);
    });

    const results = await Promise.all(upsertPromises);
    const failedUpdates = results.filter(r => !r.success);

    if (failedUpdates.length > 0) {
      return res.status(500).json({
        success: false,
        message: 'Some settings failed to save',
        errors: failedUpdates.map(r => r.error)
      });
    }

    res.json({
      success: true,
      message: `Successfully saved ${settings.length} settings`
    });

  } catch (error) {
    console.error('Bulk update school settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Get all setting categories
router.get('/school/categories', authenticateToken, async (req, res) => {
  try {
    const query = `
      SELECT DISTINCT category
      FROM school_settings
      ORDER BY category
    `;

    const result = await executeQuery(query);

    if (result.success) {
      const categories = result.data.map(row => row.category);
      res.json({
        success: true,
        data: categories
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to fetch setting categories',
        error: result.error
      });
    }
  } catch (error) {
    console.error('Get setting categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

module.exports = router;
